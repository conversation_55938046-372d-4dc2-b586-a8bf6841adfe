import copy
import logging
import os
import re
from collections import Counter, OrderedDict, defaultdict, named<PERSON><PERSON>
from copy import deepcopy
from difflib import SequenceMatcher
from functools import cached_property, lru_cache
from itertools import chain, groupby
from operator import itemgetter
from typing import Callable, Iterable, List, Pattern, Type, TypeVar, Union

from msgspec import Struct
from pdfparser.pdftools.interdoc import Interdoc

from remarkable.common.common import (
    get_keys,
    is_para_elt,
    is_sub_sequence,
    is_table_elt,
    page_merged_table_indices,
    roman_to_int,
)
from remarkable.common.common_pattern import (
    DIRECTORY_START_PATTERN,
    P_ALL_CHAPTER_TITLES,
    P_CONTENTS,
    P_CONTINUED,
    P_CONTINUED_WITH_COLON,
    P_NON_CH_EN,
    PAGE_PATTERN,
    R_CI_CHAPTER_TITLES,
    R_CONTINUED,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import PDFInsight<PERSON>lassEnum
from remarkable.common.element_util import (
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    add_space_to_element,
    box_left,
    box_right,
    classify_by_page,
    element_text,
    get_page_column,
    get_page_para_outline,
    is_end_line,
    is_invalid_syllabus,
    is_multi_line,
    is_valid_first_ele_in_page,
    max_col_of_tbl_element,
)
from remarkable.common.exceptions import PdfInsightNotFound
from remarkable.common.pattern import PatternCollection
from remarkable.common.util import (
    P_CHNS,
    clean_txt,
    has_chinese_chars,
    has_english_chars,
    is_aim_element,
    is_in_range,
    normalize_outline,
    pairwise,
    read_pdfinsight_file,
    split_chars,
)
from remarkable.pdfinsight import clear_syl_title
from remarkable.pdfinsight.chapter_fixer import fix_chapters
from remarkable.pdfinsight.reader_util import (
    R_TABLE_UNITS,
    clean_title,
    fill_middle_page_title,
    find_real_syllabus,
    get_contents,
    get_first_row_texts_of_table,
    is_merged_elt,
    is_same_title,
    is_title_in_page,
    page_first_title,
)
from remarkable.predictor.common_pattern import R_ANY_EN_DATE

Index = namedtuple("Index", ["idx", "page", "index", "outline", "data"])
Index.__new__.__defaults__ = (0, 0, 0, [0.0, 0.0, 0.0, 0.0], None)
T = TypeVar("T")

logger = logging.getLogger(__name__)


PDFINSIGHT_CLASS_MAPPING = {
    "syllabuses": "SYLLABUS",
    "paragraphs": "PARAGRAPH",
    "tables": "TABLE",
    "page_headers": "PAGE_HEADER",
    "page_footers": "PAGE_FOOTER",
    "shapes": "SHAPE",
    "images": "IMAGE",
    "footnotes": "FOOTNOTE",
    "infographics": "INFOGRAPHIC",
    "figures": "FIGURE",
    "captions": "CAPTION",
}

P_FOLLOW_CHAPTER_PREFIX = re.compile(
    r"^(?P<prefix>[(（])?((?P<word>[a-z])|(?P<roman>[ivx]+)|(?P<num>\d+))(?P<suffix>[ .)）])", re.I
)
P_IGNORE_FIX_CHAPTER = PatternCollection(R_CI_CHAPTER_TITLES, flags=re.I)
P_WORD_NUM = re.compile(r"\w")
P_TBL_UNIT = PatternCollection(R_TABLE_UNITS, flags=re.I)
P_INVALID_PAGE_FIRST_PARA = PatternCollection(
    [
        rf"^[{R_MIDDLE_DASHES}\d\s|]*ANNUAL\s*REPORT",
        r"ANNUAL\s*REPORT[\d\s]*$",
        rf"^for\s*the\s*year\s*ended\s*{R_ANY_EN_DATE}$",
    ],
    flags=re.I,
)
# http://************:55647/#/project/remark/407004?treeId=3980&fileId=113308&schemaId=18&projectId=17&schemaKey=C2.3&page=37, index=626
P_INVALID_PARA = PatternCollection(
    [
        r"^\|\w{,20}$",
        r"^\|(\w\s+){,20}$",
    ],
    flags=re.I,
)
# 续表的正则，主要针对财务报表
P_CONTINUE_TABLE = PatternCollection(
    [
        # 表（续）
        rf"表{R_CONTINUED}",
        # 资产负债表（续）
        rf"(consolidated|statement).*\b(financial\s*position|balance).*{R_CONTINUED}",
        # 利润表
        rf"(consolidated|statement).*\b(income|equity).*{R_CONTINUED}",
        # 现金流量表
        rf"(consolidated|statement).*\bcash\s*flows.*{R_CONTINUED}",
        # 损益表
        rf"(consolidated|statement).*\bprofit\s*or\s*loss.*{R_CONTINUED}",
    ],
    re.I,
)


def _pretreat(data):
    # https://mm.paodingai.com/cheftin/pl/oia5cdfogtd1ty6ncjbjycznzr
    data = Interdoc.restore_page_merged_table(data)
    data = gen_element_set(data)
    return data


def gen_element_set(data):
    for group_name, class_name in PDFINSIGHT_CLASS_MAPPING.items():
        for item in data.get(group_name, []):
            if "class" not in item:
                item["class"] = class_name
    items = {}
    index_keys = list(PDFINSIGHT_CLASS_MAPPING.keys())
    index_keys.remove("syllabuses")
    for cate in index_keys:
        for idx, elt in enumerate(data.get(cate, [])):
            items[elt.get("index")] = Index(idx, elt.get("page"), elt.get("index"), elt.get("outline"), elt)
    data["_index"] = items
    return data


class Singleton:
    _instance = None
    _inited = False

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(Singleton, cls).__new__(cls)
        return cls._instance

    def _pop(self):
        pass


class InterDoc(Struct):
    id: Union[str, int]
    name: str = ""
    path: str = ""
    styles: dict = {}
    columns: dict = {}
    pages: dict = {}
    shapes: List[dict] = []
    images: List[dict] = []
    model_version: dict = {}
    thin: bool = False
    combo_tables: List[dict] = []
    use_combo_tables: bool = False
    tags: List[str] = []
    embedded_syllabuses: List[dict] = []
    syllabuses: List[dict] = []
    tables: List[dict] = []
    paragraphs: List[dict] = []
    page_headers: List[dict] = []
    page_footers: List[dict] = []
    _index: dict = {}

    def __getitem__(self, key):
        return getattr(self, key, [] if key.endswith("s") else None)

    def __setitem__(self, key, value):
        setattr(self, key, value)

    def get(self, key, default=None):
        return self[key] or default


class Cacher(Singleton):
    def __init__(self, size=0):
        if not self._inited:
            self.size = size
            self.keys = []
            self.data = {}
            self._inited = True

    def __reinit__(self):
        self.keys = []
        self.data = {}

    def get(self, zip_path, msgspec_type: Type[T] | None = None):
        if not os.path.isfile(zip_path):
            raise PdfInsightNotFound("file {} not exists".format(zip_path))

        if zip_path not in self.data:
            # NOTE: 大文件没有期望中的快, 先不启用
            # https://pythonspeed.com/articles/faster-python-json-parsing/
            data = read_pdfinsight_file(zip_path, msgspec_type=msgspec_type)
            _pretreat(data)
            if self.size > 0:
                self.data[zip_path] = data
                self._push(zip_path)
        else:
            data = self.data[zip_path]
            self._push(zip_path)

        return data

    def close(self, zip_path):
        if not zip_path:
            self.__reinit__()

        if not os.path.isfile(zip_path):
            return

        if zip_path in self.keys and zip_path in self.data:
            self.keys.remove(zip_path)
            self.data.pop(zip_path, None)

    def _push(self, key):
        if key in self.keys:
            self.keys.remove(key)

        self.keys.insert(0, key)
        while len(self.keys) > self.size:
            key_ = self.keys.pop()
            self.data.pop(key_, None)


class PdfinsightReaderBase:
    def __init__(
        self, path, cachesize=0, data=None, *, msgspec_type: Type[T] | None = None, pdf_contents: list[dict] = None
    ):
        self.path = path
        self.cacher = Cacher(size=cachesize)
        self.data = self.cacher.get(path, msgspec_type) if not data else _pretreat(data)
        self.pdf_contents = pdf_contents or []

    @classmethod
    @lru_cache(maxsize=1)
    def from_path(cls, path, *, pdf_hash: str | None = None):
        if pdf_hash is not None:
            from remarkable.models.doc_cache import DocCache

            return cls(path, pdf_contents=DocCache.get_or_create_outline(pdf_hash))
        return cls(path)

    # @property
    # def pdfinsight_hash(self):
    #     return "".join(self.path.split(os.sep)[-2:])

    @cached_property
    def element_dict(self):
        return {idx: item.data for idx, item in self.data["_index"].items()}

    @cached_property
    def page_element_dict(self) -> dict[int, list[Index]]:
        # merged_indices = []
        result_dict = {}
        for _, elt in sorted(self.data["_index"].items(), key=lambda x: x[0]):
            # if (
            #     elt.data.get("page_merged_paragraph")
            #     and elt.index == elt.data["page_merged_paragraph"]["paragraph_indices"][0]
            # ):
            #     merged_indices.append(elt.index)
            if elt.page in result_dict:
                result_dict[elt.page].append(elt)
            else:
                result_dict[elt.page] = [elt]
        # logger.debug(f"page_merged_paragraphs: {merged_indices}")
        return result_dict

    @cached_property
    def max_index(self):
        return max(self.element_dict)

    @cached_property
    def max_page(self):
        return max(self.page_element_dict)

    def __getattr__(self, name):
        """
        id, name, path,
        syllabuses, paragraphs, tables,
        page_headers, page_footers
        """
        return self.data.get(name, [] if name.endswith("s") else None)


class MergedTable:
    def __init__(self, tables):
        self.tables = tables or []
        self._cells = None
        self.column_count = self.get_table_col_size(tables[0])

    @cached_property
    def index(self):
        return self.tables[0].get("index", 0)

    @cached_property
    def row_count(self):
        return max(int(idx.split("_")[0]) for idx in self.cells) + 1

    @cached_property
    def cells_idx(self):
        return defaultdict(dict)

    @cached_property
    def merged_table_indices(self):
        return [t["index"] for t in self.tables]

    @cached_property
    def continue_table_cells(self):
        # 存储续表的merged_cells
        if len(self.tables) < 2:
            return {}
        return {tbl["index"]: self.fix_merged_cells(tbl, 0) for tbl in self.tables[1:]}

    @cached_property
    def cells(self):
        if self._cells:
            return self._cells
        row_idx = 0
        cells = {}
        row_continued = False
        for tbl in self.tables:
            # todo 存在问题： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_750534
            if row_continued and tbl["cells"].get("0_0") and tbl["cells"]["0_0"]["text"] == cells["0_0"]["text"]:
                row_idx -= 1
            tbl_row_begin = self.get_table_row_begin(tbl)
            row_idx -= tbl_row_begin
            merged_cells = self.fix_merged_cells(tbl, row_idx)
            for idx, cell in merged_cells.items():
                row, col = [int(item) for item in idx.split("_")]
                # 合并跨页行（即同一行的数据横跨了两页）
                if row_continued and row == tbl_row_begin and cell["text"] == cells[f"0_{col}"]["text"]:
                    last_cell = cells.get(f"{row + row_idx}_{col}")
                    if last_cell:
                        last_cell["bottom"] = max(last_cell["bottom"], cell["bottom"])
                    continue
                row += row_idx
                merge_index = f"{row}_{col}"
                cells[merge_index] = cell
                self.cells_idx[str(tbl["index"])][merge_index] = idx
            row_idx += self.get_table_row_size(tbl)
            row_continued = tbl.get("row_continued")
        self._cells = cells
        return self._cells

    @classmethod
    def fix_merged_cells(cls, tbl, row_base):
        """
        填充被合并的单元格
        """
        cells = {}
        for idx, cell in tbl["cells"].items():
            row, col = map(int, idx.split("_"))
            row += row_base
            new_cell = copy.copy(cell)
            for key, value in [("left", col), ("right", col + 1), ("top", row), ("bottom", row + 1)]:
                new_cell.setdefault(key, value)
            cells[idx] = new_cell
        for merged in tbl["merged"]:
            cell = None
            max_row = row_base + max(row for row, _ in merged)
            max_col = max(col for row, col in merged)
            for row, col in merged:
                cell = cells.get("{}_{}".format(row, col))
                if cell is not None:
                    cell["right"] = max_col + 1
                    cell["bottom"] = max_row + 1
                    break
            if cell is not None:
                for row, col in merged:
                    key = "{}_{}".format(row, col)
                    if cells.get(key) is None:
                        dummy_cell = deepcopy(cell)
                        dummy_cell["dummy"] = True
                        cells[key] = dummy_cell
        return cells

    @classmethod
    def get_table_row_begin(cls, tbl):
        return min(int(idx.split("_")[0]) for idx in tbl["cells"])

    @classmethod
    def get_table_row_size(cls, tbl):
        return max(int(idx.split("_")[0]) for idx in tbl["cells"]) + 1

    @classmethod
    def get_table_col_size(cls, tbl):
        return max(int(idx.split("_")[1]) for idx in tbl["cells"]) + 1


class PdfinsightReader(PdfinsightReaderBase):
    @cached_property
    def syllabus_reader(self):
        return PdfinsightSyllabus(self.syllabuses)

    @cached_property
    def fixed_syllabuses(self):
        try:
            return fix_chapters(self, self.syllabuses) or self.syllabuses
        except Exception as e:
            logger.exception(e)
            return self.syllabuses

    @cached_property
    def syllabus_titles(self):
        """
        所有大写形式的章节标题
        """
        return {clean_txt(syll["title"]).upper() for syll in self.syllabuses}

    @cached_property
    def syllabus_dict(self):
        return {syl["index"]: syl for syl in self.syllabuses}

    @cached_property
    def fixed_element_dict(self):
        element_dict = {}
        for index, elt in sorted(self.element_dict.items(), key=lambda x: x[0]):
            element_dict[index] = self._fix_element(elt)
        return element_dict

    @cached_property
    def page_dict(self) -> dict[int, "PdfinsightPage"]:
        page_dict = {}
        for page in self.page_element_dict:
            page_dict[page] = PdfinsightPage(page=page, pdfinsight=self, is_catalog=self._is_catalog_page(page))
        return page_dict

    @cached_property
    def page_offset(self):
        """
        计算page索引相对实际页码的偏移量
        """
        offsets = []
        for page_index, page in self.page_dict.items():
            if page.page_in_content:
                offset = page_index - page.page_in_content
                if abs(offset) > 10:
                    continue
                offsets.append(offset)
        return Counter(offsets).most_common()[0][0] if len(offsets) > (len(self.page_dict) - 10) / 2 else None

    @cached_property
    def page_image_count(self):
        """
        统计每个页面图片个数
        """
        count = {}
        for page, page_data in self.page_element_dict.items():
            count[page] = len([elt for elt in page_data if elt.data["class"] == "IMAGE"])
        return count

    @cached_property
    def page_shape_count(self):
        """
        统计每个页面图表个数
        """
        count = {}
        for page, page_data in self.page_element_dict.items():
            count[page] = len([elt for elt in page_data if elt.data["class"] == "SHAPE"])
        return count

    @cached_property
    def page_columns(self):
        """
        获取每个页面的分栏列宽
        """
        page_columns = {}
        for page, page_info in self.pages.items():
            page_columns[int(page)] = get_keys(page_info, ["column", "grid", "columns"], []) + [page_info["size"][0]]
        return page_columns

    @cached_property
    def page_left_right(self):
        """ "
        计算每个页面中，每个分栏下文本元素块的最左侧和最右侧
        """
        left_right = defaultdict(dict)
        for page, page_data in self.page_element_dict.items():
            for idx_obj in page_data:
                element = idx_obj.data
                # 只根据表格+段落的outline判断边界，图片不做参考
                if "position" not in element:
                    continue
                if is_table_elt(element) and element.get("cells"):
                    # 取表格的第一列和最后一列的内容，来判断边界
                    max_col = max(int(idx.split("_")[1]) for idx in element["cells"])
                    all_chars = list(
                        chain.from_iterable(
                            c["chars"]
                            for k, c in element["cells"].items()
                            if int(k.split("_")[1]) in {0, max_col} and c.get("chars")
                        )
                    )
                else:
                    all_chars = element.get("chars")
                if not all_chars:
                    continue
                position = tuple(element["position"])
                if is_para_elt(element):
                    # 注意：必须用chars的font_box，不能用元素块的outline
                    left = min(box_left(char) for char in all_chars if char["text"])
                    right = max(box_right(char) for char in all_chars if char["text"])
                else:
                    left, _, right, _ = element["outline"]
                if left_right[page].get(position):
                    left_, right_ = left_right[page][position]
                    min_left, max_right = min(left, left_), max(right, right_)
                else:
                    min_left, max_right = left, right
                left_right[page][position] = (min_left, max_right)
        return left_right

    @cached_property
    def normal_font_size(self):
        """
        取中间页面10个单词个数超出100的元素块，取出出现最多的字号（正文字号）
        """
        elements = []
        for page, index_datas in self.page_element_dict.items():
            if page < 10:
                continue
            if len(elements) > 9:
                break
            for index_data in index_datas:
                element = index_data.data
                if (
                    is_para_elt(element)
                    and element["index"] not in self.syllabus_reader.elt_syllabus_dict
                    and element["text"].strip().endswith(".")
                    and len(element["text"].split()) > 100
                ):
                    elements.append(element)
        font_sizes = []
        for element in elements:
            for char in element["chars"]:
                if font_style := self.get_font_style([char]):
                    font_sizes.append(font_style.fontsize)
        if font_sizes:
            return Counter(font_sizes).most_common()[0][0]
        if self.max_page > 20:
            logger.warning("No normal text found after the 10th page.")
        return None

    def texts_before_table(self, tbl_element, need_prev_page_texts=False):
        # 考虑到隔页会有大标题，存储前一页的前三个元素块： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_751693
        page, tbl_index = tbl_element["page"], tbl_element["index"]
        prev_texts = []
        if need_prev_page_texts and (page_elements := [e.data for e in self.page_element_dict.get(page - 1, [])[:3]]):
            prev_texts = [
                clean_txt(e["text"], remove_cn_text=True)
                for e in page_elements
                if is_para_elt(e) and not is_multi_line(e)
            ]
        before_table_texts = []
        for index_elt in self.page_element_dict.get(page) or []:
            element = index_elt.data
            if element["index"] >= tbl_index:
                break
            # 同一页在当前table之前，还有一张表，则不继续提取texts_before_table
            if is_table_elt(element):
                break
            if not is_para_elt(element):
                continue
            text = clean_txt(element["text"], remove_cn_text=True)
            before_table_texts.append(text)
            if len(before_table_texts) > 5:
                # 元素块太多时，仅取页面前三个（为了排除每页的大章节标题等）
                # http://************:55647/#/project/remark/256015?projectId=17&treeId=4451&fileId=66449&schemaId=29&page=107 index=1561
                return before_table_texts[:3]
        return prev_texts + before_table_texts

    def texts_after_table(self, page, table_index=None):
        """
        找指定页面下指定表格或第一个表格到页脚之间的文本，超过3个则不取
        """
        after_table_texts = []
        found_table = False
        if page not in self.page_element_dict:
            return []
        for index_elt in self.page_element_dict.get(page) or []:
            element = index_elt.data
            if is_table_elt(element) and (not table_index or element["index"] == table_index):
                found_table = True
                continue
            if not found_table:
                continue
            if element["class"] == PDFInsightClassEnum.PAGE_FOOTER.value:
                break
            if not is_para_elt(element):
                return []
            text = clean_txt(element["text"], remove_cn_text=True)
            after_table_texts.append(text)
            if len(after_table_texts) > 3:
                return []
        return after_table_texts

    def is_continue_table_page(self, page):
        """
        页面的前3个元素块中包含续表的正则，说明当前页表格需要和上一页表格合并
        """
        page_elements = self.page_element_dict[page]
        if not page_elements:
            return False
        start_index = page_elements[0].data["index"]
        return any(
            P_CONTINUE_TABLE.nexts(e.data["text"])
            for e in page_elements
            if is_para_elt(e.data) and e.data["index"] < start_index + 3
        )

    @staticmethod
    def can_merge_table_element(element: dict, max_col: int, header_texts: list[str], strict=False):
        if not max_col:
            return False
        current_max_col = max_col_of_tbl_element(element)
        # reader中合并表格时，必须保证列数一致
        # http://************:55647/#/project/remark/295004?fileid=70851&projectId=17&treeId=45192&fileId=70851&schemaId=18&page=224 index=3915,3923
        if strict and max_col != current_max_col:
            return False
        if max_col < 2 and max_col != current_max_col or abs(max_col - current_max_col) > 1:
            return False
        if not header_texts:
            return False
        current_header_texts = get_first_row_texts_of_table(element)
        if header_texts == current_header_texts:
            return True
        if "".join(clean_txt(t, remove_blank=True, remove_cn_text=True) for t in header_texts) == "".join(
            clean_txt(t, remove_blank=True, remove_cn_text=True) for t in current_header_texts
        ):
            return True
        header_texts, current_header_texts = set(header_texts), set(current_header_texts)
        count_header = min(len(header_texts), len(current_header_texts))
        same_texts = header_texts & current_header_texts
        if len(same_texts) not in (count_header, count_header - 1):
            return False
        # 排除相同单元格后，剩余单元格拆成的每个单词都一致（识别错误问题）
        # http://************:55647/#/project/remark/231684?projectId=17&treeId=4265&fileId=66236&schemaId=15&page=29 index=328,331
        rest_header = sorted(s for t in header_texts - same_texts for s in t.split())
        rest_current_header = sorted(s for t in current_header_texts - same_texts for s in t.split())
        return rest_header == rest_current_header

    @cached_property
    def page_continued_tables(self) -> dict[int, list]:
        """
        存储所有续表，其中key为第一张表格的index，value为所有连续表格的index
        """
        return {}

    @cached_property
    def merged_tables(self) -> dict[int, list]:
        """
        存储所有被MergedTable合并的表，其中key为第一张表格的index，value为其余续表的index
        """
        return {}

    @cached_property
    def table_dict(self) -> dict[int, MergedTable]:
        def save_page_continued_tables(_tables):
            if len(_tables) < 2:
                return
            self.page_continued_tables[_tables[0]["index"]] = [e["index"] for e in _tables]

        def save_merged_tables(_tables):
            if len(_tables) < 2:
                return
            first_index = min(t["index"] for t in _tables)
            self.merged_tables[first_index] = [e["index"] for e in _tables if e["index"] != first_index]

        tables = []
        table_blocks, merge_table_blocks = [], []
        # 存储表格到页首之间的文本内容
        before_texts, after_texts = [], []
        last_page = 0
        continue_pages = {}
        max_col, header_texts = None, None
        page_merged, all_tbl_indices = [], []
        has_continued = False
        for element in (elt for pages in self.page_element_dict.values() for elt in pages):
            element = element.data
            page = element["page"]
            if page not in continue_pages:
                continue_pages[page] = self.is_continue_table_page(page)
            if page > last_page + 1:
                if table_blocks:
                    tables.append(MergedTable(table_blocks))
                    save_merged_tables(table_blocks)
                    table_blocks = []
                if merge_table_blocks:
                    save_page_continued_tables(merge_table_blocks)
                    merge_table_blocks = []
            if is_para_elt(element):
                # 针对interdoc识别的续表，跳过续表之前的所有元素块
                # http://************:55647/#/project/remark/451839?fileid=74567&projectId=37&treeId=46986&fileId=74567&schemaId=38
                if has_continued:
                    continue
                # 跳过续表页的所有文本元素块
                if continue_pages.get(page) or P_CONTINUED_WITH_COLON.search(element["text"]):
                    continue
                # http://************:55647/#/project/remark/418044?treeId=37993&fileId=114544&schemaId=29&projectId=17&page=118 index=1832
                # http://************:55647/#/project/remark/414813?treeId=9300&fileId=114184&schemaId=29&projectId=17&page=119 index=1161
                # 当前页面表格之前，与基准表格的表格之前有相同文本的元素块，则跳过
                text = clean_txt(element["text"], remove_cn_text=True)
                if not text or text in before_texts:
                    continue
                # 每个表格后面都有相同内容，可能是误识别为段落的页脚，也可能是通用notes
                # http://************:55647/#/project/remark/406553?treeId=5815&fileId=113257&schemaId=18&projectId=5815&schemaKey=C2.1.1&page=130 index=1054
                if (
                    page == last_page
                    and text in after_texts
                    and (text in self.texts_after_table(page - 1) or text in self.texts_after_table(page + 1))
                ):
                    continue
                if table_blocks:
                    tables.append(MergedTable(table_blocks))
                    save_merged_tables(table_blocks)
                    table_blocks = []
                if merge_table_blocks:
                    save_page_continued_tables(merge_table_blocks)
                    merge_table_blocks = []
            elif is_table_elt(element):
                tbl_idx = element["index"]
                # 列宽不同或列名文本不同，则表格不可以合并
                # TODO 考虑识别问题，可能需要用相似度来判断列名是否相同
                # pdfinsight识别到的page_merged必须做合并，否则信息不完整：https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7524#note_749134
                if merge_table_blocks:
                    is_page_merged = element["index"] in page_merged
                    can_merge = is_page_merged or self.can_merge_table_element(
                        element, max_col, header_texts, strict=False
                    )
                    # 合并条件：1.必须保证列宽相同  2. interdoc识别的续表必须保证连续
                    if table_blocks and (
                        not can_merge
                        or max_col != max_col_of_tbl_element(element)
                        or (
                            is_page_merged
                            and not is_sub_sequence(
                                page_merged, [e["index"] for e in table_blocks if e["index"] >= page_merged[0]]
                            )
                        )
                    ):
                        tables.append(MergedTable(table_blocks))
                        save_merged_tables(table_blocks)
                        table_blocks = []
                    if not can_merge:
                        # 是续表，但因为列宽识别原因，不能用MergedTable合并，但是不清空merged_table_blocks
                        # http://************:55647/#/project/remark/295004?fileid=70851&projectId=17&treeId=45192&fileId=70851&schemaId=18&page=224 index=3915,3923
                        save_page_continued_tables(merge_table_blocks)
                        merge_table_blocks = []
                if not table_blocks:
                    max_col = max_col_of_tbl_element(element)
                if not merge_table_blocks:
                    page_merged = page_merged_table_indices(element)
                    # 存储表格之前的文本元素块
                    # http://************:55647/#/project/remark/406553?treeId=5815&fileId=113257&schemaId=18&projectId=5815&schemaKey=C2.1.1
                    before_texts = self.texts_before_table(element, need_prev_page_texts=True)
                    after_texts = self.texts_after_table(page, tbl_idx)
                    header_texts = get_first_row_texts_of_table(element)
                has_continued = any(i > tbl_idx for i in page_merged)
                all_tbl_indices.append(tbl_idx)
                table_blocks.append(element)
                merge_table_blocks.append(element)
                # 取到一张续表后，当前页下方元素块照常判断
                continue_pages[page] = False
                last_page = page
        if table_blocks:
            tables.append(MergedTable(table_blocks))
            save_merged_tables(table_blocks)
        if merge_table_blocks:
            save_page_continued_tables(merge_table_blocks)

        table_dict = {}
        for table in tables:
            table_dict.update({item["index"]: table for item in table.tables})
        return table_dict

    @cached_property
    def root_chapter_ranges_from_contents(self) -> dict[str, list[int]]:
        """
        存储从目录中获取到的root章节的index范围
        """
        pdf_title_pages = {c["index"]: c["dest"]["page_index"] for c in self.pdf_contents}
        pdf_children = {c["title"]: c["children"] for c in self.pdf_contents if c["children"]}
        page_indices = sorted(self.page_chapter_from_contents)
        chapter_ranges = {}
        for page, next_page in zip(page_indices, page_indices[1:] + [self.max_page]):
            if page not in self.page_dict:
                # page_dict中无值，表示这个页面未识别到任何元素块
                continue
            root_title = self.page_chapter_from_contents[page]
            if root_title in pdf_children:
                # 如果下一个章节是子章节，则章节范围要取最大子章节的下一个章节
                # stock_code=00535, year=2024, mid=29
                next_index = max(pdf_children[root_title]) + 1
                next_page = pdf_title_pages.get(next_index) or self.max_page
            # 取第一页的第一个元素块index
            start, end = self.page_dict[page].element_index_range
            next_pager = self.page_dict.get(next_page - 1)
            if next_page - 1 > page and next_pager and (next_range := next_pager.element_index_range):
                # 取最后一页的最后一个元素块index + 1
                end = next_range[1] + 1
            chapter_ranges[root_title] = [start, end]
        return chapter_ranges

    @cached_property
    def page_chapter_from_contents(self) -> dict[int, str]:
        """
        根据pdf本身目录，结合年报中的目录，获取目录页码及章节名称
        注：父章节和子章节在同一页，直接取子章节名称
        """
        page_chapter_dict = {}
        for catalog in self.pdf_contents:
            page = catalog["dest"]["page_index"]
            if page > self.max_page:
                # 最后一个Cover页，可能未识别到任何元素块
                # http://************:55647/#/project/remark/379228?projectId=17&treeId=3370&fileId=156870&schemaId=29 page=190
                break
            page_chapter_dict[catalog["dest"]["page_index"]] = catalog["title"]
        if len(self.pdf_contents) < 10:
            page_chapters = self.find_chapter_from_contents()
            if not page_chapter_dict:
                return page_chapters
            titles = page_chapter_dict.values()
            for page, title in page_chapters.items():
                if page in page_chapter_dict or any(is_same_title(t, title) for t in titles):
                    continue
                page_chapter_dict[page] = title
        return page_chapter_dict

    def find_chapter_from_contents(self) -> dict[int, str]:
        """
        提取目录中的标题作为一级目录标题
        TODO 不使用偏移，而是使用每页页脚的页码做映射
        """
        # 取每个页面的前四个元素块，用于判断页面标题
        page_first_paras = {
            p.page: [
                clear_syl_title(e["text"], remove_cn_text=True, remove_bound_num=True).upper()
                for e in p.origin_para_elements[:4]
                if e.get("text") and len(e["text"]) < 100
            ]
            for p in self.page_dict.values()
        }
        found_contents = False
        for pager in self.page_dict.values():
            page, elements = pager.page, pager.origin_elements
            if page > 5:
                break
            elements = [e for e in elements if is_table_elt(e) or is_para_elt(e)]
            try:
                if len(elements) == 1 and P_CONTENTS.nexts(
                    clear_syl_title(elements[0].get("text") or "", remove_cn_text=True, remove_bound_num=True)
                ):
                    found_contents = True
                    continue
                found, contents = get_contents(self, elements, found_contents)
                if not found:
                    continue
                if not contents:
                    return {}
                # 修正页码偏移
                page_offset = self.page_offset or 0
                real_offset = []
                for page, title in contents.items():
                    page_idx = page + page_offset
                    # 先在页面周围找包含了标题的page
                    start_page = 0
                    if is_title_in_page(page_first_paras.get(page_idx), title):
                        start_page = page_idx
                    else:
                        for offset in range(1, 10):
                            if start_page > 0:
                                break
                            for idx in (page_idx - offset, page_idx + offset):
                                if is_title_in_page(page_first_paras.get(idx), title):
                                    start_page = idx
                                    break
                    if start_page == 0:
                        real_offset.append(None)
                        continue
                    # 从标题页向回找，直到页面找不到标题为止，再+1作为正确的开始页
                    right_page_idx = start_page
                    for idx in range(start_page - 1, start_page - 20, -1):
                        if not is_title_in_page(page_first_paras.get(idx), title):
                            right_page_idx = idx + 1
                            break
                    real_offset.append(right_page_idx - page)
                if len(set(real_offset)) == 1:
                    if real_offset[0] is None:
                        return {}
                    return {page + real_offset[0]: title for page, title in contents.items()}
                # http://************:55647/#/project/remark/234077?treeId=37672&fileId=66635 P80的章节是横向
                most_comm_offset, count = Counter(real_offset).most_common()[0]
                if most_comm_offset is not None and count / len(contents) >= 0.8:
                    return {page + most_comm_offset: title for page, title in contents.items()}
                return {}
            except Exception as e:
                logger.warning(f"find contents error: {e}")
        return {}

    @cached_property
    def root_syllabuses(self) -> list[dict]:
        """
        清除前后页码后的一级目录
        """
        root_syllabuses = []
        for syllabus in self.syllabus_reader.syllabuses:
            if syllabus["parent"] != -1:
                continue
            root_syllabuses.append({"title": syllabus["title"].upper(), "range": syllabus["range"]})
        return root_syllabuses

    @cached_property
    def page_chapter_from_first_para(self) -> dict[int, str]:
        """
        判断文档每页的第一个元素块，是不是一级章节标题
        """
        page_first = {}
        for page, pager in self.page_dict.items():
            if not pager.like_para_elements:
                continue
            if fake_title := page_first_title(pager.header_elements + pager.origin_para_elements, self):
                page_first[page] = fake_title
        fill_middle_page_title(self.page_dict.keys(), page_first)
        return page_first

    def _fix_page_merged_paragraph(self, element, need_copy=True):
        if not (page_merged_paragraph := element.get("page_merged_paragraph")):
            return None
        new_element = copy.copy(element) if need_copy else element
        page_merged_paragraph = copy.deepcopy(page_merged_paragraph)
        new_chars = []
        # page_merged_paragraph中无text，说明是后处理得到，需要重新组织text
        need_reset_text = "text" not in page_merged_paragraph
        merged_indices = page_merged_paragraph["paragraph_indices"]
        # 跨页合并时，合并了下一页的大标题
        # https://jura6-lir.paodingai.com/#/hkex/agm-circular-checking/report-review/323039?fileId=87344&schemaId=33&rule=M37 index=132
        if len(merged_indices) == 2 and len({self.element_dict[idx]["page"] for idx in merged_indices}) == 2:
            next_element = self.element_dict[merged_indices[1]]
            if self.is_page_first_para(next_element):
                all_follow_elements = [next_element, *(self.find_follow_lines(next_element) or [])]
                new_index = max(e["index"] for e in all_follow_elements) + 1
                if new_index in self.element_dict and P_ALL_CHAPTER_TITLES.nexts(
                    " ".join(e["text"] for e in all_follow_elements)
                ):
                    need_reset_text = True
                    merged_indices[1] = new_index
        start_idx, *_, end_idx = merged_indices
        for idx in merged_indices:
            if idx not in self.element_dict:
                continue
            paragraph = self.element_dict[idx]
            new_chars.extend(paragraph.get("chars", []))
            # http://************:55647/#/project/remark/268767?treeId=6453&fileId=70587&schemaId=18&projectId=17&schemaKey=C1 元素块409,410
            if idx != end_idx and P_WORD_NUM.search(paragraph["text"][-1]):
                need_reset_text = True
                # 多行之间必须给空格，构造一个outline与最后一个字符相同的空字符串
                space_char = copy.copy(paragraph["chars"][-1])
                space_char["text"] = " "
                new_chars.append(space_char)
            if idx != element["index"]:
                page_merged_paragraph["page"] = paragraph.get("page")
                page_merged_paragraph["outline"] = paragraph.get("outline")
        page_merged_paragraph["chars"] = new_chars
        if need_reset_text:
            page_merged_paragraph["text"] = "".join(c["text"] for c in new_chars)
        element_index = element["index"]
        if element_index != start_idx:
            new_element["fragment"] = True
        else:
            # 覆盖跨页段落片段原信息
            # （只保证片段的一个元素块完整，后面的 elt['fragment'] = True 且内容不完整）
            new_element["chars"] = page_merged_paragraph["chars"]
            new_element["text"] = page_merged_paragraph["text"]
        # 修改元素块对应的章节的标题
        if element_index in self.syllabus_reader.elt_syllabus_dict:
            self.syllabus_reader.elt_syllabus_dict[element_index]["title"] = page_merged_paragraph["text"]
        new_element["page_merged_paragraph"] = page_merged_paragraph
        return new_element

    def is_page_first_para(self, element):
        if not is_para_elt(element):
            return False
        for item in self.page_element_dict.get(element["page"]) or []:
            elt = item.data
            if self.is_background_image(elt):
                continue
            return elt["index"] == element["index"]
        return False

    def _fix_element(self, elt) -> tuple[str, dict]:
        new_element = None
        if elt.get("class") == "TABLE":
            if table := self.table_dict.get(elt.get("index")):
                new_element = copy.copy(elt)
                new_element["origin_cells"] = elt["cells"]
                # 直接把page_merged所有表的cells都给到每一个表，是不合适的，无法区分续表和首表，这里做拆分
                # stock_code=00535, year=2024, mid=29, index=939,940
                elt_index = new_element["index"]
                if elt_index in table.continue_table_cells:
                    # 续表只存本身的cells
                    new_element["cells"] = table.continue_table_cells[elt_index]
                else:
                    # 仅对跨页表格的第一张表格重新设置cells
                    new_element["cells"] = table.cells
                # 补充page_merged_table信息
                if len(table.tables) > 1 and not new_element.get("page_merged_table"):
                    new_element["page_merged_table"] = table.tables[0] | {"cells_idx": table.cells_idx}
        elif elt.get("class") in ("IMAGE", "SHAPE"):
            title = self.find_shape_title(elt)
            new_element = copy.copy(elt)
            new_element["title"] = title
            if "text" not in elt:
                new_element["text"] = ""
        if elt.get("class") == "PARAGRAPH":
            # 章节序号和标题之间缺少空格
            new_elt = None
            if elt["index"] in self.syllabus_reader.elt_syllabus_dict:
                new_elt = add_space_to_element(elt)
            new_element = self._fix_page_merged_paragraph(new_elt or elt, need_copy=not new_elt)
            # 如果是识别错误的行，合并text
            if not new_element and self.max_page > 10 and (follow_elements := self.find_follow_lines(elt)):
                # stock=2689, year=2024, page=6, index=114&115, mid=15, rule=B90
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7539
                if not new_elt:
                    new_elt = copy.copy(elt)
                merge_elements = [new_elt, *follow_elements]
                indices = [e["index"] for e in merge_elements]
                # 给原始元素块带上`page_merged_paragraph`
                for follow_element in merge_elements:
                    follow_element["page_merged_paragraph"] = {"paragraph_indices": indices}
                new_element = self._fix_page_merged_paragraph(new_elt, need_copy=False)
        return elt.get("class"), new_element or elt

    def get_font_style(self, chars) -> FontStyle | None:
        if not chars:
            return None
        style_id = Counter(char["style"] for char in chars).most_common()[0][0]
        style = self.data.get("styles", {}).get(style_id)
        if not style:
            style = self.data.get("text_styles")[style_id]
        if not (style and style.get("has_font")):
            return None
        # 仅考虑这些字体样式
        style_names = {"fontsize", "fontcolor", "fontname", "italic", "bold", "light"}
        return FontStyle(**{k: v for k, v in style.items() if k in style_names})

    def get_font_size(self, chars) -> int | None:
        font = self.get_font_style(chars)
        if not font:
            return None
        return font.fontsize

    def find_follow_lines(self, element: dict):
        """
        找到被识别成多行的连续段落
        注意：传进来的element必须是一个段落
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_408592
        """
        elt_index, text, page = element["index"], element.get("text"), element["page"]
        # 前5页/后1页，或图片+图表个数超过5个不做处理
        if (
            (self.max_page <= 50 and page < 3)
            or (self.max_page > 50 and page < 5)
            or page == self.max_page
            or self.page_image_count[page] + self.page_shape_count[page] > 5
        ):
            return []
        # 不处理CI章节的元素块，因为都是大量人名
        ci_chapters = [s for s in self.syllabus_reader.syllabuses if P_IGNORE_FIX_CHAPTER.nexts(s["title"])]
        if not is_para_elt(element) or not text.strip() or any(is_in_range(elt_index, s["range"]) for s in ci_chapters):
            return []
        pos, page_box = element.get("position"), self.page_left_right[page]
        page_left, page_right = page_box.get(tuple(pos)) or (0.0, 0.0) if pos else (0.0, 0.0)
        if page_left == 0.0 or page_right == 0.0 or not self.normal_font_size:
            # 页面outline或默认字号获取有问题，不处理
            return []
        base_style = self.get_font_style(element["chars"])
        is_normal_size = base_style.fontsize <= self.normal_font_size + 1 if base_style else True
        is_first, elements, texts = True, [], []
        is_chapter, is_page_first = False, self.is_page_first_para(element)
        while True:
            if not is_first:
                element = self.element_dict[elt_index]
                elements.append(element)
            # 没有下一行
            next_index = elt_index + 1
            if elt_index + 1 not in self.element_dict:
                break
            # 下一行的page/position/class及样式必须与当前行一致
            next_element = self.element_dict[next_index]
            if (
                next_element["page"] > page
                or next_element.get("position") != pos
                or next_element["class"] != PDFInsightClassEnum.PARAGRAPH
                or not next_element["text"].strip()
            ):
                break
            next_ele_style = self.get_font_style(next_element.get("chars"))
            # 大小一致，仅字体存在部分差异，如上下文可按英文语法格式拼接，则也可合并
            if next_ele_style != base_style:
                chars = list(chain(element.get("chars") or [], next_element.get("chars") or []))
                if (
                    chars
                    and next_element["page"] == page
                    and base_style
                    and next_ele_style
                    and base_style.similarity(next_ele_style)
                    and text.strip().lower().split()[-1] in ("of", "for")
                    # 上下行左间距小于1，且上下文行间距小于平均高度，则认为可合并
                    and abs(element["outline"][0] - next_element["outline"][0]) < 1
                    and (next_element["outline"][1] - element["outline"][-1])
                    < sum(char["box"][-1] - char["box"][1] for char in chars) / len(chars)
                ):
                    pass
                else:
                    break

            is_chapter = is_chapter or any(
                idx in self.syllabus_reader.elt_syllabus_dict for idx in {elt_index, next_index}
            )
            texts.append(element["text"])
            if is_end_line(
                element, next_element, " ".join(texts), page_right, is_first, is_chapter, is_page_first, is_normal_size
            ):
                break
            is_first = False
            elt_index += 1
        return elements

    def find_shape_title(self, element):
        title = ""
        title_idx = element["index"] - 1
        while True:
            if title_idx < 1:
                break
            if title_idx not in self.element_dict:  # NOTE: 因为pdfinsight数据的改动，可能会导致index不在data中
                title_idx -= 1
                continue
            possible_title_elt = self.element_dict[title_idx]
            if possible_title_elt["page"] != element["page"]:
                break
            if possible_title_elt["class"] != "PARAGRAPH":
                title_idx -= 1
                continue
            title = possible_title_elt["text"]
            break
        return title

    def is_syllabus_title(self, element: dict, check_page_first: bool = False):
        if not element or not (text := element.get("text")):
            return False
        text = clean_txt(text, remove_cn_text=True).upper()
        if (
            element["index"] in self.syllabus_reader.elt_syllabus_dict
            or bool(P_CONTINUED.search(text))
            or text in self.syllabus_titles
        ):
            return True
        return check_page_first and (text in self.page_chapter_from_first_para.values())

    def get_full_syll_path_of_elem(self, element) -> list[dict]:
        """
        获取元素块的所有章节
        """
        if not element or not (syllabus := self.syllabus_dict.get(element.get("syllabus"))):
            return []
        return self.syllabus_reader.full_syll_path(syllabus)

    @staticmethod
    def find_cell_idx_by_outline(tbl, outline, box_page):
        overlap_threshold = 0.618

        def inter_x(*outlines):
            overlap_length = min(outlines[0][2], outlines[1][2]) - max(outlines[0][0], outlines[1][0])
            return overlap_length if overlap_length > 0 else 0

        def inter_y(*outlines):
            overlap_length = min(outlines[0][3], outlines[1][3]) - max(outlines[0][1], outlines[1][1])
            return overlap_length if overlap_length > 0 else 0

        def area(*outlines):
            return (outlines[1][3] - outlines[1][1]) * (outlines[1][2] - outlines[1][0])

        def overlap_percent(*outlines):
            return inter_y(*outlines) * inter_x(*outlines) / area(*outlines)

        def rcnn(*outlines):
            return overlap_percent(*outlines) > overlap_threshold

        max_overlap = None
        for cell_idx, cell in tbl.get("cells", {}).items():
            if int(cell["page"]) != int(box_page):
                continue
            overlap = overlap_percent(outline, cell["box"])
            if overlap <= 0:
                continue
            if max_overlap is None or max_overlap[1] < overlap:
                max_overlap = (cell_idx, overlap)
        if max_overlap is not None:
            return max_overlap[0]
        return None

    def find_cell_idxes_by_outline(self, tbl, outline, box_page):
        def inter_x(*outlines):
            overlap_length = min(outlines[0][2], outlines[1][2]) - max(outlines[0][0], outlines[1][0])
            return overlap_length if overlap_length > 0 else 0

        def inter_y(*outlines):
            overlap_length = min(outlines[0][3], outlines[1][3]) - max(outlines[0][1], outlines[1][1])
            return overlap_length if overlap_length > 0 else 0

        def area(*outlines):
            return (outlines[1][3] - outlines[1][1]) * (outlines[1][2] - outlines[1][0])

        def overlap_percent(*outlines):
            return inter_y(*outlines) * inter_x(*outlines) / area(*outlines)

        outline = normalize_outline(outline)

        res = []
        for cell_idx, cell in tbl.get("cells", {}).items():
            if int(cell["page"]) != int(box_page):
                continue
            overlap = overlap_percent(outline, cell["box"])
            if overlap <= 0:
                continue
            res.append(cell_idx)
        return res

    def find_element_by_outline(self, page, outline, debug=False):
        overlap_threshold = 0.618

        def inter_x(*outlines):
            overlap_length = min(outlines[0][2], outlines[1][2]) - max(outlines[0][0], outlines[1][0])
            return overlap_length if overlap_length > 0 else 0

        def inter_y(*outlines):
            overlap_length = min(outlines[0][3], outlines[1][3]) - max(outlines[0][1], outlines[1][1])
            return overlap_length if overlap_length > 0 else 0

        def area(*outlines):
            return (outlines[1][3] - outlines[1][1]) * (outlines[1][2] - outlines[1][0])

        def overlap_percent(*outlines):
            _area = area(*outlines)
            if _area == 0:
                return 0
            return inter_y(*outlines) * inter_x(*outlines) / _area

        def rcnn(*outlines):
            return overlap_percent(*outlines) > overlap_threshold

        outline = normalize_outline(outline)

        max_overlap = None
        elements = self.page_element_dict.get(page, [])
        if debug:
            logger.info(f"find outline: {outline}")
        for elt in elements:
            overlap = overlap_percent(outline, elt.outline)
            if debug:
                logger.info(f"{overlap:.2f}, {elt.outline}")
            if overlap > 0 and max_overlap is None:
                max_overlap = (elt, overlap)
            elif overlap > 0 and max_overlap[1] < overlap:
                max_overlap = (elt, overlap)
            # if overlap > overlap_threshold:
            #     return self._return(elt.data)

        if max_overlap is not None:
            return self.fixed_element_dict.get(max_overlap[0].index) or self._fix_element(max_overlap[0].data)

        return None, None

    @staticmethod
    def overlap_percent(element_outline, box_outline, base="box", method="area"):
        """outline:
        (left, top, right, bottom)
        or
        {"box_bottom": bottom, "box_left": left, ...}
        """

        def inter_x(*outlines):
            overlap_length = min(outlines[0][2], outlines[1][2]) - max(outlines[0][0], outlines[1][0])
            return overlap_length if overlap_length > 0 else 0

        def inter_y(*outlines):
            overlap_length = min(outlines[0][3], outlines[1][3]) - max(outlines[0][1], outlines[1][1])
            return overlap_length if overlap_length > 0 else 0

        def area(outline):
            return (outline[3] - outline[1]) * (outline[2] - outline[0])

        element_outline = normalize_outline(element_outline)
        box_outline = normalize_outline(box_outline)

        if method == "edge":
            if base == "element":
                base_x, base_y = element_outline[2] - element_outline[0], element_outline[3] - element_outline[1]
            else:
                base_x, base_y = box_outline[2] - box_outline[0], box_outline[3] - box_outline[1]
            return inter_x(element_outline, box_outline) / base_x, inter_y(element_outline, box_outline) / base_y
        if base == "element":
            base_area = area(element_outline)
        elif base == "max":
            base_area = max(area(element_outline), area(box_outline))
        elif base == "min":
            base_area = min(area(element_outline), area(box_outline))
        else:
            base_area = area(box_outline)
        base_area = base_area if base_area else 1

        return inter_y(element_outline, box_outline) * inter_x(element_outline, box_outline) / base_area

    def find_elements_by_outline(self, page, outline, debug=False):
        overlap_threshold = 0.618

        def rcnn(*outlines):
            return self.overlap_percent(*outlines) > overlap_threshold

        res = []
        max_overlap = None
        elements = self.page_element_dict.get(page, [])
        if debug:
            logger.info(f"find outline: {outline}")
        for elt in elements:
            overlap = self.overlap_percent(outline, elt.outline)
            if debug:
                logger.info(f"{overlap:.2}f, {elt.outline}")
            if overlap > 0 and max_overlap is None:
                max_overlap = (elt, overlap)
            elif overlap > 0 and max_overlap[1] < overlap:
                max_overlap = (elt, overlap)
            if overlap > overlap_threshold:
                res.append(self.fixed_element_dict.get(elt.index) or self._fix_element(elt.data))

        if not res and max_overlap is not None:
            res.append(self.fixed_element_dict.get(max_overlap[0].index) or self._fix_element(max_overlap[0].data))

        if debug:
            logger.info(f"find {len(res)} elements")

        if not res:
            logging.warning(f"can't find elements by outline {outline}, in page {page}")

        return res

    def find_chars_by_outline(self, page, outline):
        chars = []
        chars_idx = []
        outline = normalize_outline(outline)
        _, element = self.find_element_by_outline(page, outline)
        if element:
            for idx, char in enumerate(element.get("chars", [])):
                if self.is_box_in_box_by_center(char["box"], outline):
                    chars.append(char)
                    chars_idx.append(idx)

        return element, chars, chars_idx

    @staticmethod
    def is_box_in_box_by_center(char_box, box2):
        if not char_box or not box2:
            return False
        h_center = (char_box[0] + char_box[2]) / 2
        v_center = (char_box[1] + char_box[3]) / 2
        return box2[2] >= h_center >= box2[0] and box2[3] >= v_center >= box2[1]

    def find_element_by_index(self, index: int) -> tuple[str | None, dict | None]:
        """
        NOTE: pdfinsight数据的改动或者是预期之外的index会导致返回为None，或者index超限，也会直接抛出异常，调用方需要对此进行处理。
        1.如果 index 是从pdfinsight中得来的 调用方可以不处理异常  有错误抛出即可
            有以下几种情况
                1. index 来自 syllabus['range']
                2. index 来自 初步定位元素块
                3. index 来自 pdfinsight.page_element_dict\table_dict等
        2.如果 index 是自生成的 则需要处理异常 保证程序正常运行下去
            有以下几种情况
                1. range(start, start+10)
                2. while True
                3. index from predictor config
        """
        if index > self.max_index or index < 0:
            raise IndexError(f"{index=} out of range")
        return self.fixed_element_dict.get(index, (None, None))

    def elements_iter(self, filter_func: Callable | None = None) -> Iterable[dict]:
        for _, element in self.fixed_element_dict.values():
            if element and (not filter_func or filter_func(element)):
                yield element

    def origin_elements_iter(self, filter_func: Callable | None = None) -> Iterable[dict]:
        for items in self.page_element_dict.values():
            for item in items:
                element = item.data
                if element and (not filter_func or filter_func(element)):
                    yield element

    def find_elements_near_by(
        self,
        index,
        amount=1,
        step=1,
        steprange=100,
        include=False,
        aim_types=None,
        neg_patterns=None,
        stop_at_syllabus=False,
    ) -> List[dict]:
        """Unique index only works for tables and paragraphs"""
        elements = []
        cursor = index
        if include:
            try:
                _, ele = self.find_element_by_index(cursor)
            except IndexError:
                pass
            else:
                if ele:
                    elements.append(ele)
        while True:
            cursor += step
            try:
                _, ele = self.find_element_by_index(cursor)
            except IndexError:
                break
            if not ele or not is_aim_element(ele, aim_types, neg_patterns) or self.is_skip_element(ele):
                continue
            merged_indices = get_keys(ele, ["page_merged_paragraph", "paragraph_indices"])
            # 合并元素块取第一个
            # http://************:55647/#/project/remark/296354?treeId=4927&fileId=71020&schemaId=18&projectId=17&schemaKey=C1 index=1244
            if merged_indices and merged_indices[0] != cursor:
                _, merged_elt = self.find_element_by_index(merged_indices[0])
                elements.append(merged_elt)
            else:
                elements.append(ele)
            # 遇到目录则终止提取
            if stop_at_syllabus and self.is_syllabus_title(ele):
                break
            if abs(cursor - index) >= steprange:
                break
            if len(elements) >= amount:
                break

        return elements

    def find_syllabuses_by_index(self, index):
        """Unique element index"""
        sylls = [s for s in self.syllabus_dict.values() if is_in_range(index, s["range"])]
        return sorted(sylls, key=lambda s: s["index"])

    def get_root_syllabus_range(self, p_root_syllabus) -> tuple[int, int]:
        """
        根据给定的root章节正则，计算其章节范围
        """

        def calc_start_end(new_start, new_end):
            return min(start, new_start) if start else new_start, max(end, new_end)

        start, end = 0, 0
        # 1. 根据目录章节范围找
        for title, index_range in self.root_chapter_ranges_from_contents.items():
            if not p_root_syllabus.search(title):
                continue
            start, end = calc_start_end(*index_range)
        if end > start > 0:
            return start, end
        # 2.1 根据章节范围找
        for syllabus in self.syllabus_reader.syllabuses:
            if syllabus["parent"] == -1 and p_root_syllabus.search(syllabus["title"]):
                cur_start, cur_end = syllabus["range"]
                start, end = calc_start_end(cur_start, cur_end)
        # 2.2 根据page_first找
        for page, title in self.page_chapter_from_first_para.items():
            if p_root_syllabus.search(title) and (pager := self.page_dict.get(page)):
                cur_start, cur_end = pager.element_index_range
                start, end = calc_start_end(cur_start, cur_end)
        return start, end

    def get_root_syllabus_title(self, syllabus_or_element, is_syllabus=False):
        """
        找章节或元素块的一级章节名称
        """
        root_title = ""
        if is_syllabus:
            page = get_keys(syllabus_or_element, ["dest", "page_index"])
            index = syllabus_or_element.get("element")
        else:
            page, index = syllabus_or_element["page"], syllabus_or_element["index"]
        if page:
            root_title = self.page_chapter_from_first_para.get(page)
        if root_title:
            return root_title
        if not index:
            return ""
        syllabuses = self.find_syllabuses_by_index(index)
        if not syllabuses:
            return ""
        return syllabuses[0]["title"]

    def get_nearest_syllabus(self, syllabus_or_element, is_syllabus=False):
        """
        找章节或元素块的上一级章节
        """
        if is_syllabus:
            syllabuses = self.find_syllabuses_by_index(syllabus_or_element["element"])
        else:
            syllabuses = self.find_syllabuses_by_index(syllabus_or_element["index"])
        if not syllabuses:
            return None
        return find_real_syllabus(self, syllabuses[-1])

    def find_sylls_by_pattern(self, patterns, candidates=None, *, neglect_patterns=None, match_remove_blank=False):
        res = []
        pattern = patterns[0]
        if candidates is None:
            # candidates may be a empty list, At this time,
            # it should be a recursive call, and should not get all the chapters
            # ex: res.extend(self.find_sylls_by_pattern(patterns[1:], candidates=[]))

            candidates = sorted(self.syllabus_dict.keys())
        for syll_idx in candidates:
            syll = find_real_syllabus(self, self.syllabus_dict[syll_idx])
            clean_text = clear_syl_title(syll["title"])
            if len(clean_text.split()) > 40:
                # 单词数量过多的一般是误识别的章节
                continue
            if not pattern.search(clean_text) and not (
                match_remove_blank and pattern.search(clean_txt(clean_text, remove_blank=True))
            ):
                continue
            if neglect_patterns and neglect_patterns.nexts(clean_text):
                continue
            if len(patterns) == 1:
                res.append(syll)
            else:
                res.extend(
                    self.find_sylls_by_pattern(
                        patterns[1:], candidates=syll["children"], match_remove_blank=match_remove_blank
                    )
                )
        return res

    def get_parent_titles(self, index, remove_cn_text=False, remove_blank=False) -> list[str]:
        if is_invalid_syllabus(index):
            return []
        return [
            clear_syl_title(syllabus.get("title", ""), remove_cn_text=remove_cn_text, remove_blank=remove_blank)
            for syllabus in self.syllabus_reader.full_syll_path(self.syllabus_dict[index])
        ]

    def get_real_parent_syllabuses(self, syllabus, include_root=False):
        """
        为指定章节找到所有父章节
        """

        # 递归函数用于查找父章节
        def find_parents(syllabus_, last_index=None):
            if syllabus_["parent"] != -1 and last_index != syllabus_["index"]:
                syllabuses.append(syllabus_)
                last_index = syllabus_["index"]
                parent_syllabus = find_real_syllabus(self, self.syllabus_reader.syllabus_dict[syllabus_["parent"]])
                return find_parents(parent_syllabus, last_index=last_index)
            return syllabus_

        syllabus = find_real_syllabus(self, syllabus)
        syllabuses = []
        last_syllabus = find_parents(syllabus)

        # 如果需要包括根章节，检查并加入根章节
        if include_root and last_syllabus["parent"] == -1:
            syllabuses.append(last_syllabus)
        return syllabuses

    def get_child_syllabus_indices(self, syllabus: dict) -> set[str]:
        child_indices = set()
        for idx in syllabus["children"]:
            child_indices.add(idx)
            child_indices.update(self.get_child_syllabus_indices(self.syllabus_dict[idx]))
        return child_indices

    def find_tables_by_pattern(self, patterns, start=None, end=None):
        res = []
        for table in self.tables:
            if start is not None and table["index"] < start:
                continue
            if end is not None and table["index"] > end:
                continue
            match_all = True
            for pattern in patterns:
                match = False
                for cell in table["cells"].values():
                    if pattern.search(clean_txt(cell["text"], remove_blank=True)):
                        match = True
                        break
                if not match:
                    match_all = False
                    break
            if match_all:
                res.append(table)
        return res

    def find_paragraphs_by_pattern(self, patterns, start=None, end=None, remove_blank=True):
        res = []
        for para in self.paragraphs:
            if start is not None and para["index"] < start:
                continue
            if end is not None and para["index"] > end:
                continue
            match_all = True
            for pattern in patterns:
                if not pattern.search(clean_txt(para["text"], remove_blank=remove_blank)):
                    match_all = False
                    continue
            if match_all:
                res.append(para)
        return res

    def find_elements_by_index_range(self, index, start, end, limit=0, etype=None):
        elements = []
        for i in range(index + start, index + end):
            try:
                _etype, _ele = self.find_element_by_index(i)
            except IndexError:
                break
            if _ele is None:
                continue
            if etype is not None and _etype != etype:
                continue
            elements.append(_ele)
            if len(elements) >= limit > 0:
                break
        return elements

    def find_elements_by_page(self, page: int) -> List[dict]:
        return [x.data for x in self.page_element_dict.get(page, [])]

    def is_catalog_page(self, page: int) -> bool:
        if page in self.page_dict:
            return self.page_dict[page].is_catalog
        return self._is_catalog_page(page)

    def _is_catalog_page(self, page: int) -> bool:
        match_times = 0
        is_catalog = False
        page_elements = self.find_elements_by_page(page)
        for page_element in page_elements:
            if page_element["class"] != "PARAGRAPH":
                continue
            clean_text = clean_txt(page_element["text"], remove_blank=self.remove_blank)
            if P_CONTENTS.nexts(clean_text):
                return True
            if DIRECTORY_START_PATTERN.nexts(clean_text):
                match_times += 1
                if match_times > 5:
                    is_catalog = True
                    break
        return is_catalog

    def fix_continued_para(self, elt):
        """
        拼接跨页段落
        """
        elt = deepcopy(elt)
        prev_elts = self.find_elements_near_by(elt["index"], step=-1, amount=1)
        if prev_elts and prev_elts[0] and prev_elts[0]["class"] == "PARAGRAPH" and prev_elts[0]["continued"]:
            elt["text"] = prev_elts[0]["text"] + elt["text"]
            elt["chars"] = prev_elts[0]["chars"] + elt["chars"]

        if elt["continued"]:
            next_elts = self.find_elements_near_by(elt["index"], step=1, amount=3)
            for next_elt in next_elts:
                if next_elt["class"] == "PARAGRAPH":
                    elt["text"] += next_elt["text"]
                    elt["chars"] += next_elt["chars"]
                    break
        return elt

    def get_page_box_for_elements(self, elements):
        element_indexes = set()
        elements_by_page = OrderedDict()
        for elt in elements:
            elt_type = elt["type"]
            if elt and elt_type not in ["PAGE_HEADER", "PAGE_FOOTER"] and elt["index"] not in element_indexes:
                elements_by_page.setdefault(elt["page"], []).append(elt)
                if elt.get("page_merged_paragraph"):
                    for elt_index in elt["page_merged_paragraph"]["paragraph_indices"]:
                        element_indexes.add(elt_index)
                else:
                    element_indexes.add(elt["index"])
        page_box = []
        for page, elts in elements_by_page.items():
            groups = self.get_discontinuous_group(elts)
            for group in groups:
                outline = {
                    "box_top": min(e["outline"][1] for e in group),
                    "box_right": max(e["outline"][2] for e in group),
                    "box_bottom": max(e["outline"][3] for e in group),
                    "box_left": min(e["outline"][0] for e in group),
                }
                page_box.append(
                    {
                        "page": page,
                        "box": outline,
                        "text": "\n".join([e.get("text") for e in group if e.get("text")]),  # 添加完整的段落text
                    }
                )
                for item in group:
                    page_merged_paragraph = item.get("page_merged_paragraph", {})
                    if page_merged_paragraph and page_merged_paragraph.get("page"):
                        page_box.append(
                            {
                                "page": page_merged_paragraph["page"],
                                "box": page_merged_paragraph["outline"],
                                "text": "",  # 上面已经添加完整的段落text 这里不再添加
                            }
                        )
        return page_box

    @staticmethod
    def get_discontinuous_group(elements):
        element_dict = {elt["index"]: elt for elt in elements}
        indexes = list(element_dict.keys())
        element_groups = []
        for _, idx in groupby(enumerate(indexes), lambda x: x[1] - x[0]):
            element_group = []
            for index in list(map(itemgetter(1), idx)):
                element_group.append(element_dict[index])
            element_groups.append(element_group)

        return element_groups

    def continuous_tables(self, tbl_element, only_after=False):
        """
        找给定元素块的跨页连续表格，注意返回结果中会包含元素本身
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4574#note_545337
        http://************:55647/#/project/remark/232965?projectId=17&treeId=4451&fileId=66449&schemaId=15&schemaKey=B81 page103
        http://************:55647/#/project/remark/235176?treeId=5614&fileId=66818&schemaId=15&projectId=17&schemaKey=B79
        跨4页: http://************:55647/#/project/remark/244798?treeId=5614&fileId=66818&schemaId=28&projectId=17&schemaKey=E(d)(iv)-1&page=87
        """
        # # 先向前找5页
        # table_elements = [] if only_after else self._find_continuous_tables(tbl_element, -5)
        # # 再向后找5页
        # table_elements.extend(self._find_continuous_tables(tbl_element, 5))
        # if not table_elements:
        #     return [tbl_element]
        merge_indices = []
        for table_indices in self.page_continued_tables.values():
            if tbl_element["index"] in table_indices:
                merge_indices = table_indices
                break
        if not merge_indices:
            return [tbl_element]
        table_elements = []
        for index in merge_indices:
            if only_after and index < tbl_element["index"]:
                continue
            if index == tbl_element["index"]:
                table_elements.append(tbl_element)
            else:
                _, element = self.find_element_by_index(index)
                table_elements.append(element)
        # # 添加元素块本身
        # table_elements.append(tbl_element)
        # table_elements = sorted(table_elements, key=itemgetter("index"))
        # 排除已经被识别为合并表格的元素块
        merged_indices, lost_elem_indices = set(), set()
        for element in table_elements:
            elem_index = element["index"]
            if not (merge_index := get_keys(element, ["page_merged_table", "index"])):
                continue
            if elem_index == merge_index:
                for index, cells in get_keys(element, ["page_merged_table", "cells_idx"]).items():
                    index = int(index)
                    if index == elem_index:
                        continue
                    # 跨页合并表格中的cells没合并 index=893 http://************:55647/#/project/remark/235176?treeId=5614&fileId=66818&schemaId=15&projectId=17&schemaKey=B79
                    if not set(cells).issubset(element["cells"]):
                        lost_elem_indices.add(index)
            elif elem_index not in lost_elem_indices:
                merged_indices.add(elem_index)
        result = [elt for elt in table_elements if elt["index"] not in merged_indices]
        # 过滤后没有可用的表格，直接返回入参元素块
        return result if result else [tbl_element]

    # def _find_continuous_tables(self, tbl_element, page_offset):
    #     base_page = tbl_element["page"]
    #     stop_page = base_page + page_offset
    #     merged_indices = {int(i) for i in get_keys(tbl_element, ["page_merged_table", "cells_idx"], default={})}
    #     # 计算表格列数
    #     max_col = max_col_of_tbl_element(tbl_element)
    #     step = -1 if page_offset < 0 else 1
    #     next_elem_idx = tbl_element["index"]
    #     first_row_texts = get_first_row_texts_of_table(tbl_element)
    #     table_elements = []
    #     while True:
    #         next_elem_idx += step
    #         try:
    #             ele_type, element = self.find_element_by_index(next_elem_idx)
    #         except IndexError:
    #             break
    #         if not element:
    #             continue
    #         if (step < 0 and element["page"] < stop_page) or (step > 0 and element["page"] > stop_page):
    #             # 超过3页则终止
    #             break
    #         # 已经被识别为合并表格： http://************:55647/#/project/remark/234816?treeId=7989&fileId=66758&schemaId=15&projectId=17 index=151
    #         if next_elem_idx in merged_indices:
    #             table_elements.append(element)
    #             continue
    #         if self.is_skip_element(element, skip_tbl_unit=True):
    #             continue
    #         # # TODO 最好判断表格上方的日期与上一张表格一致: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/7326
    #         # if is_para_elt(element) and PC_ONLY_DATE.nexts(element["text"]):
    #         #     continue
    #         if ele_type != PDFInsightClassEnum.TABLE.value:
    #             break
    #         # http://************:55647/#/project/remark/263084?treeId=8837&fileId=69831&schemaId=18&projectId=17&schemaKey=C6.1
    #         # 当前表格的列数或者第一行文本与上个表格不同，则不合并
    #         if not self.can_merge_table_element(element, max_col, first_row_texts, strict=False):
    #             break
    #         table_elements.append(element)
    #     return table_elements

    def is_skip_element(
        self,
        element,
        *,
        aim_types: set[str] = None,
        outline_left: float = None,
        outline_right: float = None,
        skip_tbl_unit: bool = False,
        skip_chinese: bool = True,
        skip_merged: bool = True,
    ) -> bool:
        """
        Args:
            element: 要判断的元素块
            aim_types: 指定的类型，不满足该类型返回True
            outline_left: 文档内容左边界，用于排除超过左边界的图片
            outline_right: 文档内容右边界，用于排除超过右边界的图片
            skip_tbl_unit: 是否跳过表格的单位，一般为`unit: xxx`
            skip_chinese: 是否跳过含中文的元素块
            skip_merged: 是否跳过合并元素块的后续元素块（不是第一个)
        """
        if not element or not element.get("class"):
            return True
        element_type = element["class"]
        if (aim_types and element_type not in aim_types) or (
            not aim_types and element_type in {PDFInsightClassEnum.PAGE_FOOTER, PDFInsightClassEnum.PAGE_HEADER}
        ):
            return True

        page_start, _ = self.page_dict[element["page"]].element_index_range
        if self.is_background_image(element, outline_left, outline_right):
            return True
        if element_type != PDFInsightClassEnum.PARAGRAPH.value:
            return False
        if not clean_txt(element.get("text") or "", remove_blank=True):
            return True
        # 页面前3个元素块，且满足正则条件，则跳过
        if page_start <= element["index"] <= page_start + 3 and P_INVALID_PAGE_FIRST_PARA.nexts(element["text"]):
            return True
        # 侧栏的以|开头，后面多个单字符的元素块
        # http://************:55647/#/project/remark/407004?treeId=3980&fileId=113308&schemaId=18&projectId=17&schemaKey=C2.3
        if P_INVALID_PARA.nexts(clean_txt(element["text"], remove_cn_text=True)):
            return True
        if skip_chinese and self.is_chinese_elt(element):
            return True
        if skip_tbl_unit and self.is_table_unit(element):
            return True
        if skip_merged and is_merged_elt(element):
            return True
        return self.is_continued_title(element)

    def is_background_image(self, element: dict, outline_left=None, outline_right=None):
        """
        判断图片是否为页面的第一个元素块，或者为页面的背景图片(TODO)
        todo：考虑用图片outline判断是背景图 http://************:55647/#/project/remark/264708?treeId=20188&fileId=70156&schemaId=15&projectId=17&schemaKey=B74
        todo: 考虑页面最后一个元素块是背景图
        """
        if element["class"] != PDFInsightClassEnum.IMAGE.value:
            return False
        # 1. 图片在页面的最左，最右，最上或者最下
        left, top, right, bottom = element["outline"]
        if left == 0 or top == 0:
            return True
        if page_info := get_keys(self.data, ["pages", element["page"]]):
            page_right, page_bottom = page_info["size"]
            # http://************:55647/#/project/remark/258128?treeId=7925&fileId=68840&schemaId=15&projectId=17&schemaKey=B63.1&page=80 index=1548
            if right == page_right or bottom == page_bottom:
                return True
        # 2. 图片不在基准元素块左右范围内
        if outline_left and right < outline_left:
            # http://************:55647/#/project/remark/236280?treeId=38044&fileId=67002&schemaId=15&projectId=38044&schemaKey=B63.1&page=67 index=830
            return True
        if outline_right and left > outline_right:
            return True
        if not (page_elements := self.page_element_dict.get(element["page"], None)):
            return False
        # 3. 图片是页面的第一个图片
        return page_elements[0].index == element["index"]

    def is_chinese_elt(self, element):
        """
        是否为中文元素块，考虑分栏页面左下角为英文，右上角为中文的场景
        注意：使用该函数需要确保element是一个paragraph
        """
        # 只有一栏的文档，认为都是英文元素块
        # http://************:55647/#/project/remark/265399?treeId=8641&fileId=70294&schemaId=18&projectId=8641&schemaKey=C2.3&page=106 index  1371
        if element["class"] != "PARAGRAPH" or len(self.page_columns[element["page"]]) <= 1:
            return False
        text = element["text"]
        if not P_CHNS.search(text):
            # 未包含中文字符
            return False
        merged_indices = get_keys(element, ["page_merged_paragraph", "paragraph_indices"])
        if merged_indices and element["index"] == merged_indices[0]:
            # 跨页/跨栏的第一个元素块，需要排除其他元素块文本后判断
            for index in merged_indices[1:][::-1]:
                _, next_elem = self.find_element_by_index(index)
                text = text[: (len(text) - len(next_elem.get("text", "")))]
            # 排除其他元素后无中文
            if not P_CHNS.search(text):
                return False
        # 排除非中英文字符后，中文占比>=20%
        text = P_NON_CH_EN.sub("", text)
        if not text:
            return False
        return len(P_CHNS.findall(text)) / len(text) >= 0.2

    def is_table_unit(self, element: dict):
        """
        判断元素块是否为表格的单位
        注意：使用该函数需要确保element是一个paragraph
        """
        try:
            next_type, _ = self.find_element_by_index(element["index"] + 1)
            return next_type == PDFInsightClassEnum.TABLE.value and P_TBL_UNIT.nexts(element["text"])
        except IndexError:
            return False

    def is_continued_title(self, element: dict):
        """
        判断元素块是否为页首要跳过的章节： 带continued关键词，或者为一级目录
        """
        if element and element["index"] in self.syllabus_reader.elt_syllabus_dict:
            return False
        text = clean_title(element.get("text")).upper()
        return (
            bool(P_CONTINUED.search(text))
            or text in {s["title"] for s in self.root_syllabuses}
            or text in self.page_chapter_from_first_para.values()
            # http://************:55647/#/project/remark/257968?treeId=8163&fileId=68808&schemaId=15&projectId=17&schemaKey=B63.1&page=124 元素块=1253
            # 不跳过root章节的开始元素
            or (
                element["page"] not in self.page_chapter_from_contents
                and any(s["title"].startswith(text) or s["title"].endswith(text) for s in self.root_syllabuses)
            )
        )

    @cached_property
    def content_pages(self):
        return {p_page.page_in_content: page for page, p_page in self.page_dict.items()}


class PdfinsightSyllabus:
    def __init__(self, syllabuses):
        self.syllabuses = syllabuses

    @cached_property
    def syllabus_dict(self):
        return {syl["index"]: syl for syl in self.syllabuses}

    @cached_property
    def elt_syllabus_dict(self):
        return {syl["element"]: syl for syl in self.syllabuses if "element" in syl}

    def is_syllabus_elt(self, element):
        return element.get("index") in self.elt_syllabus_dict

    def find_by_elt_index(self, index):
        """Unique element index"""
        sylls = [s for s in self.syllabuses if is_in_range(index, s["range"])]
        return sorted(sylls, key=lambda s: s["index"])

    def full_syll_path(self, syll):
        sylls = []
        cursor = syll
        while cursor:
            sylls.insert(0, cursor)
            cursor = self.syllabus_dict.get(cursor["parent"])
        return sylls

    def get_root_syllabus(self, syllabus):
        parent = self.syllabus_dict.get(syllabus["parent"])
        if not parent:
            return syllabus
        return self.get_root_syllabus(parent)

    def find_by_index(self, index):
        syll = self.syllabus_dict.get(index)
        return self.full_syll_path(syll)

    def find_by_pattern(
        self,
        patterns: List[Union[Pattern, str]],
        candidates: List[int] | None = None,
        order_by="index",
        reverse=False,
        clean_func=None,
    ):
        """
        patterns 支持 正则、字符串 两种模式：
        正则：正则匹配
        字符串：相似度超过 60%
        """

        def match(pattern: Union[Pattern, str], name: str):
            if isinstance(pattern, re.Pattern):
                return pattern.search(name)
            if isinstance(pattern, str):
                return SequenceMatcher(None, pattern, name).ratio() > 0.6
            return False

        if not patterns:
            return []
        res = []
        head_p, *tails_p = patterns
        if not candidates:
            syllabuses = list(self.syllabus_dict.values())
        else:
            syllabuses = [self.syllabus_dict[i] for i in candidates if i in self.syllabus_dict]
        for syllabus in sorted(syllabuses, key=lambda x: x[order_by], reverse=reverse):
            cleaned_title = clean_func(syllabus["title"]) if clean_func else clean_txt(syllabus["title"])
            if not match(head_p, cleaned_title):
                continue
            if not tails_p:
                # 只有一个pattern取当前章节
                res.append(syllabus)
            elif not syllabus["children"]:
                continue
            else:
                # 多个pattern说明是按父子关系严格匹配, 取后续子章节
                tails = self.find_by_pattern(tails_p, candidates=syllabus["children"], clean_func=clean_func)
                if tails:
                    res.extend(tails)
        return res

    def find_by_clear_title(self, title, order_by="index", reverse=False, multi=False, equal_mode=False):
        res = []
        for syl in sorted(self.syllabus_dict.values(), key=lambda x: x[order_by], reverse=reverse):
            condition = (
                clear_syl_title(syl["title"]) == title
                if equal_mode
                else re.search(title, clear_syl_title(syl["title"]))
            )
            if condition:
                res.append(syl)
                if not multi:
                    break
        return res

    def find_sylls_by_name(self, names, candidates=None):
        res = []
        name = names[0]
        if not candidates:
            candidates = sorted(self.syllabus_dict.keys())
        for syll_idx in candidates:
            syll = self.syllabus_dict[syll_idx]
            if name == syll["title"]:
                res.append(syll)
                if len(names) > 1:
                    tails = self.find_sylls_by_name(names[1:], candidates=syll["children"])
                    if not tails:
                        return []
                    res.extend(tails)
        return res

    def recalc_syllabus_range(self, syllabus: dict) -> list:
        """
        章节目录结束index错误，重新计算
        http://************:55647/#/project/remark/233018?treeId=24500&fileId=66458&schemaId=15&projectId=17&schemaKey=B76
        http://************:55647/#/project/remark/233197?treeId=13539&fileId=66488&schemaId=15&projectId=17&schemaKey=B76&page=190
        """
        start_idx, end_idx = syllabus["range"]
        # 1. 不符合正则的章节不处理
        first_title = syllabus["title"]
        if not (matched := P_FOLLOW_CHAPTER_PREFIX.search(first_title)):
            return [start_idx, end_idx]
        first_group = matched.groupdict()
        # 2. 结束元素块如果格式和当前一致，说明范围正确，直接返回
        end_title = self.elt_syllabus_dict.get(end_idx, {}).get("title")
        if end_title and self.relation_of_2_syllabuses(first_title, end_title, first_group=first_group) == 1:
            return [start_idx, end_idx]
        # 3. 章节范围内若存在兄弟章节，则兄弟章节作为结束pos；章节范围之后章节，若其中存在和当前格式一致，序号+1的章节，作为结束pos
        for elem_idx, syll in sorted(self.elt_syllabus_dict.items(), key=lambda x: x[0]):
            if elem_idx <= start_idx + 1 or P_CONTINUED.search(syll["title"]):
                continue
            relation = self.relation_of_2_syllabuses(first_title, syll["title"], first_group=first_group)
            if relation == 0:
                break
            if relation == 1:
                return [start_idx, elem_idx]
        return [start_idx, end_idx]

    @staticmethod
    def relation_of_2_syllabuses(
        first_title: str, next_title: str, first_group: dict | None = None, distance: int = 1
    ) -> int:
        """
        判断两个章节之间的关系
        Return:
            1 - 是兄弟章节
            0 - 是堂兄弟章节（例如，都是(a) 开头）
            -1 - 是无法判断关系的章节
        """

        if not first_group:
            if not (matched := P_FOLLOW_CHAPTER_PREFIX.search(first_title)):
                return -1
            first_group = matched.groupdict()
        if not (next_matched := P_FOLLOW_CHAPTER_PREFIX.search(next_title)):
            return -1
        next_group = next_matched.groupdict()
        for key in (
            "prefix",
            "suffix",
        ):
            if first_group[key] != next_group[key]:
                return -1

        if (value := first_group["num"]) and (next_value := next_group["num"]):
            if value == next_value:
                return 0
            if int(next_value) - int(value) <= distance:
                return 1
        if (value := first_group["roman"]) and (next_value := next_group["roman"]):
            if value.upper() == next_value.upper():
                return 0
            if roman_to_int(next_value) - roman_to_int(value) <= distance:
                return 1
        if (value := first_group["word"]) and (next_value := next_group["word"] or next_group["roman"]):
            if value.lower() == next_value.lower():
                return 0
            if len(next_value) == 1 and ord(next_value.lower()) - ord(value.lower()) <= distance:
                return 1
            # 字母i, v, x可能会被识别为word
            if (
                len(next_value) > 1
                and value.lower() in ("i", "v", "x")
                and roman_to_int(next_value) - roman_to_int(value) <= distance
            ):
                return 1
        return -1

    @classmethod
    def syl_outline(
        cls,
        syllabus,
        pdfinsight,
        include_title=False,
        ignore_pattern=None,
        only_before_first_chapter=False,
        include_shape=False,
        only_first=False,
        break_para_pattern=None,
        include_sub_title=True,
        break_when_table=False,
        ignore_syllabus_pattern: PatternCollection = None,
        break_function: Callable = None,
    ):
        """
        获取章节外框
        """

        def _filter_elements(idx_from, idx_to):
            nonlocal elements, element_indexes, ignore_syllabus_indexes
            for e_idx in range(idx_from + 1, idx_to):
                elt_type, elt = pdfinsight.find_element_by_index(e_idx)
                if not elt:
                    continue
                if ignore_syllabus_indexes and elt.get("syllabus") in ignore_syllabus_indexes:
                    # 跳过忽略的子章节
                    continue
                if callable(break_function) and break_function(pdfinsight, elt):
                    break
                if (
                    not include_sub_title
                    and elt.get("syllabus") in syllabus["children"]
                    and elt["index"] in pdfinsight.syllabus_reader.elt_syllabus_dict
                ):
                    # 略过子章节标题
                    continue
                if elt_type in ("SHAPE", "IMAGE") and not include_shape:
                    continue
                if elt_type == "TABLE" and break_when_table:
                    break
                clean_element_text = clean_txt(elt.get("text", ""))
                if not clean_element_text and not (
                    include_shape and elt_type == "TABLE"
                ):  # 使用了include_shape带判断是否包含表格 需要细分的时候再修改
                    continue
                if break_para_pattern and break_para_pattern.patterns and break_para_pattern.nexts(clean_element_text):
                    break
                if not is_valid_first_ele_in_page(elt, pdfinsight):
                    continue
                if elt and elt_type not in ["PAGE_HEADER", "PAGE_FOOTER"] and elt["index"] not in element_indexes:
                    if ignore_pattern and ignore_pattern.patterns and ignore_pattern.nexts(clean_element_text):
                        continue
                    elements.append(elt)
                    if elt.get("page_merged_paragraph"):
                        for elt_index in elt["page_merged_paragraph"]["paragraph_indices"]:
                            element_indexes.add(elt_index)
                    else:
                        element_indexes.add(elt["index"])

        ignore_syllabus_indexes = set()
        if ignore_syllabus_pattern:
            ignore_syllabus_indexes = {
                syllabus["index"]
                for pattern in ignore_syllabus_pattern.dynamic_patterns()
                for syllabus in pdfinsight.find_sylls_by_pattern([pattern])
            }

        elements = []
        start, end = syllabus["range"]
        if only_before_first_chapter:
            children = syllabus["children"]
            if children:
                first_children = children[0]
                first_children_syllabus = pdfinsight.syllabus_dict[first_children]
                end = first_children_syllabus["element"]
            elif only_first:
                end = start + 2
        if include_title:  # 是否包含章节标题
            elt_type, elt = pdfinsight.find_element_by_index(start)
            if elt_type == "PARAGRAPH":
                elements.append(elt)
        element_indexes = set()
        _filter_elements(start, end)
        if not elements and not only_before_first_chapter:
            # 如果首段被略过，再取下一段，尽可能保证能在当前章节中取到一段
            try:
                _filter_elements(start, syllabus["range"][-1] + 1)
            except IndexError:
                pass
        return cls.elements_outline(elements, pdfinsight)

    @classmethod
    def elements_outline(cls, elements, pdfinsight=None, ignore_column_position=False) -> list[dict]:
        """
        获取elements的外框 返回的外框中有可能包含跨页元素块，第二页的元素块中仅包含 box信息 不包含具体的element
        """
        element_by_index = defaultdict(list)
        for element in elements:
            element_by_index[element["index"]].append(element)
        elements = sorted([elms[0] for elms in element_by_index.values()], key=lambda x: x["index"])
        page_elements = classify_by_page(elements, pdfinsight)
        page_boxes = []
        for page, elts in page_elements.items():
            # 排除[0, 0, 1.0483, 807] 类似的元素块
            elts = [e for e in elts if not (e["outline"][0] == 0 and e["outline"][1] == 0)]
            # 排除 [593.9215, 0, 595, 368.1877] 的图片
            elts = [e for e in elts if not (e["outline"][1] == 0 and e["class"] == "IMAGE")]
            # 排除 完全没有英文的段落
            elts = [e for e in elts if (not is_para_elt(e) or has_english_chars(e) or e.get("type") == "fake_element")]
            # 补充跨页元素块的index
            if len(elts) >= 2 and elts[0]["type"] == "fake_element":
                elts[0]["index"] = elts[1]["index"] - 1
            # 按照index是否连续分组
            ele_dict = {e.get("index", 0): e for e in elts}
            indexes = list(ele_dict.keys())
            element_groups = []
            for _, sub_idx in groupby(enumerate(indexes), lambda x: x[1] - x[0]):
                element_group = []
                for index in list(map(itemgetter(1), sub_idx)):
                    element_group.append(ele_dict[index])
                element_groups.append(element_group)
            # 按照 position 分组
            group_elements = chain.from_iterable(cls.group_elements_by_position(group) for group in element_groups)
            for contiguous_elts in group_elements:
                # 根据pdfinsight中columns信息判断 双栏的文件排除中文
                column_position, is_left_chinese = None, None
                if not ignore_column_position and pdfinsight:
                    column_position, is_left_chinese = get_page_column(pdfinsight, page, contiguous_elts)

                if column_position:
                    contiguous_elts = [
                        elt
                        for elt in contiguous_elts
                        if any(
                            (
                                elt["outline"][0] >= column_position
                                if is_left_chinese
                                else elt["outline"][0] <= column_position,
                                elt["class"] in ("TABLE", "IMAGE", "SHAPE", "INFOGRAPHIC"),
                                not has_chinese_chars(elt),  # 有中文
                            )
                        )
                    ]
                if not contiguous_elts:
                    continue
                boxes = cls.gen_contiguous_elts_outline(
                    contiguous_elts, page, column_position, is_left_chinese, page_boxes
                )
                page_boxes.extend(boxes)

        return page_boxes

    @classmethod
    def indices_of_merged_para(cls, paragraph):
        """
        获取连续段落的索引组，没有返回None，有则返回一个索引列表
        """
        if not paragraph.get("page_merged_paragraph"):
            return None
        return paragraph["page_merged_paragraph"].get("paragraph_indices")

    @classmethod
    def group_elements_by_position(cls, contiguous_elts):
        group_by_position = defaultdict(list)
        for contiguous_elt in contiguous_elts:
            position_key = tuple(contiguous_elt["position"])
            group_by_position[position_key].append(contiguous_elt)
        group_by_position = dict(sorted(group_by_position.items(), key=lambda x: x[0]))
        group_data = []
        if len(group_by_position) == 1:
            group_data = [group_by_position[list(group_by_position.keys())[0]]]
        for left, right in pairwise(group_by_position):
            if group_by_position[left]:
                left_bottom_element = group_by_position[left][-1]
                if page_merged_paragraph := left_bottom_element.get("page_merged_paragraph", {}):
                    right_top_element = group_by_position[right][0]
                    paragraph_indices = page_merged_paragraph.get("paragraph_indices", [])
                    if right_top_element["index"] == paragraph_indices[-1]:
                        group_by_position[left].pop()
                        group_by_position[right].pop(0)
                        if group_by_position[left]:
                            group_data.append(group_by_position[left])
                        group_data.append([left_bottom_element, right_top_element])
                        if group_by_position[right]:
                            group_data.append(group_by_position[right])
                else:
                    group_data.append(group_by_position[left])
                    group_data.append(group_by_position[right])
            elif group_by_position[right]:
                group_data.append(group_by_position[right])
        return group_data

    @classmethod
    def is_same_page_continued(cls, paragraphs):
        """
        判断段落组是否为同一页左下+右上的连续段落
        """
        return (
            len(paragraphs) == 2
            and paragraphs[0].get("page") == paragraphs[1].get("page")
            and any(para.get("continued") for para in paragraphs)
        )

    @classmethod
    def gen_contiguous_elts_outline(cls, contiguous_elts, page, column_position, is_left_chinese, all_boxes):
        page_boxes = []
        non_para_elt_idxes = [idx for idx, elt in enumerate(contiguous_elts) if elt["class"] != "PARAGRAPH"]
        cursor = 0
        while cursor < len(contiguous_elts):
            non_para_idx = non_para_elt_idxes.pop(0) if non_para_elt_idxes else len(contiguous_elts)
            divided_ele = contiguous_elts[non_para_idx] if non_para_idx != len(contiguous_elts) else None
            paras = contiguous_elts[cursor:non_para_idx]
            if paras:
                if cls.is_same_page_continued(paras):
                    # http://************:55647/#/project/remark/244305?treeId=6307&fileId=67311&schemaId=28&projectId=17&schemaKey=H%28b%29-Frequency%20of%20review%20and%20the%20period%20covered
                    # 同一页文档分左右两块，且paras中一个段落在左下角，另一个段落在右上角，这里只取左下角元素即可
                    page_boxes.extend(
                        [
                            {
                                "page": page,
                                "outline": para["outline"],
                                "text": element_text(para),
                                "elements": [para],
                            }
                            for para in paras
                            if not para.get("fragment")
                        ]
                    )
                else:
                    page_boxes.append(
                        {
                            "page": page,
                            "outline": get_page_para_outline(paras, column_position, is_left_chinese),
                            "text": "\n".join(
                                [element_text(ele) for ele in paras if not ele.get("fragment")]
                            ),  # 添加完整的段落text
                            "elements": [ele for ele in paras if ele.get("type") != "fake_element"],
                        }
                    )
            if divided_ele and divided_ele.get("type") != "fake_element":  # 非段落元素画框
                page_boxes.append(
                    {
                        "page": page,
                        "outline": divided_ele["outline"],  # 使用自己的outline
                        "text": "\n".join(
                            [element_text(divided_ele)] if not divided_ele.get("fragment") else []
                        ),  # 添加完整的段落text
                        "elements": [divided_ele],
                    }
                )
            cursor = non_para_idx + 1

        if contiguous_elts and contiguous_elts[0].get("fragment"):
            # contiguous_elts 仅有跨页或者跨栏段落的第二段 此时需要补充整个段落的文本
            if not all_boxes and page_boxes:
                page_box = page_boxes[0]  # 只需要看第一个框
                if not page_box.get("text") and page_box.get("elements"):
                    merged_element = page_box["elements"][0]
                    page_merged_paragraph = merged_element.get("page_merged_paragraph", {})
                    page_box["text"] = page_merged_paragraph.get("text", "")
        return page_boxes


class PdfinsightPage:
    def __init__(self, page, pdfinsight, is_catalog=False):
        self.page = page
        self.pdfinsight = pdfinsight
        self.is_catalog = is_catalog

    @cached_property
    def element_index_range(self):
        indices = [e["index"] for e in self.origin_elements]
        return [min(indices, default=0), max(indices, default=0)]

    @cached_property
    def elements(self):
        elements = []
        for item in self.pdfinsight.page_element_dict.get(self.page) or []:
            _, ele = self.pdfinsight.find_element_by_index(item.index)
            if not ele:
                continue
            elements.append(ele)
        return elements

    @cached_property
    def header_elements(self):
        return [e for e in self.origin_elements if e["class"] == PDFInsightClassEnum.PAGE_HEADER.value]

    @cached_property
    def footer_elements(self):
        return [e for e in self.origin_elements if e["class"] == PDFInsightClassEnum.PAGE_FOOTER.value]

    @cached_property
    def origin_elements(self):
        return [e.data for e in self.pdfinsight.page_element_dict.get(self.page) or []]

    @cached_property
    def origin_para_elements(self):
        return [e for e in self.origin_elements if is_para_elt(e)]

    @cached_property
    def like_para_elements(self):
        """
        注意：这里返回的段落，都是原始段落，不带page_merged文本
        """
        return sorted(self.header_elements + self.origin_para_elements + self.footer_elements, key=lambda x: x["index"])

    @cached_property
    def page_in_content(self):
        # 返回当前页面文本中的页码
        if len(self.like_para_elements) < 2:
            return None
        footer_elements = self.like_para_elements[-1:-3:-1]  # 仅仅取倒数前两个元素块
        page = self.get_page(footer_elements)
        if page:
            return page
        header_elements = self.like_para_elements[:2]
        page = self.get_page(header_elements)
        if page:
            return page
        return None

    @staticmethod
    def get_page(elements):
        for element in elements:
            element_text = element.get("text")
            if not element_text:
                continue
            # stock code: 00013 2024
            # 按行拆分chars
            if lines := split_chars(element.get("chars") or []):
                if matcher := PAGE_PATTERN.nexts(clean_txt("".join(x.get("text") or "" for x in lines[-1]))):
                    page = matcher.groupdict().get("dst", None)
                    if page:
                        return int(page)
            matcher = PAGE_PATTERN.nexts(clean_txt(element_text))
            if not matcher:
                continue
            page = matcher.groupdict().get("dst", None)
            if page:
                return int(page)
        return None

import collections
import logging
import re
from collections import defaultdict
from dataclasses import asdict, dataclass, field
from functools import cached_property
from itertools import chain, combinations
from typing import Iterable, Literal

import peewee
import psycopg2

from remarkable.common.common import (
    get_first_key,
    get_first_value,
    get_keys,
    is_para_elt,
    is_paragraph_elt,
    is_table_elt,
)
from remarkable.common.common_pattern import (
    P_C_FOLLOW_PREFIX,
    P_CONTINUED,
    R_MIDDLE_DASH,
    R_MIDDLE_DASHES,
)
from remarkable.common.constants import PDFInsightClassEnum, TableType
from remarkable.common.element_util import PREP_WORDS
from remarkable.common.pattern import MatchMulti, NeglectPattern, PatternCollection
from remarkable.common.util import P_EN, clean_txt, has_english_chars, is_in_range
from remarkable.db import pw_db
from remarkable.models.file_share_meta import FileShareMeta, GroupInfo, GroupItem
from remarkable.models.new_file import NewFile
from remarkable.models.new_question import NewQuestion
from remarkable.pdfinsight.chapter_fixer import <PERSON>Chapter
from remarkable.pdfinsight.parser import parse_table
from remarkable.pdfinsight.reader import <PERSON>d<PERSON><PERSON><PERSON><PERSON><PERSON>
from remarkable.pdfinsight.reader_util import UnifiedParaElement
from remarkable.predictor.common_pattern import (
    P_SHARE_INCENTIVE,
    P_YEAR,
    R_INCENTIVE_ABBREV,
)
from remarkable.predictor.hkex_predictor.model_util import (
    find_as_follow_paras,
    find_as_follow_tables,
)
from remarkable.predictor.hkex_predictor.pattern import (
    P_EQUITY_CHAPTER,
    P_LIST_RULE_IGNORE_CHAPTERS,
)
from remarkable.predictor.hkex_predictor.schemas.pattern import (
    P_CG_CHAPTER,
    P_DR_CHAPTER,
    P_NOTE_CHAPTER,
    P_ONLY_DATE,
    P_ONLY_NUM,
    R_AWARD_KEYWORD_LIST,
    R_DATES,
    R_OPTION_KEYWORD,
)
from remarkable.predictor.share_group_gpt import (
    DEFAULT_GROUP_KEY_MAP,
    P_ADOPTED_SCHEME,
    P_IS_SEN,
    P_RIGHT_QUOTES,
    P_SCHEME_TITLE,
    P_VALID_FOR_GPT,
    R_ADOPT,
    SPECIAL_GROUP_KEYS,
    Scheme,
    get_scheme_results,
)
from remarkable.predictor.share_group_utils import (
    P_NEG_PARENT_CHAPTERS,
    P_SCHEME_NAME_REPLACER,
    R_CHAPTER_PREFIX,
    R_PLAN,
    R_QUOTES,
    R_TYPE_PREFIX,
    chapter_title_style,
    extract_group_key,
    get_elem_text,
    group_children_by_style,
    guess_share_type,
    guess_tbl_share_type,
    is_contiguous_titles,
    is_same_key,
    is_sub_range,
    prefix_to_int,
    same_adopted_days,
    standardize_group_key,
    table_headers,
    trans_indices_to_tuple,
)
from remarkable.predictor.utils import (
    get_crude_elements,
    get_jura21_helper_and_ai_schemes,
    get_share_info,
    get_subsidiaries_group,
    subtract_keys,
)
from remarkable.services.question import ctx_debug_share_group

logger = logging.getLogger(__name__)

# 根据以下正则提取章节标题
P_TITLE_REPLACE_REGS = {
    re.compile(
        rf"{R_CHAPTER_PREFIX}(purpose\s*of\s*|.+(issued?|life|participants?|grante?[ed]?)\s*under\s*)(the\s*)?", re.I
    ): "",
    P_CONTINUED: "",
    re.compile(rf"{R_CHAPTER_PREFIX}(the\s*)?", re.I): "",
    # http://************:55647/#/project/remark/264823?treeId=42555&fileId=70179&schemaId=15&projectId=17&schemaKey=B76 P206
    re.compile(r"[（(]equity-settled[)）]", re.I): "",
    # http://************:55647/#/project/remark/257948?treeId=11015&fileId=68804&schemaId=15&projectId=17&schemaKey=B76
    re.compile(r"^.*shares\s*held\s*for\s*(?P<scheme>.+scheme)", re.I): r"\g<scheme>",
    # http://************:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=17&schemaKey=B74
    re.compile(r"amended\s*and\s*restated\s*", re.I): "",
    re.compile(r"amended\s*|revised\s*|restated\s*", re.I): "",
    # http://************:55647/#/project/remark/235650?treeId=4907&fileId=66897&schemaId=15&projectId=4907&schemaKey=B64
    # http://************:55647/#/project/remark/236394?treeId=10381&fileId=67021&schemaId=15&projectId=10381&schemaKey=B63.1&page=94
    re.compile(r"(issued|adopted)?\s*(of|by)\s*.*company.*$", re.I): "",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3573#note_471667
    # http://************:55647/#/project/remark/235806?treeId=38071&fileId=66923&schemaId=15&projectId=17&schemaKey=B77
    re.compile(r"[（(]\d{4}[)）]$", re.I): "",
    # http://************:55647/#/project/remark/257903?treeId=2779&fileId=68795&schemaId=15&projectId=17&schemaKey=B80
    re.compile(rf"{R_CHAPTER_PREFIX}(?P<scheme>.+?)\s+{R_MIDDLE_DASH}\s+.+$", re.I): r"\g<scheme>",
    # http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17&schemaKey=B80
    re.compile("(?P<scheme>.+)NOTE$", re.I): r"\g<scheme>",
    # http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17&schemaKey=B80&page=277
    re.compile(r"(?P<scheme>^share\s*(option|award)\s*scheme)\s*[IVX]{1,4}$", re.I): r"\g<scheme>",
}
P_CHAPTER_PREFIX = re.compile(rf"{R_CHAPTER_PREFIX}(the\s*)?", re.I)
# 以单词或)结尾的标题，可判断为章节
P_WORD_END = re.compile(r"([a-z]|[）)]|\d)$", re.I)
# 一级标题为share option scheme或者share award scheme，且无子章节，则按照incentive处理
# http://************:55647/#/project/remark/231684?treeId=4265&fileId=66236&schemaId=15&projectId=17&schemaKey=B82 share award scheme章节包含了option
P_OPTION_OR_AWARD_CHAPTER = MatchMulti.compile(
    rf"{R_CHAPTER_PREFIX}share\s*option\s*scheme$", rf"{R_CHAPTER_PREFIX}share\s*award\s*scheme$", operator=any
)
P_AWARD_CHAPTER = re.compile(rf"{R_CHAPTER_PREFIX}share\s*award\s*scheme$", re.I)
P_GROUP_OPTION_KEYWORD = re.compile(R_OPTION_KEYWORD, re.I)
# http://************:55647/#/project/remark/233455?treeId=9357&fileId=66531&schemaId=15&projectId=9357&schemaKey=B94.1&page=151
P_GROUP_AWARD_KEYWORD = MatchMulti.compile(
    *R_AWARD_KEYWORD_LIST, r"equity\s*settled\s*share-based\s*transactions", r"share\s*grant\s*scheme", operator=any
)
# 标题中既包括option又包括award，按照incentive处理
P_OPTION_AWARD_TITLE = MatchMulti.compile(R_OPTION_KEYWORD, P_GROUP_AWARD_KEYWORD, operator=all)
P_AS_INCENTIVE_TITLE = MatchMulti.compile(
    P_SHARE_INCENTIVE,
    P_OPTION_AWARD_TITLE,
    # http://************:55647/#/project/remark/233018?treeId=24500&fileId=66458&schemaId=15&projectId=17&schemaKey=B82
    # http://************:55647/#/project/remark/261058?treeId=37977&fileId=69426&schemaId=15&projectId=17&schemaKey=B76
    r"\bequity\s*plans?$",
    # http://************:55647/#/project/remark/251248?treeId=13914&fileId=68664&schemaId=15&projectId=17&schemaKey=B80&page=31
    rf"share\s*{R_PLAN}s",
    # http://************:55647/#/project/remark/235903?treeId=24438&fileId=66939&schemaId=15&projectId=17&schemaKey=B64
    r"RESTRICTED\s*SHARE\s*UNIT\s*[(（]“RSU”[)）]\s*SCHEMES$",
    operator=any,
)
# 标题中既有options又有rsus且章节下只有一段话提到了detail，跳过这种章节
P_OPTIONS_RSUS = MatchMulti.compile("options", r"rsus|share\s*awards", operator=all)
# 标题中包含逗号或者and或者schemes
P_MULTI_SCHEME = MatchMulti.compile(
    P_OPTION_AWARD_TITLE,
    r"\band\b(?!\s*(trust|restated))",
    r"[,，/]|others$",
    rf"\b({R_PLAN}|share|rsu|resticted\s*(share|stock)\s*unite?|share\s*(option|award))[(（]?s[)）]?$",
    operator=any,
)
# 符合这种条件的章节一般需要遍历子章节或段落
P_MULTI_SCHEME_TITLES = {
    "option": MatchMulti.compile(
        rf"{R_CHAPTER_PREFIX}share\s*option\s*{R_PLAN}(\s*of\s*the\s*company)?$",
        r"share\s*options",
        rf"option\s*{R_PLAN}s$",
        operator=any,
    ),
    "award": MatchMulti.compile(
        rf"award\s*{R_PLAN}s$",
        rf"{R_CHAPTER_PREFIX}share\s*award\s*{R_PLAN}(\s*of\s*the\s*company)?$",
        rf"{R_CHAPTER_PREFIX}Restricted\s*(Share|Stock)\s*Unite?s?\s*{R_PLAN}?$",
        rf"{R_CHAPTER_PREFIX}Restricted\s*share$",
        rf"{R_CHAPTER_PREFIX}rsus?$",
        operator=any,
    ),
}

# 当句子中匹配到以下内容，说明默认分组不应该被忽略，例如：(the “share option scheme”)
P_EXACT_DEFAULT_KEY = re.compile(
    rf"[(（\[](the\s*)?{R_QUOTES}(?P<scheme>(share\s*(option|award)|rsu|restricted\s*(Share|Stock)\s*Unite?s?)\s*{R_PLAN}){R_QUOTES}[)）\]]",
    flags=re.I,
)

# 任一父章节满足以下正则，则跳过
P_IGNORE_PARENTS = MatchMulti.compile(
    # http://************:55647/#/project/remark/251242?treeId=8654&fileId=68663&schemaId=15&projectId=17&schemaKey=B80&page=180
    r"ACCOUNTING\s*POLICIES",
    # http://************:55647/#/project/remark/233802?treeId=3653&fileId=66589&schemaId=15&projectId=17&schemaKey=B63.1
    P_EQUITY_CHAPTER,
    # http://************:55647/#/project/remark/233167?treeId=4194&fileId=66483&schemaId=15&projectId=17&schemaKey=B82
    r"other\s*events",
    # TODO 后续按需看是否需要合并
    # http://************:55647/#/project/remark/257903?treeId=2779&fileId=68795&schemaId=15&projectId=17&schemaKey=B80&page=147
    r"movements\s*during\s*the\s*year",
    # http://************:55647/#/project/remark/257948?treeId=11015&fileId=68804&schemaId=15&projectId=17&schemaKey=B76 p48
    rf"Underlying\s*Shares\s*{R_MIDDLE_DASH}\s*Share\s*Options\s*and\s*Share\s*Awards",
    operator=any,
)

# 章节标题或者元素所在章节满足以下正则，则跳过
P_IGNORE_CHAPTERS = MatchMulti.compile(
    P_IGNORE_PARENTS,
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_461316
    r"^41.\s*share\s*option\s*schemes$",
    # http://************:55647/#/project/remark/257648?treeId=10363&fileId=68744&schemaId=15&projectId=17&schemaKey=B80&page=70
    r"\bcompany\s*and\s*its\s*subsidiaries",
    # # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B76
    # r"LONG\s*POSITION\s*IN\s*SHARE\s*OPTIONS\s*GRANTED\s*UNDER\s*THE\s*SHARE\s*OPTION\s*SCHEME\s*\d{4}\s*AND\s*SHARE\s*OPTION\s*SCHEME\s*\d{4}$",
    # http://************:55647/#/project/remark/233581?treeId=7494&fileId=66552&schemaId=15&projectId=17&schemaKey=B76&page=172
    r"reserve",
    # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_483826 文件66597
    P_ONLY_DATE,
    # http://************:55647/#/project/remark/231684?treeId=4265&fileId=66236&schemaId=15&projectId=17&schemaKey=B82&page=184
    P_ONLY_NUM,
    r"^\(([j-uw-z]|[vx][ivx]{0,3})\)",  # 一般章节中的分组不会太多，这里过滤从J开始的分组，遇到特例可以缩小范围，考虑罗马数字I和V
    # rf"^[(（]([a-z]|\d+|[ivx]{{1,4}})[）)].*(of|in|under|to)\s+the",
    # r"^The \d+ Share Award Scheme has been terminated",  # http://************:55647/#/project/remark/233958?treeId=5370&fileId=66615&schemaId=15&projectId=17
    # http://************:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=17&schemaKey=B76
    rf"\s+(of|in|under|to|for)\s+(the\s*)?(\b[AH]\b\s*)?(Restricted|RSU(?!s)|share(?!\s*(option|award)s)|\d{{4}}|{R_TYPE_PREFIX})",
    rf"\s+(of|in|under|to|for)\s+.*({R_PLAN}|incentive|{R_INCENTIVE_ABBREV})(?!s)$",
    r"\s+(of|in|under|for)\s*(the\s*)?share",
    # 包含这些关键词的子章节标题要跳过
    r"description|overview|number|maximum|source|scope|be\s*granted|Clawback|participant|purpose|period|life|date|term",
    r"arrangement|outstanding|movement|acceptance|payment|performance|price|administration|transfer|duration|vesting",
    # rf"{R_EN_MONTH}\s*\d{{4}}$",
    # http://************:55647/#/project/remark/233455?treeId=9357&fileId=66531&schemaId=15&projectId=9357&schemaKey=B94.1&page=101
    r"\bgranted\s*to\b",
    # http://************:55647/#/project/remark/231545?treeId=5126&fileId=66213&schemaId=15&projectId=17&schemaKey=B82&page=179
    # r"Interests\s*in\s*the\s*share|valuation\s*of\s*(the\s*)?share",
    # r"\s+(of|in|under)\s*(the\s*)?share(s|options)",
    P_CONTINUED,
    rf"[{R_MIDDLE_DASHES}:：;；$]$",
    r"(?<!\bLTD)(?<!\bCO)(?<!\binc)\.$",
    rf"^{R_CHAPTER_PREFIX}[(（][^）)(（]*[）)]$",
    *R_DATES,
    r"(for|during)\s*(\S+\s+){0,3}\s*(period|year|(\bfy)?\d{4})",
    # http://************:55647/#/project/remark/251230?treeId=6889&fileId=68661&schemaId=15&projectId=17&schemaKey=B74
    r"share\s*award\s*grants",
    # http://************:55647/#/project/remark/257948?treeId=11015&fileId=68804&schemaId=15&projectId=17&schemaKey=B76
    # r"share\s*capital\s*and\s*shares\s*held\s*for\s*SHARE\s*AWARD\s*SCHEME",
    operator=any,
)
P_IGNORE_PARAS = MatchMulti.compile(
    # http://************:55647/#/project/remark/258043?treeId=3543&fileId=68823&schemaId=15&projectId=17&schemaKey=B76
    # 详情参考某章节
    r"details.*\b(included\s*in|set\s*out)\b",
    operator=any,
)

# 章节下提到控股公司，则跳过该章节
# https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3573#note_483845 文件66597
P_HOLDER_COMPANY = MatchMulti.compile(r"\bholdco\b", r"\bhold(ing)?\s*company", operator=any)
# 判断是否为子公司表格
P_SUBSIDIARY_TABLE_NAME = MatchMulti.compile("principal|主要|重要", "subsidiaries|(子|附屬|附属)公司", operator=all)
# 判断scheme的章节标题是否为子公司
P_SUBSIDIARY_TITLE = NeglectPattern.compile(match=r"subsidiar(y|ies)", unmatch=r"company")
# 判断是否为子公司语句
P_SUBSIDIARY_COMPANY = PatternCollection(
    [
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4714#note_537186
        rf"([Tt]he\s*)?(?P<name>([A-Z][-_.a-z]*\s+){{3,}})([(（]{R_QUOTES}(?P<abbr>([A-Z][-_a-z]*\s*)+?){R_QUOTES}.*?[)）])?[^.]+\ba\s*subsidiary\s*of\s*the\s*([Cc]ompany|[Gg]roup)",
    ]
)
# 提取子公司名称
P_SUBSIDIARY_NAMES = [
    re.compile(r"^[，,]?[(（]?(?P<name>.+?)[,，]?\s+([(（]|\b(inc|limited|ltd|co)\.?\b)", re.I),
    re.compile(r"^[，,]?[(（](?P<name>[-a-z.\s]+)", re.I),
]
P_SUBSIDIARY_ALIAS = re.compile(rf"{R_QUOTES}(?P<alias>[-A-Z\s.]+){R_QUOTES}", re.I)
# 没有规律，日期也不同，只能根据配置合并的分组
NEED_MERGE_GROUP_KEYS = [
    # http://************:55647/#/project/remark/233197?treeId=13539&fileId=66488&schemaId=15&projectId=17&schemaKey=B82
    {"sharegrantscheme", "seniormanagementsharegrantscheme"}
]
R_ADOPTED_BY_SUBSIDIARY = rf"{R_ADOPT}(ed)?\s*by\s*%s\b|%s\s*([-\w]\s+){{0,5}}{R_ADOPT}"


# @dataclass
# class SyllabusGroup:
#     parent_indices: set[int]
#     syllabus: dict = None
#     keywords: set[str] = field(default_factory=set)
#     # page: int | None


@dataclass
class ElementInfo:
    syll_idx: int
    page: int
    # 元素块+表格所属的share type
    share_type: str | None
    # incentive表格所属的share type
    tbl_share_types: set[str]
    group_keys: set[str]
    skip_keys: set[str]
    all_indices: list[int]
    outline: list[float] | None = None


@dataclass
class ShareGroup:
    pdfinsight: PdfinsightReader
    share_type: Literal["option", "award"]
    helper_crude_elems: dict[str, list[dict]] = field(default_factory=dict)
    share_info: dict | None = None
    report_year: str = ""
    # 年报所属公司名称
    company: str = ""
    is_test: bool = False

    def __post_init__(self):
        self.report_year = self.report_year or self.helper_crude_elems.get("report_year") or ""
        self.company = (self.company or self.helper_crude_elems.get("company") or "").lower()

    @cached_property
    def company_words(self):
        return self.company.split()

    @property
    def default_group_keys(self):
        return DEFAULT_GROUP_KEY_MAP[self.share_type]

    @cached_property
    def capitalize_default_key(self):
        if self.share_type == "option":
            return "ShareOptionScheme"
        return "ShareAwardScheme"

    @property
    def neg_chapter_regs(self):
        return P_NEG_PARENT_CHAPTERS[self.share_type]

    @cached_property
    def aliases(self):
        """
        存储分组的别名，不同名称对应的可能是同一个分组，判断依据：
        1) 同一个章节下，章节内容只提取到一个关键词且与章节名称不同
        2) 在不同章节提取到不同的关键词，但生效日期是同一天或相邻天
        """
        return defaultdict(set)

    @cached_property
    def scheme_day_map(self) -> dict[str, set[str]]:
        """
        存储各分组的adoption date
        TODO scheme中带了年份，日期与年份不一致则删除
        """
        return defaultdict(set)

    @cached_property
    def day_scheme_map(self) -> dict[str, set[str]]:
        return defaultdict(set)

    @property
    def another_type(self) -> str:
        return "award" if self.share_type == "option" else "option"

    @cached_property
    def crude_schemes(self) -> dict[int, tuple[str, int]]:
        schemes = {}
        crude_answers = self.helper_crude_elems.get("option scheme") or []
        crude_answers.extend(self.helper_crude_elems.get("award scheme") or [])
        crude_answers.extend(self.helper_crude_elems.get("incentive-title") or [])
        for ans in crude_answers:
            if ans["element_type"] != PDFInsightClassEnum.PARAGRAPH.value:
                continue
            elt_index = ans["element_index"]
            _, element = self.pdfinsight.find_element_by_index(elt_index)
            # 合并行的第二行被识别为标题，需要取第一行
            # http://************:55647/#/project/remark/258043?treeId=3543&fileId=68823&schemaId=15&projectId=17&schemaKey=B63.1 元素块960,961
            if merged_paras := element.get("page_merged_paragraph"):
                elt_index = merged_paras["paragraph_indices"][0]
                text = merged_paras["text"]
            else:
                text = clean_txt(ans["text"])
            if not self.is_scheme_chapter_title(text):
                continue
            schemes[elt_index] = (text, ans.get("page") or 0)
        return schemes

    @cached_property
    def subsidiary_names(self) -> set[str]:
        """
        根据helper-> "sub company" 找子公司相关描述或者子公司表格，提取所有子公司名称
        """
        sub_names = set()
        for ans_elem in self.helper_crude_elems.get("sub company") or []:
            elem_index = ans_elem["element_index"]
            try:
                _, element = self.pdfinsight.find_element_by_index(elem_index)
            except IndexError:
                continue
            table = None
            if is_paragraph_elt(element, strict=True):
                text = clean_txt(element.get("text") or "", remove_cn_text=True)
                if matched := P_SUBSIDIARY_COMPANY.nexts(text):
                    name = matched.group("name")
                    sub_names.add(name)
                    if abbr := matched.group("abbr"):
                        sub_names.add(abbr)
                    logger.debug(f"subsidary [{name}]({abbr}): {text}")
                elif P_SUBSIDIARY_TABLE_NAME.search(text):
                    _, next_elem = self.pdfinsight.find_element_by_index(elem_index + 1)
                    if not is_table_elt(next_elem):
                        continue
                    table = parse_table(next_elem, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            elif is_table_elt(element):
                try:
                    _, prev_elem = self.pdfinsight.find_element_by_index(elem_index - 1)
                except IndexError:
                    continue
                if P_SUBSIDIARY_TABLE_NAME.search(prev_elem.get("text") or ""):
                    table = parse_table(element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
            else:
                continue
            if not table:
                continue
            name_col = 0
            for i, header in table.col_header_texts.items():
                header = clean_txt(header, remove_cn_text=True).lower()
                if "name" in header or "subsidiar" in header:
                    name_col = i
                    break
            for cell in table.cols[name_col]:
                if cell.rowidx == 0 or cell.dummy:
                    continue
                text = clean_txt(cell.text_no_superscript, remove_cn_text=True)
                if not text or P_CONTINUED.search(text) or "subsidiar" in text.lower():
                    continue
                if text.endswith((",", "，")):
                    text = text[:-1]
                if mat_alias := P_SUBSIDIARY_ALIAS.search(text):
                    sub_names.add(mat_alias.group("alias").strip())
                max_len_name = text if text[0].isupper() else ""
                for regex in P_SUBSIDIARY_NAMES:
                    if not (matched := regex.search(text)):
                        continue
                    sub_name = matched.group("name").strip()
                    if len(sub_name) > len(max_len_name):
                        max_len_name = sub_name
                    if self.is_valid_subsidiary_name(sub_name):
                        sub_names.add(sub_name)
                if not max_len_name:
                    continue
                sub_words = max_len_name.split()
                for i in range(len(sub_words)):
                    if i > 2:
                        break
                    if self.is_valid_subsidiary_name(sub_words[i]):
                        sub_names.add(sub_words[i])
                        break
        logger.debug(f"subsidiary names: {sub_names}")
        return sub_names

    def is_valid_subsidiary_name(self, name):
        if name.lower() in self.company:
            return False
        if len(name.split()) > 1 or (len(name) > 1 and all(w.isupper() for w in name)):
            return True
        name = name.lower()
        if (
            len(name) < 10
            or name
            in {
                "principal",
                "subsidiary",
                "subsidiaries",
                "group",
                "company",
                "beijing",
                "shanghai",
                "shenzhen",
                "chongqing",
                "hongkong",
            }
            or name.endswith(("zhou", "shi", "cheng", "jiang"))
        ):
            return False
        return True

    @cached_property
    def ai_alias_adop_days(self) -> dict[int, set[Scheme]]:
        result = defaultdict(set)
        ai_schemes = self.helper_crude_elems.get("ai_schemes") or {}
        for para_idx, schemes in ai_schemes.items():
            new_schemes = set()
            for scheme in schemes:
                if isinstance(scheme, dict):
                    # 单元测试预存结果格式为json
                    scheme = Scheme(**scheme)
                if not P_AS_INCENTIVE_TITLE.search(scheme.orig_name) and not self.is_current_type(scheme.orig_name):
                    continue
                if scheme.alias:
                    self.merge_alias_key(scheme.name, scheme.alias)
                new_schemes.add(scheme)
            if new_schemes:
                new_schemes = self.merge_schemes(new_schemes)
                result[int(para_idx)] = new_schemes
                logger.debug("%s: %s", para_idx, new_schemes)
        return result

    def merge_schemes(self, schemes: set[Scheme]) -> set[Scheme]:
        """
        合并同一个元素块下的scheme
        """
        if len(schemes) <= 1:
            return schemes
        pop_names = set()
        for base_scheme, scheme in combinations(schemes, 2):
            if base_scheme.name == scheme.name or not self.is_alias_same_key(base_scheme.name, scheme.name):
                continue
            self.merge_alias_key(base_scheme.name, scheme.name)
            if not base_scheme.day_str:
                base_scheme.day_str = scheme.day_str
            pop_names.add(scheme.name)
        if not pop_names:
            return schemes
        return {sch for sch in schemes if sch.name not in pop_names}

    def is_incentive(self, syllabus, is_child=False):
        """
        是否incentive章节
        """
        if self.is_special_incentive(syllabus, is_child):
            return True
        return bool(P_AS_INCENTIVE_TITLE.search(syllabus["title"]))

    def is_special_incentive(self, syllabus: dict, is_child=False):
        title = syllabus["title"]
        # 是子章节，或者同级章节中有其他share award 或share option章节，则不走incentive逻辑
        if is_child or not P_OPTION_OR_AWARD_CHAPTER.search(title):
            return False
        if syllabus["parent"] == -1:
            return True
        # notes章节的share award/option scheme不当作incentive
        parent_syllabus = self.pdfinsight.syllabus_dict[syllabus["parent"]]
        if P_NOTE_CHAPTER.search(parent_syllabus["title"]):
            return False
        for child_idx in parent_syllabus["children"]:
            if child_idx == syllabus["index"]:
                continue
            child_title = clean_txt(self.pdfinsight.syllabus_dict[child_idx]["title"])
            if not P_SCHEME_TITLE.search(child_title):
                continue
            if P_OPTION_OR_AWARD_CHAPTER.search(child_title) or (
                not P_MULTI_SCHEME.search(child_title) and P_MULTI_SCHEME_TITLES[self.share_type].search(child_title)
            ):
                return False
            if self.is_multi_shares_chapter(child_title):
                continue
            return False
        return True

    def is_current_type(self, text, strict=False, share_type=None):
        """
        判断章节标题是否属于当前share_type
        """
        share_type = share_type or self.share_type
        if share_type == "option":
            kw_reg, neg_reg = P_GROUP_OPTION_KEYWORD, P_GROUP_AWARD_KEYWORD
        else:
            kw_reg, neg_reg = P_GROUP_AWARD_KEYWORD, P_GROUP_OPTION_KEYWORD
        if not kw_reg.search(text):
            return False
        if strict:
            return not neg_reg.search(text)
        return True

    @staticmethod
    def is_holder_company(text):
        return bool(P_HOLDER_COMPANY.search(text))

    def is_multi_shares_chapter(self, title):
        """
        判断章节标题是否为多组
        """
        return bool(P_MULTI_SCHEME_TITLES[self.share_type].search(title) or P_MULTI_SCHEME.search(title))

    def is_subsidiary(self, group_key: str, group_items: list[GroupItem]):
        """
        是否为子公司的计划
        """
        # 1. 分组名称中包含子公司信息
        for key in {group_key} | self.aliases[group_key]:
            if any(sub_name.lower() in key for sub_name in self.subsidiary_names):
                return True
        for item in group_items:
            # 2. 章节标题或者父章节标题包含关键词subsidiary或者子公司名称
            for title in self.pdfinsight.get_parent_titles(item.chapter_index)[1:]:
                if P_SUBSIDIARY_TITLE.search(title) or any(sub_name in title for sub_name in self.subsidiary_names):
                    logger.debug(f"[{group_key}] is_subsidiary=True, by chapter: title={title}")
                    return True
        if not self.subsidiary_names:
            return False
        for item in group_items:
            _, element = self.pdfinsight.find_element_by_index(item.range[0])
            if not is_paragraph_elt(element, strict=True):
                continue
            text = clean_txt(element["text"], remove_cn_text=True)
            if group_key not in self.get_multi_group_keys(text, exclude_default=True):
                continue
            # 3. 句子中包含子公司的名称
            for name in self.subsidiary_names:
                try:
                    pattern = re.compile(R_ADOPTED_BY_SUBSIDIARY % (name, name))
                except re.error:
                    continue
                if pattern.search(text):
                    logger.debug(
                        f"[{group_key}] is_subsidiary=True, by element: subsidiary_name={name}, index={element['index']}, text={text}"
                    )
                    return True
            break
        return False

    def is_invalid_day(self, adopted_day: str):
        """
        判断scheme是否为合法scheme（不能大于当前财报期末）
        """
        return self.report_year and adopted_day[:4] > self.report_year

    def title_to_group_key(self, title, strict=False):
        """
        把章节标题转换为分组key
        """
        # 提取option时，内容来自share award scheme章节，且章节里没有提到option的scheme，给默认key
        title = clean_txt(title, remove_cn_text=True)
        if P_OPTION_OR_AWARD_CHAPTER.search(title) and not self.is_current_type(title):
            return self.default_group_keys[0]
        if P_MULTI_SCHEME.search(title):
            title_keys = extract_group_key(title, self.share_type, ignore_case=True)
            if not title_keys or len(title_keys) > 1:
                return None
            return get_first_key(title_keys)
        if strict and not self.is_current_type(title, strict=True):
            return None
        if P_RIGHT_QUOTES.search(title):
            # 标题中带了别名
            # http://************:55647/#/project/remark/251242?treeId=8654&fileId=68663&schemaId=15&projectId=17&schemaKey=B80&page=53
            title_keys = extract_group_key(title, self.share_type, ignore_case=True)
            if not title_keys or len(title_keys) > 2:
                return None
            if len(title_keys) == 1:
                return get_first_key(title_keys)
            main_key, alias = sorted(title_keys, key=lambda x: (bool(P_YEAR.search(x)), x), reverse=True)
            if not self.is_default_key(alias):
                self.merge_alias_key(main_key, alias)
            return main_key
        if not extract_group_key(title, self.share_type, multi=True):
            return None
        title_key = title
        for reg, rpl in P_TITLE_REPLACE_REGS.items():
            title_key = reg.sub(rpl, title_key)
        return title_key.replace(" ", "").lower()

    def is_skip_group_key(self, group_key, text):
        return (group_key in SPECIAL_GROUP_KEYS or self.is_default_key(group_key)) and not P_EXACT_DEFAULT_KEY.search(
            text
        )

    def is_skip_chapter(self, syllabus, is_incentive, is_child=False):
        title = syllabus["title"]
        if P_IGNORE_CHAPTERS.search(title) or P_CONTINUED.search(title) or not P_SCHEME_TITLE.search(title):
            return True
        if any(P_IGNORE_PARENTS.search(title) for title in self.pdfinsight.get_parent_titles(syllabus["index"])):
            return True
        if not (is_incentive or self.is_incentive(syllabus, is_child=is_child) or self.is_current_type(title)):
            return True
        elems_num = len(range(*syllabus["range"]))
        if elems_num <= 1:
            return True
        if len(range(*syllabus["range"])) == 2:
            # http://************:55647/#/project/remark/231684?treeId=4265&fileId=66236&schemaId=15&projectId=17&schemaKey=B82&page=23
            # 标题为share options and rsus，章节下只有一段话提到了detail，则跳过章节
            elem_type, element = self.pdfinsight.find_element_by_index(syllabus["range"][0] + 1)
            if element and elem_type == PDFInsightClassEnum.PARAGRAPH.value and "detail" in element["text"].lower():
                return True
        return False

    def is_chapter_like(self, element):
        """
        升级pdfinsight后，长句子标题识别差，以下2个条件可判定为章节标题
        1. 不以.结尾
        2. 样式与所属章节或父章节标题完全相同 或者 多数单词首字母大写或全大写
        stock_code=08425, year=2024, rule=B64, page=24
        """
        if element["text"].endswith((".", ":", "：")):
            return False
        font_style = UnifiedParaElement(element, self.pdfinsight).font_style
        # 找相邻段落，如果样式与相邻段落样式相同，则不是章节标题
        for i in range(1, 6):
            next_index = element["index"] + i
            if next_index > self.pdfinsight.max_index:
                break
            next_element = self.pdfinsight.find_element_by_index(next_index)[1]
            if not is_para_elt(next_element) or self.pdfinsight.is_syllabus_title(next_element, check_page_first=True):
                continue
            next_style = UnifiedParaElement(next_element, self.pdfinsight).font_style
            # 段落样式与相邻段落样式相同，则不是章节标题
            if font_style == next_style:
                return False
        for syllabus in self.pdfinsight.find_syllabuses_by_index(element["index"])[::-1]:
            if syllabus["parent"] == -1:
                break
            # continued标题是误识别的标题，不能使用该样式判断
            if P_CONTINUED.search(syllabus["title"]):
                continue
            if font_style == UnifiedChapter(syllabus, self.pdfinsight).font_style:
                return True
        # 仅取前10个单词做判断
        for word in element["text"].split()[:10]:
            # 排除介词，介词一般可以不大写
            if P_EN.search(word[0]) and word.lower() not in PREP_WORDS and word[0].islower():
                return False
        return True

    def is_invalid_element(self, element):
        """
        满足该条件的元素块不提取group key
        """
        if not element:
            # 单元测试这里可能为None
            return True
        if is_table_elt(element):
            return not any(has_english_chars(cell) for cell in element["cells"].values())
        text = clean_txt(element.get("text") or "")
        return bool(
            not is_para_elt(element)
            or element["index"] in self.pdfinsight.syllabus_reader.elt_syllabus_dict
            or self.is_chapter_like(element)
            or self.pdfinsight.is_skip_element(element)
            or P_ONLY_DATE.search(text)
            or P_ONLY_NUM.search(text)
        )

    def trans_to_existed_key(
        self, group_key: str, group_keys: set[str] | dict, main_key: str | None = None
    ) -> tuple[bool, str]:
        """
        group_key若已经存在于获取的分组或者分组别名中，则存为别名
        """
        if group_key in group_keys:
            return True, main_key or group_key
        for key in group_keys:
            if self.is_alias_same_key(group_key, key):
                final_key = main_key or key
                self.merge_alias_key(final_key, group_key, final_key)
                return True, key
            if main_key:
                continue
            existed, _ = self.trans_to_existed_key(group_key, self.aliases[key], main_key=key)
            if existed:
                return True, key
        return False, group_key

    def merge_keys_in_group(self, group_results: dict[str, list[GroupItem]], group_keys=None):
        # 排序规则：带年份的排在前，首字母大写的排在前，再按首字母降序排序
        group_keys = sorted(
            group_keys or group_results, key=lambda x: (bool(P_YEAR.search(x)), x[0].lower() != x[0], x), reverse=True
        )
        if len(group_keys) < 2:
            return
        first, *left = group_keys
        if same_keys := {key for key in left if self.is_alias_same_key(first, key)}:
            if group_results:
                for same_key in same_keys:
                    group_results[first].extend(self.de_duplicate_items(group_results[same_key], group_results[first]))
                    group_results.pop(same_key)
                    self.merge_alias_key(first, same_key, group_results)
        return self.merge_keys_in_group(group_results, group_keys=subtract_keys(left, same_keys))

    def merge_alias_key(self, main_key: str, merge_key: str, exclude_keys: Iterable = ()):
        """
        合并group_key时，别名也要合并，合并后要从self.aliases中pop合并的key
        """
        if main_key == merge_key or main_key.endswith((f"underthe{merge_key}", f"underthe{main_key}")):
            return
        merge_keys = subtract_keys({merge_key} | self.aliases[merge_key], {*exclude_keys, main_key})
        self.aliases[main_key].update(merge_keys)
        self.aliases.pop(merge_key, None)

    @staticmethod
    def is_same_year_schemes(schemes: set[Scheme]):
        return len({sch.day_str[:4] for sch in schemes if sch.day_str}) == 1

    def set_alias_by_group_indices(self, group_results: dict[str, list[GroupItem]]):
        """
        如果adopted句子提取到了别名，且句子在分组范围内，则合并别名
        TODO 如果日期来自definitions章节则直接取
        http://************:55647/#/project/remark/232881?treeId=38070&fileId=66435&schemaId=15&projectId=17&schemaKey=B82
        """
        change_key_map = {}
        all_schemes = set(chain.from_iterable(self.ai_alias_adop_days.values()))
        for key, items in group_results.items():
            schemes = set(
                chain.from_iterable(
                    schemes
                    for idx, schemes in self.ai_alias_adop_days.items()
                    if any(is_in_range(idx, item.range) for item in items)
                )
            )
            if not schemes:
                # 在已识别分组的章节取不到，则尝试在其他章节取adoption date
                if not (
                    schemes := (
                        {sch for sch in all_schemes if sch.name == key}
                        or {sch for sch in all_schemes if self.is_alias_same_key(sch.name, key)}
                    )
                ):
                    continue
            matched_schemes = set()
            if len(schemes) == 1 and len(self.ai_alias_adop_days[get_first_key(schemes).index]) == 1:
                matched_schemes = schemes
            else:
                # 匹配到多个scheme或同一个段落中有多个scheme时，名称必须一致
                for scheme in schemes:
                    # 多个scheme时，必须为相同key
                    if any(self.is_alias_same_key(key, k) for k in {scheme.name} | self.aliases[scheme.name]):
                        matched_schemes.add(scheme)
                if len(matched_schemes) > 1 and (
                    filter_schemes := {sch for sch in matched_schemes if not sch.from_life}
                ):
                    # 同时存在两种日期，不考虑从生命周期推算出来的日期
                    matched_schemes = filter_schemes
                if not self.is_same_year_schemes(matched_schemes):
                    # 根据元素块index过滤
                    matched_schemes = {
                        sch
                        for sch in matched_schemes
                        if not any(
                            any(is_in_range(sch.index, v.range) for v in vs)
                            for k, vs in group_results.items()
                            if k != key
                        )
                    }
                # 多个日期时，取年份多的那组
                most_common = collections.Counter(
                    [sch.day_str[:4] for sch in matched_schemes if sch.day_str]
                ).most_common()
                if len(most_common) == 1:
                    matched_schemes = {
                        sch for sch in matched_schemes if not sch.day_str or sch.day_str[:4] == most_common[0][0]
                    }
            if not matched_schemes:
                continue
            is_same_year = self.is_same_year_schemes(matched_schemes)
            for scheme in matched_schemes:
                if key != scheme.name:
                    if not self.is_default_key(scheme.name):
                        self.merge_alias_key(key, scheme.name, group_results)
                    scheme.name = key
                if is_same_year and scheme.day_str:
                    self.scheme_day_map[key].add(scheme.day_str)
                    if len(scheme.day_str) == 10:
                        self.day_scheme_map[scheme.day_str].add(key)
        for before, after in change_key_map.items():
            group_results[after] = group_results.pop(before)

    def is_alias_same_key(self, key1, key2):
        """
        根据别名及配置判断两个key是否为同一组，满足下方任一条件：
        1. 两个key相等
        2. 任意一个是另一个的别名
        3. 在NEED_MERGE_GROUP_KEYS中为同一组
        """
        if (
            key1.lower() == key2.lower()
            or any({key1.lower(), key2.lower()}.issubset(group) for group in NEED_MERGE_GROUP_KEYS)
            or is_same_key(self.share_type, key1, key2, company_words=self.company_words)
        ):
            return True
        key2_upper = self.capitalize_default_key if key2 == self.default_group_keys[0] else key2
        key1_upper = self.capitalize_default_key if key1 == self.default_group_keys[0] else key1
        for key in self.aliases[key2] | self.aliases[key2.lower()] | self.aliases[key2_upper]:
            if is_same_key(self.share_type, key1, key, company_words=self.company_words):
                return True
        for key in self.aliases[key1] | self.aliases[key1.lower()] | self.aliases[key1_upper]:
            if is_same_key(self.share_type, key2, key, company_words=self.company_words):
                return True
        return False

    def is_related_key(self, key1, key2, strict=False):
        for base_key, another_key in [(key1, key2), (key2, key1)]:
            for key in {base_key} | self.aliases[base_key]:
                if any(self._is_related_key(key, k, strict=strict) for k in {another_key} | self.aliases[another_key]):
                    return True
        return False

    def _is_related_key(self, key1: str, key2: str, strict: bool = False, do_replace: bool = True) -> bool:
        """
        判断两个key是否有关
        http://************:55647/#/project/remark/233850?treeId=44978&fileId=66597&schemaId=15&projectId=17&schemaKey=B82
        66597中，2021 share incentive scheme和rsu scheme是同一个key
        """
        # 1. 名称互相包含
        if key1 in key2 or key2 in key1:
            return True
        # 2. 都包含默认名称
        if (
            not strict
            and any(k in key1 or key1 in k for k in self.default_group_keys)
            and any(k in key2 or key2 in k for k in self.default_group_keys)
        ):
            return True
        if not do_replace:
            return False
        std_key1, std_key2 = standardize_group_key(key1), standardize_group_key(key2)
        if self._is_related_key(std_key1, std_key2, do_replace=False):
            return True
        for reg, rpl in P_SCHEME_NAME_REPLACER[self.share_type].items():
            if self._is_related_key(reg.sub(rpl, std_key1), reg.sub(rpl, std_key2), do_replace=False):
                return True
        return False

    def merge_group_keys(self, group_keys: set | list, merged_keys: set = None) -> set[str]:
        """
        根据是否为相同key，依次合并group_keys中的key
        """
        merged_keys = merged_keys or set()
        if len(group_keys) <= 1:
            merged_keys.update(group_keys)
            return merged_keys
        first = get_first_key({k for k in group_keys if self.aliases[k]} or group_keys)
        left = subtract_keys(group_keys, first)
        if same_keys := [key for key in left if self.is_alias_same_key(first, key)]:
            for same_key in same_keys:
                self.merge_alias_key(first, same_key)
        merged_keys.add(first)
        return self.merge_group_keys([k for k in left if k not in same_keys], merged_keys)

    def merge_groups_by_key(self, to_merge_group: dict[str, list], group: dict[str, list]):
        for key, items in group.items():
            to_merge_group[key].extend(self.de_duplicate_items(items, to_merge_group[key]))

    @staticmethod
    def chapter_to_group_item(syllabus, page=0) -> GroupItem:
        start, end = syllabus["range"]
        return GroupItem(
            syllabus["element"],
            [start + 1, end],
            True,
            syllabus["index"],
            syllabus["level"],
            page or syllabus["dest"]["page_index"],
            title=syllabus["title"],
        )

    @staticmethod
    def elems_to_group_item(elem_indices, chapter_idx, page) -> list[GroupItem]:
        return [GroupItem(idx, [idx, idx + 1], False, chapter_idx, 0, page) for idx in elem_indices]

    @cached_property
    def sorted_syllabus(self):
        dr_syllabuses = []
        cg_syllabuses = []
        other_syllabuses = []
        note_syllabuses = []
        need_ignore_syllabus_idx = set()
        for index, syllabus in self.pdfinsight.syllabus_dict.items():
            root_titles = {self.pdfinsight.get_parent_titles(syllabus["index"])[0]}
            if page := get_keys(syllabus, ["dest", "page_index"]):
                # 忽略 目录 中的章节
                if self.pdfinsight.is_catalog_page(page):
                    continue
                if fake_title := self.pdfinsight.page_chapter_from_first_para.get(page):
                    root_titles.add(fake_title)
            if any(P_CG_CHAPTER.search(root_title) for root_title in root_titles):
                cg_syllabuses.append(syllabus)
                continue
            if index in need_ignore_syllabus_idx:
                continue
            # 忽略 企业信息 中的章节
            if any(P_LIST_RULE_IGNORE_CHAPTERS.nexts(root_title) for root_title in root_titles):
                need_ignore_syllabus_idx.add(syllabus["index"])
                cg_syllabuses.append(syllabus)
                continue
            if any(P_DR_CHAPTER.search(root_title) for root_title in root_titles):
                dr_syllabuses.append(syllabus)
                continue
            if any(P_NOTE_CHAPTER.search(root_title) for root_title in root_titles):
                note_syllabuses.append(syllabus)
                continue
            other_syllabuses.append(syllabus)
        # DR章节没信息时，尝试取CG章节： https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4696#note_527216
        # cg_syllabuses = [] if dr_syllabuses else cg_syllabuses
        return dr_syllabuses, other_syllabuses, cg_syllabuses, note_syllabuses

    def get_group_info(self, as_dict=False) -> dict[str, GroupInfo | dict]:
        """
        计算文中有几组share_option和share_award
        从章节中匹配，匹配原则：
        a. 优先取director' report章节，再取其它章节，最后取notes章节
        b. 多个章节的结果根据分组名称合并，若分组名称不一致，则根据各计划adoption date合并，adoption date由openai根据helper进行合并
        c. 自上往下找能匹配`P_SCHEME_TITLE`的章节，否则跳过
        d. 排除章节下仅有一个非标题元素块，且元素块中有detail关键词的章节
        e. 排除满足`P_EXCLUDE_GROUP_CHAPTERS`正则的章节
        f. 若章节为incentive章节，或同时包含share option和share award，则遍历子章节下的所有段落，分别取段落
        g. 若当前share_type为option，遇到了share award scheme章节，且章节下没有子章节，当做incentive处理
        """
        if self.share_info and not self.share_info[f"has_share_{self.share_type}"]:
            return {}
        # 遍历所有章节获取关键词 先从DR章节提取，再从非Note章节提取，最后从Note章节提取
        res_groups = defaultdict(list)
        for num, syllabuses in enumerate(self.sorted_syllabus):
            if num == 2 and res_groups:
                # num == 2表示 CG章节，只有当notes以外章节都提取不到信息时，才考虑CG章节
                continue
            cur_groups = self.get_group_info_from_syllabus(syllabuses, res_groups)
            if not cur_groups:
                continue
            if res_groups:
                self.merge_group_results(res_groups, cur_groups)
            else:
                for key, group_items in cur_groups.items():
                    res_groups[key].extend(self.de_duplicate_items(group_items, res_groups[key]))

        # 删除重复的别名，同时反向取别名（group_key在某个名称的别名中）
        for group_key in res_groups:
            new_aliases = set()
            for key, aliases in self.aliases.items():
                if group_key not in aliases:
                    continue
                if group_key == key:
                    aliases.remove(group_key)
                else:
                    new_aliases.update({key} | subtract_keys(aliases, group_key))
            for new_alias in new_aliases:
                self.merge_alias_key(group_key, new_alias)

        # 删除没有别名的key
        for key in {k for k, v in self.aliases.items() if not v}:
            self.aliases.pop(key)
        for key in {k for k, v in self.scheme_day_map.items() if not v}:
            self.scheme_day_map.pop(key)

        res_groups = self.to_group_info(res_groups, as_dict)
        return res_groups

    def to_group_info(
        self, res_groups: dict[str, list[GroupItem]], as_dict: bool = False
    ) -> dict[str, GroupInfo | dict]:
        final_groups = {}
        # 取各个key的年份和日期
        for key, items in res_groups.items():
            adopt_year = ""
            if mat := P_YEAR.search(key):
                # 先根据名称提取年份
                adopt_year = mat.group("year") or ""
            # 再根据别名提取
            adopt_days = self.scheme_day_map[key]
            for alias in self.aliases[key]:
                if mat := P_YEAR.search(alias):
                    # 根据名称提取年份
                    alias_year = mat.group("year") or ""
                    if not adopt_year:
                        adopt_year = alias_year
                    elif adopt_year != alias_year:
                        # 名称和别名的年份不同，说明分组合并错误
                        logger.warning(f"Wrong share group: {key}")
                        adopt_year = ""
                if days := self.scheme_day_map[alias]:
                    # 取别名的采纳日期
                    adopt_days.update(days)
            adop_years = {day[:4] for day in adopt_days}
            if len(adop_years) > 1:
                # 提取到多个年份，说明采纳日提取错误或者分组错误
                logger.warning(f"Wrong adoption date or share group: {key}")
                adopt_days = set()
            elif not adopt_year:
                adopt_year = get_first_key(adop_years)
            if not adopt_year and len({day[:4] for day in self.scheme_day_map[key]}) == 1:
                adopt_days = self.scheme_day_map[key]
                adopt_year = get_first_key(adopt_days)[:4]
            final_groups[key] = GroupInfo(
                items, adopt_year, adopt_days, self.aliases[key], self.is_subsidiary(key, items)
            )
        self.debug_group_info(final_groups)
        if not as_dict:
            return final_groups
        return self.to_dict(final_groups)

    @staticmethod
    def to_dict(res_groups: dict[str, GroupInfo]) -> dict[str, dict]:
        dict_result = {}
        for group_key, info in res_groups.items():
            info_dict = asdict(info)
            for key, value in info_dict.items():
                if isinstance(value, set):
                    info_dict[key] = list(value)
            dict_result[group_key] = info_dict
        return dict_result

    def get_group_info_from_syllabus(self, syllabuses, res_groups) -> dict[str, list[GroupItem]]:
        groups = defaultdict(list)
        skip_indices = set()
        for syllabus in syllabuses:
            # 这种章节标题不需要提取
            # 跳过已处理的章节
            if syllabus["index"] in skip_indices or any(
                is_sub_range(syllabus["range"], item.range) for item in chain(*groups.values())
            ):
                continue
            if self.is_skip_chapter(syllabus, False):
                continue
            skip_indices |= self.pdfinsight.get_child_syllabus_indices(syllabus)
            title = syllabus["title"]
            title_key = self.title_to_group_key(title)
            is_cur_type = self.is_current_type(title, strict=True)
            is_incentive = self.is_incentive(syllabus)
            use_parent, child_groups = self.get_groups_from_children(syllabus, res_groups, is_incentive, is_cur_type)
            if child_groups:
                self.merge_groups_by_key(groups, child_groups)
                continue
            if not use_parent:
                continue
            if title_key and not self.is_multi_shares_chapter(title):
                if (is_cur_type and not is_incentive) or (
                    not self.is_default_key(title_key)
                    and title_key in (res_groups | groups)
                    and (
                        any(item.is_chapter for item in res_groups[title_key])
                        or any(item.is_chapter for item in groups[title_key])
                    )
                ):
                    # http://************:55647/#/project/remark/250363?treeId=6141&fileId=68516&schemaId=15&projectId=17&schemaKey=B74
                    existed, existed_key = self.trans_to_existed_key(title_key, res_groups)
                    if existed:
                        groups[existed_key].append(self.chapter_to_group_item(syllabus))
                        continue
            chapter_groups = self.get_groups_from_chapter(syllabus, res_groups, is_incentive, is_cur_type)
            self.merge_groups_by_key(groups, chapter_groups)
        if groups:
            self.set_alias_by_group_indices(groups)
        if len(groups) > 1:
            self.merge_keys_in_group(groups)
        return groups

    def get_groups_from_chapter(
        self, syllabus: dict, res_groups: dict[str, list[GroupItem]], is_incentive, is_cur_type, use_chapter=False
    ) -> dict[str, list[GroupItem]]:
        """
        递归章节及子章节，提取分组信息
        """
        groups = defaultdict(list)
        title = syllabus["title"]
        title_key = self.title_to_group_key(title)
        if use_chapter:
            groups[title_key or self.default_group_keys[0]] = [self.chapter_to_group_item(syllabus)]
            return groups
        if is_incentive:
            return self.get_groups_from_incentive(syllabus, title_key, res_groups)
        if self.is_multi_shares_chapter(title):
            return self.get_groups_from_paras(syllabus, title_key, res_groups)
        if not is_cur_type and self.is_current_type(title, share_type=self.another_type):
            return groups
        groups[title_key or self.default_group_keys[0]] = [self.chapter_to_group_item(syllabus)]
        return groups

    def get_groups_from_children(
        self, syllabus: dict, res_groups: dict[str, list[GroupItem]], is_incentive: bool, is_cur_type: bool
    ) -> tuple[bool, dict[str, list[GroupItem]]]:
        max_num = 5
        groups = defaultdict(list)
        syll_idx, syll_page, child_indices = syllabus["index"], syllabus["dest"]["page_index"], syllabus["children"]
        children = [self.pdfinsight.syllabus_dict[idx] for idx in child_indices]
        grouped_children = group_children_by_style(self.pdfinsight, children)
        start, end = syllabus["range"]
        if len(grouped_children) > 1:
            # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_532035
            children = self.gene_children_by_crude(syllabus, start, end, grouped_children)
        elif child_indices:
            # 子章节超过5个或者子章节最大序号超过5或子章节一半都无效，则直接根据子章节名称提取关键词
            if (
                len(child_indices) > max_num
                or prefix_to_int(self.pdfinsight.syllabus_dict[max(child_indices)]["title"]) > max_num
                or len({c["title"] for c in children if P_IGNORE_CHAPTERS.search(c["title"])}) / len(children) > 0.5
            ):
                # 如果子章节很多，或者子章节都无效，则直接从子章节标题提取关键词，提取到1个直接用作分组
                # http://************:55647/#/project/remark/233167?treeId=4194&fileId=66483&schemaId=15&projectId=17&schemaKey=B82
                group_keys = set()
                for child_syll in children:
                    title = clean_txt(child_syll["title"])
                    if P_IGNORE_CHAPTERS.search(title):
                        continue
                    group_keys.update(extract_group_key(title, self.share_type))
                if len(group_keys) != 1 or is_incentive:
                    return True, groups
                groups[get_first_key(group_keys)] = [self.chapter_to_group_item(syllabus)]
                return True, groups
        if child_indices and (next_syllabus := self.pdfinsight.syllabus_dict[child_indices[0]]):
            # 章节嵌套了两层，尽可能使用第二层章节
            # http://************:55647/#/project/remark/231582?treeId=6025&fileId=66219&schemaId=15&projectId=17&schemaKey=B82
            if next_syllabus["children"] and len(children) == 1 and len(next_syllabus["children"]) > 1:
                is_incentive = self.is_incentive(next_syllabus)
                is_cur_type = self.is_current_type(next_syllabus["title"], strict=True)
                return self.get_groups_from_children(next_syllabus, res_groups, is_incentive, is_cur_type)
        if len(grouped_children) <= 1:
            gene_children = self.gene_children_by_crude(syllabus, start, end)
            if gene_children and (not children or len(gene_children) > len(children)):
                children = gene_children
        if not children or len(children) > max_num:
            return True, groups
        has_valid, count = self.count_children([child["title"] for child in children], is_incentive)
        if not has_valid:
            return True, groups
        if is_incentive and count == 0:
            return False, groups
        other_items = []
        page_dict = {child["element"]: child["dest"]["page_index"] for child in children}
        min_idx, max_idx = min(page_dict), max(child["range"][1] for child in children)
        if min_idx - start > 1:
            # 父章节与子章节之间有内容，则内容归属于每个子章节
            other_items.extend(
                self.elems_to_group_item(self.filter_invalid_elements(start, min_idx), syll_idx, page=syll_page)
            )
        if max_idx < end:
            other_items.extend(
                self.elems_to_group_item(
                    self.filter_invalid_elements(max_idx, end), syll_idx, page=page_dict[max(page_dict)]
                )
            )
        use_parent = True
        for child_syll in children:
            child_title = child_syll["title"]
            if self.is_current_type(child_title, strict=True, share_type=self.another_type):
                use_parent = False
                continue
            if self.is_current_type(child_title, strict=True):
                child_is_cur, is_incen = True, False
            else:
                is_incen = self.is_incentive(child_syll, is_child=True)
                child_is_cur, is_incen = not is_incen and is_cur_type, is_incen or is_incentive
            if not child_is_cur and self.is_skip_chapter(child_syll, is_incen, is_child=True):
                continue
            # http://************:55647/#/project/remark/251141?treeId=10041&fileId=68646&schemaId=15&projectId=17&schemaKey=B74
            use_chapter = (
                child_is_cur
                and len(child_syll["children"]) > 5
                and not self.gene_children_by_crude(child_syll, *child_syll["range"])
            )
            child_groups = self.get_groups_from_chapter(
                child_syll, res_groups, is_incen, child_is_cur, use_chapter=use_chapter
            )
            if all(child_groups):
                self.merge_groups_by_key(groups, child_groups)
            elif not is_incen:
                # 子章节若未提取到关键词，则合并到其它章节
                other_items.append(self.chapter_to_group_item(child_syll))
        if other_items:
            for items in groups.values():
                items.extend(other_items)
        return use_parent, groups

    def filter_invalid_elements(self, index_start, index_end):
        """
        从给定index的元素块中，删除页眉、页脚、中文等无效元素块
        """
        results = []
        for index in range(index_start, index_end):
            type_, element = self.pdfinsight.find_element_by_index(index)
            if type_ not in (
                PDFInsightClassEnum.PARAGRAPH.value,
                PDFInsightClassEnum.TABLE.value,
            ) or self.is_invalid_element(element):
                continue
            results.append(index)
        return results

    def gene_children_by_crude(self, syllabus, start, end, grouped_children: dict[str, list[dict]] = None):
        """
        有一些子章节被识别为段落，根据初步预测结果，结合子章节样式信息，构造一组新的子章节
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_476071
        https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_485386
        """
        children = []
        if not (indices := {idx for idx in self.crude_schemes if is_in_range(idx, [start + 1, end])}):
            return []
        styles_map = {idx: chapter_title_style(self.pdfinsight, idx, use_char_style=False) for idx in indices}
        parent_style = chapter_title_style(self.pdfinsight, start)
        # 目录可能没有取全
        if not is_contiguous_titles(
            [pair[0] for idx, pair in sorted(self.crude_schemes.items(), key=lambda x: x[0]) if idx in indices],
            get_first_value(styles_map),
        ):
            for index in range(start + 1, end):
                try:
                    _, element = self.pdfinsight.find_element_by_index(index)
                except IndexError:
                    continue
                if index in indices or not is_paragraph_elt(element, strict=True) or element["type"] != "PARAGRAPH_1":
                    continue
                text = clean_txt(element["text"], remove_cn_text=True)
                if not self.is_scheme_chapter_title(text):
                    continue
                style = chapter_title_style(self.pdfinsight, index, element=element, use_char_style=False)
                if style and parent_style == style:
                    break
                if style not in styles_map.values():
                    continue
                indices.add(index)
                styles_map[index] = style
                self.crude_schemes[index] = (text, element["page"])

        level = syllabus["level"]
        new_indices = set()
        if grouped_children:
            for style, childs in grouped_children.items():
                child_indices = {ch["element"] for ch in childs}
                if style not in styles_map.values() or not indices & child_indices:
                    continue
                new_indices.update(child_indices)
                new_indices.update({k for k, v in styles_map.items() if v == style})
                break
        indices = sorted(new_indices) if new_indices else sorted(indices)
        for start_idx, end_idx in zip(indices, indices[1:] + [end]):
            if start_idx in self.crude_schemes:
                child_title, page = self.crude_schemes[start_idx]
            else:
                _, element = self.pdfinsight.find_element_by_index(start_idx)
                if not (child_title := element.get("text")):
                    continue
                page = element["page"]
            if chapter := self.pdfinsight.syllabus_reader.elt_syllabus_dict.get(start_idx):
                chapter_idx = chapter["index"]
            else:
                level += 1
                chapter_idx = syllabus["index"]
            children.append(
                {
                    "element": start_idx,
                    "index": chapter_idx,
                    "title": child_title,
                    "range": [start_idx, end_idx],
                    "level": level,
                    "dest": {"page_index": page},
                    "children": [],
                }
            )
        return children

    @staticmethod
    def is_scheme_chapter_title(text):
        return (
            text
            and not text.endswith((".", ";", "；", ":", "："))
            and P_WORD_END.search(text)
            and P_SCHEME_TITLE.search(text)
            and not (P_CONTINUED.search(text) or P_IGNORE_CHAPTERS.search(text) or P_IGNORE_PARENTS.search(text))
        )

    def get_groups_from_incentive(
        self, syllabus: dict, title_key: str, res_groups: dict[str, list[GroupItem]]
    ) -> dict[str, list[GroupItem]]:
        """
        从incentive章节提取分组，提取逻辑：
        1. 既有option，又有award，提取key和对应段落
        2. 只有一种，且key只有一个，整章节提取
        3. 只有一种，key有多个，提取key和对应段落
        TODO 是否只要有option或者award就给整个章节？
        """
        skip_keys, default_items = set(), []
        last_elem_info = None
        groups = defaultdict(list)
        skip_indices, cur_indices, another_indices = set(), set(), set()
        start, end = syllabus["range"]
        tbl_share_types = set()
        for index in range(start + 1, end):
            if index in skip_indices:
                last_elem_info = None
                continue
            elem_info = self.get_elem_info_by_index(index, skip_indices, groups, is_incentive=True)
            if not elem_info:
                last_elem_info = None
                continue
            if self.is_chapter_end(syllabus, index, elem_info, last_elem_info):
                break
            if isinstance(elem_info, str):
                last_elem_info = None
                continue
            last_elem_info = elem_info
            tbl_share_types.update(elem_info.tbl_share_types)
            # is_skip = elem_info.syll_idx and P_INCENTIVE_SKIP_TITLE.nexts(
            #     self.pdfinsight.syllabus_dict[elem_info.syll_idx]["title"]
            # )
            elem_s_type = elem_info.share_type
            if elem_s_type in ("all", self.share_type):
                cur_indices.add(index)
            if elem_s_type in ("all", self.another_type):
                another_indices.add(index)
            if index in another_indices and index not in cur_indices:
                continue
            skip_keys.update(elem_info.skip_keys)
            items = self.elems_to_group_item(elem_info.all_indices, syllabus["index"], elem_info.page)
            # 如果章节名称中包含年份，直接用章节名称做key TODO 也可以是type前缀
            # group_keys = (
            #     [title_key] if title_key and P_YEAR.search(title_key) and else self.merge_group_keys(elem_info.group_keys)
            # )
            for group_key in elem_info.group_keys:
                _, exist_key = self.trans_to_existed_key(group_key, groups)
                groups[exist_key].extend(self.de_duplicate_items(items, groups[exist_key]))
            if not elem_info.group_keys:
                default_items.extend(self.de_duplicate_items(items, default_items))
        if (
            not cur_indices
            or (not groups and not default_items)
            or (tbl_share_types and not any(type_ in ("all", self.share_type) for type_ in tbl_share_types))
        ):
            # incentive章节中有表格，但表格中未提取到当前类型，则认为incentive不属于当前share_type
            return {}
        # http://************:55647/#/project/remark/233455?treeId=9357&fileId=66531&schemaId=15&projectId=17&schemaKey=B82&page=33 仅识别到一个元素块，且key是别名，则跳过
        if (
            not groups
            and len(cur_indices) == 1
            and P_OPTION_OR_AWARD_CHAPTER.search(syllabus["title"])
            and not self.is_current_type(syllabus["title"])
        ):
            return {}
        use_chapter = not another_indices or another_indices.issubset(cur_indices)
        return self.process_group_from_elements(
            syllabus, title_key, groups, res_groups, skip_keys, default_items, True, use_chapter
        )

    def get_groups_from_paras(
        self, syllabus: dict, title_key: str, res_groups: dict[str, list[GroupItem]] | None
    ) -> dict[str, list[GroupItem]]:
        """
        遍历章节下元素块，提取关键词替换默认key
        提取前提：
            章节标题为默认分组或带and、或为RSU、或以schemes, plans, rsus结尾
            http://************:55647/#/project/remark/233581?treeId=7494&fileId=66552&schemaId=15&projectId=17&schemaKey=B76
        提取原则：
            1. 提取到一个，直接替换key名
            2. 提取到多个，拆分成多个key+对应元素块
            http://************:55647/#/project/remark/250830?treeId=9858&fileId=68594&schemaId=15&projectId=17&schemaKey=B93.1
        TODO 优先找A.x 1.x 能提取到关键词的章节
        TODO 全文如果只提到某个group_key一次，则丢弃这个group_key
        """
        # 存储提不到关键词或默认关键词的元素块
        last_elem_info = None
        default_items, skip_keys, skip_indices = [], set(), set()
        new_groups = defaultdict(list)
        start, end = syllabus["range"]
        for index in range(start + 1, end):
            if index in skip_indices:
                last_elem_info = None
                continue
            elem_info = self.get_elem_info_by_index(index, skip_indices, new_groups)
            if not elem_info:
                last_elem_info = None
                continue
            if self.is_chapter_end(syllabus, index, elem_info, last_elem_info):
                break
            if isinstance(elem_info, str):
                last_elem_info = None
                continue
            last_elem_info = elem_info
            skip_keys.update(elem_info.skip_keys)
            items = self.elems_to_group_item(elem_info.all_indices, elem_info.syll_idx, elem_info.page)
            for group_key in elem_info.group_keys:
                _, exist_key = self.trans_to_existed_key(group_key, new_groups)
                new_groups[exist_key].extend(self.de_duplicate_items(items, new_groups[exist_key]))
            if not elem_info.group_keys:
                default_items.extend(self.de_duplicate_items(items, default_items))
        return self.process_group_from_elements(
            syllabus, title_key, new_groups, res_groups, skip_keys, default_items, False
        )

    def is_chapter_end(self, syllabus: dict, index: int, elem_info: ElementInfo, last_elem_info: ElementInfo) -> bool:
        if isinstance(elem_info, str):
            # 当前段落和标题level同级，则章节终止
            # http://************:55647/#/project/remark/257648?treeId=10363&fileId=68744&schemaId=15&projectId=17&schemaKey=B80&page=188
            if self.pdfinsight.syllabus_reader.relation_of_2_syllabuses(syllabus["title"], elem_info) == 1:
                syllabus["range"][1] = index
                # 目录识别有误，这里是下一个章节
                return True
        elif (
            last_elem_info
            and last_elem_info.page == elem_info.page
            and get_keys(syllabus, ["dest", "box"])
            and last_elem_info.outline
            and elem_info.outline
        ):
            last_left, _, last_right, _ = last_elem_info.outline
            left, _, right, _ = elem_info.outline
            # 当前段落的缩进远远小于上个段落的缩进，且约等于章节标题的缩进，则认为段落到此终止
            # http://************:55647/#/project/remark/264608?treeId=13198&fileId=70136&schemaId=15&projectId=17&schemaKey=B76&page=59 元素块1082
            if abs(last_right - right) < 1 and abs(left - syllabus["dest"]["box"][0]) < 1 and (last_left - left) > 20:
                syllabus["range"][1] = index
                # 目录识别有误，这里是父章节的段落
                return True
        return False

    def process_group_from_elements(
        self,
        syllabus: dict,
        title_key: str,
        groups: dict[str, list[GroupItem]],
        res_groups: dict[str, list[GroupItem]],
        skip_keys: set[str],
        default_items: list[GroupItem],
        from_incentive: bool,
        use_chapter: bool = True,
    ) -> dict[str, list[GroupItem]]:
        if not groups:
            if not default_items:
                return groups
            if len(default_items) == 1:
                # 章节中只有1个段落，且段落无效，则不提分组
                # http://************:55647/#/project/remark/258043?treeId=3543&fileId=68823&schemaId=15&projectId=17&schemaKey=B76
                elt_type, elt = self.pdfinsight.find_element_by_index(default_items[0].index)
                if elt_type == PDFInsightClassEnum.PARAGRAPH.value and (
                    elt["type"] == "PARAGRAPH_1" or P_IGNORE_PARAS.search(clean_txt(elt["text"], remove_cn_text=True))
                ):
                    return groups
            guess_key = (skip_keys and get_first_key(skip_keys)) or self.default_group_keys[0]
            if use_chapter:
                guess_key = title_key or guess_key
            _, group_key = self.trans_to_existed_key(guess_key, res_groups)
            groups[group_key] = [self.chapter_to_group_item(syllabus)] if use_chapter else default_items
            return groups
        # 过滤groups中的默认key和标题key
        default_items.extend(self.filter_chapter_group_keys(groups, title_key))
        if len(groups) > 1:
            self.merge_keys_in_group(groups)
        if len(groups) == 1 and use_chapter:
            new_key = get_first_key(groups)
            final_key, alias = (
                (new_key, title_key) if not title_key or self.is_default_key(title_key) else (title_key, new_key)
            )
            _, exist_key = self.trans_to_existed_key(final_key, res_groups)
            if alias and alias != exist_key and not self.is_default_key(alias):
                self.merge_alias_key(title_key, new_key, res_groups)
            return {exist_key: [self.chapter_to_group_item(syllabus)]}
        if not from_incentive or use_chapter:
            # 多个key，但是key的元素块都相同，则每个key都取这个章节
            # http://************:55647/#/project/remark/233191?projectId=17&treeId=10935&fileId=66487&schemaId=15&schemaKey=B94.1
            if len({tuple(item.index for item in items) for items in groups.values()}) == 1:
                for key in groups:
                    groups[key] = [self.chapter_to_group_item(syllabus)]
                return groups
        if len(groups) == 2 and len({k if k.startswith("new") else f"new{k.lower()}" for k in groups}) == 1:
            # 如果只有两个key，一个是new{scheme}，一个是{scheme}， 则new取全部
            # http://************:55647/#/project/remark/232851?treeId=6372&fileId=66430&schemaId=15&projectId=17&schemaKey=B82
            new_key = [k for k in groups if k.startswith("new")][0]
            not_new_key = get_first_key(k for k in groups if k != new_key)
            groups[not_new_key].extend(self.de_duplicate_items(default_items, groups[not_new_key]))
            groups[new_key] = [self.chapter_to_group_item(syllabus)]
            return groups
        # 当前结果与已获取的结果合并
        new_groups = defaultdict(list)
        for key, items in groups.items():
            items.extend(self.de_duplicate_items(default_items, items))
            existed, exist_key = self.trans_to_existed_key(key, res_groups)
            if existed:
                res_groups[exist_key].extend(self.de_duplicate_items(items, res_groups[exist_key]))
            else:
                new_groups[key] = items
        return new_groups

    def filter_chapter_group_keys(self, groups: dict[str, list[GroupItem]], title_key: str | None = None):
        """
        章节关键词提取完成后，如果同时存在多个key，按照以下规则过滤：
        1. 删除所有默认key
        2. 如果存在章节标题key，且标题key只取到一个段落，或者被其他key包含，则删除章节标题key
        """
        if len(groups) <= 1:
            return []
        # http://************:55647/#/project/remark/232965?treeId=4451&fileId=66449&schemaId=15&projectId=17&schemaKey=B82&page=306
        default_keys = [*SPECIAL_GROUP_KEYS, *self.default_group_keys]
        # http://************:55647/#/project/remark/250811?treeId=42901&fileId=68591&schemaId=15&projectId=17&schemaKey=B80
        if title_key and len(groups) > 1 and any(title_key in k for k in groups if title_key != k):
            default_keys.append(title_key)
        default_items = []
        for key in default_keys:
            if len(groups) == 1:
                break
            if key not in groups:
                continue
            for item in groups[key]:
                if any(
                    item.index == it.index
                    for it in chain.from_iterable(items for gk, items in groups.items() if gk != key)
                ):
                    continue
                default_items.append(item)
            groups.pop(key)
        return default_items

    @staticmethod
    def de_duplicate_items(items: list[GroupItem], existed_items: list[GroupItem]) -> list[GroupItem]:
        """
        合并items时需要根据index去重
        """
        existed_chapter_indices = {ex_item.index for ex_item in existed_items if ex_item.is_chapter}
        filter_items = []
        for item in items:
            if item.is_chapter and item.index in existed_chapter_indices:
                continue
            if not item.is_chapter and any(is_in_range(item.index, ex_item.range) for ex_item in existed_items):
                continue
            filter_items.append(item)
        return filter_items

    @staticmethod
    def tbl_element_share_type(tbl_share_types: set[str]) -> str:
        if "all" in tbl_share_types or {"option", "award"}.issubset(tbl_share_types):
            return "all"
        left_types = tbl_share_types - {"all", None}
        return None if not left_types else get_first_key(left_types)

    def get_elem_info_by_index(
        self,
        index: int,
        skip_indices: set[int],
        new_groups: dict[str, list[GroupItem]],
        is_incentive: bool = False,
    ) -> ElementInfo | str | None:
        elem_type, element = self.pdfinsight.find_element_by_index(index)
        if self.is_invalid_element(element):
            return None
        text = get_elem_text(self.pdfinsight, element)
        all_indices = {index}
        if para_merged := element.get("page_merged_paragraph"):
            skip_indices.update(para_merged["paragraph_indices"])
            all_indices.update(para_merged["paragraph_indices"])
        if is_paragraph_elt(element) and (not text or (element["type"] != "PARAGRAPH_2" and not P_IS_SEN.search(text))):
            # 遇到被识别为段落的目录，需要判断是终止还是跳过
            # http://************:55647/#/project/remark/257648?treeId=10363&fileId=68744&schemaId=15&projectId=17&schemaKey=B80&page=188
            return text if text in self.pdfinsight.syllabus_titles or P_CHAPTER_PREFIX.search(text) else None
        tbl_share_types = set()
        group_keys = set()
        alias = set()
        elem_share_type = None
        if text:
            if (
                not self.is_test
                and index not in self.ai_alias_adop_days
                and P_ADOPTED_SCHEME.search(text)
                and P_VALID_FOR_GPT.search(text)
            ):
                # 可能漏掉的内容，GPT再提一次
                # TODO：后续helper标注完整后，删除这块逻辑
                for idx, schemes in get_scheme_results(index, text).items():
                    alias.update(sch.alias for sch in schemes if sch.alias)
                    self.ai_alias_adop_days[idx].update(schemes)
            if self.is_holder_company(text):
                return None
            group_keys = self.get_multi_group_keys(text, strict=not is_incentive)
            if is_incentive:
                elem_share_type = guess_share_type(text)
        if is_table_elt(element):
            header_keys = set()
            # 提取表格的跨页合并表格
            # http://************:55647/#/project/remark/250811?treeId=42901&fileId=68591&schemaId=15&projectId=17&schemaKey=B76
            for tbl_element in self.pdfinsight.continuous_tables(element):
                table = parse_table(tbl_element, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                header_texts = table_headers(table)
                header_keys.update(
                    chain.from_iterable(
                        self.get_multi_group_keys(ht, exclude_default=True, strict=False) for ht in header_texts
                    )
                )
                # 注意： 如果存在表格，且表格中有number of shares或number of options，则以此为准
                if is_incentive and (tbl_share_type := guess_tbl_share_type(table)):
                    tbl_share_types.add(tbl_share_type)

                foot_indices = [note["index"] for note in table.footnotes]
                skip_indices.update(foot_indices)
                all_indices.update(foot_indices)
            group_keys.update(header_keys)
            elem_share_type = self.tbl_element_share_type(tbl_share_types | {elem_share_type})
        elif clean_txt(element["text"]).endswith(tuple(R_MIDDLE_DASHES + ":：")):
            as_follow_indices = []
            if follow_tables := find_as_follow_tables(self.pdfinsight, element, ignore_pattern=P_ONLY_DATE):
                # as follow为表格
                for next_elem in follow_tables:
                    next_table = parse_table(next_elem, tabletype=TableType.TUPLE, pdfinsight_reader=self.pdfinsight)
                    header_texts = table_headers(next_table)
                    group_keys.update(
                        chain.from_iterable(
                            self.get_multi_group_keys(ht, exclude_default=True, strict=False) for ht in header_texts
                        )
                    )
                    if is_incentive and (tbl_share_type := guess_tbl_share_type(next_table)):
                        tbl_share_types.add(tbl_share_type)
                    as_follow_indices.extend([next_elem["index"], *[note["index"] for note in next_table.footnotes]])
                    # TODO：后续helper标注完整后，删除这块逻辑
                    group_keys_, alias_ = self.get_adoption_from_elements(next_table.footnotes)
                    group_keys.update(group_keys)
                    alias.update(alias_)
                elem_share_type = self.tbl_element_share_type(tbl_share_types | {elem_share_type})
            elif follow_elements := find_as_follow_paras(
                self.pdfinsight, element, P_C_FOLLOW_PREFIX, include_start=False
            ):
                as_follow_indices = [e["index"] for e in follow_elements]
                # TODO：后续helper标注完整后，删除这块逻辑
                group_keys_, alias_ = self.get_adoption_from_elements(follow_elements)
                group_keys.update(group_keys)
                alias.update(alias_)
            if as_follow_indices:
                skip_indices.update(range(index, max(as_follow_indices) + 1))
                all_indices.update(as_follow_indices)
        group_keys = subtract_keys(group_keys, alias)
        skip_keys, new_keys = set(), set()
        if len(group_keys) > 1 and not is_incentive:
            group_keys = self.merge_group_keys(group_keys)
        for group_key in group_keys:
            existed, new_key = self.trans_to_existed_key(group_key, new_groups)
            if not existed and self.is_skip_group_key(group_key, text):
                skip_keys.add(group_key)
            else:
                new_keys.add(group_key)
        return ElementInfo(
            element.get("syllabus") or 0,
            element.get("page"),
            elem_share_type,
            tbl_share_types,
            new_keys,
            skip_keys,
            sorted(all_indices),
            element.get("outline"),
        )

    def get_adoption_from_elements(self, elements: list[dict]) -> (set, set):
        """
        从脚注或者as follow语句中提取adoption date
        TODO：后续helper标注完整后，删除这块逻辑
        """
        group_keys, aliases = set(), set()
        if self.is_test:
            return group_keys, aliases
        for element in elements:
            follow_text = clean_txt(element["text"], remove_cn_text=True)
            group_keys.update(extract_group_key(follow_text, self.share_type, ignore_case=True))
            # GPT再提一次adoption date
            follow_idx = element["index"]
            if follow_idx in self.ai_alias_adop_days or not is_paragraph_elt(element, strict=True):
                continue
            for idx, schemes in get_scheme_results(follow_idx, follow_text).items():
                aliases.update(sch.alias for sch in schemes if sch.alias)
                self.ai_alias_adop_days[idx].update(schemes)
        return group_keys, aliases

    def get_multi_group_keys(self, text, exclude_default=False, strict=True) -> set[str]:
        group_keys = extract_group_key(text, self.share_type, multi=True, strict=strict)
        if exclude_default:
            return subtract_keys(group_keys, self.default_group_keys)
        return group_keys

    def is_default_key(self, group_key):
        return any(
            is_same_key(self.share_type, group_key, key) for key in {*SPECIAL_GROUP_KEYS, *self.default_group_keys}
        )

    def merge_group_results(self, res_groups: dict[str, list[GroupItem]], note_groups: dict[str, list[GroupItem]]):
        """
        合并directors章节和notes章节提取到的group keys
        1. 生效日期为同一天的分组直接合并，把note章节的key往其他章节合并
        2. 互为别名的直接合并
        3. 丢弃生效日>year_end的scheme
        http://************:55647/#/project/remark/234414?treeId=38065&fileId=66691&schemaId=15&projectId=17&schemaKey=B76
        """
        # 1. 如果DR和NOTES章节都仅有一个key，且其中一个为默认key，直接合并
        if len(res_groups) == len(note_groups) == 1:
            # http://************:55647/#/project/remark/233191?treeId=10935&fileId=66487&schemaId=15&projectId=17&schemaKey=B82
            dr_key, note_key = get_first_key(res_groups), get_first_key(note_groups)
            if self.is_alias_same_key(dr_key, note_key) or self.is_default_key(note_key):
                res_groups[dr_key].extend(self.de_duplicate_items(get_first_value(note_groups), res_groups[dr_key]))
                self.merge_alias_key(dr_key, note_key)
                return
            if self.is_default_key(dr_key):
                dr_items = res_groups.pop(dr_key)
                res_groups[note_key] = dr_items + self.de_duplicate_items(get_first_value(note_groups), dr_items)
                self.merge_alias_key(note_key, dr_key)
                return

        # 2. 先根据key或别名或配置合并
        pop_keys = set()
        for note_key, items in note_groups.items():
            existed, exist_key = self.trans_to_existed_key(note_key, res_groups)
            if existed:
                pop_keys.add(note_key)
                res_groups[exist_key].extend(self.de_duplicate_items(items, res_groups[exist_key]))

        for pop_key in pop_keys:
            note_groups.pop(pop_key)

        # 3. 再找出notes以外章节的adopted day，并与notes章节结果合并
        pop_keys, invalid_keys = set(), set()
        new_groups = {}
        for key, items in res_groups.items():
            days = self.scheme_day_map[key]
            if len(days) == 1 and self.is_invalid_day(get_first_key(days)):
                invalid_keys.add(key)
            for note_key, note_items in note_groups.items():
                if any(self.is_alias_same_key(note_key, key) for key in invalid_keys):
                    pop_keys.add(note_key)
                    continue
                can_merge = self.can_merge_key(key, note_key)
                if not can_merge:
                    continue

                use_note_key = self.use_note_key(key, note_key)
                # 别名或adopted day关联的组合并
                if self.is_default_key(key) and not self.is_default_key(note_key) or use_note_key:
                    new_groups[note_key] = items + self.de_duplicate_items(note_items, items)
                    invalid_keys.add(key)
                    if use_note_key and not self.is_default_key(key):
                        self.merge_alias_key(note_key, key)
                else:
                    res_groups[key].extend(self.de_duplicate_items(note_items, res_groups[key]))
                    self.merge_alias_key(key, note_key, res_groups)
                pop_keys.add(note_key)
                break

        # 4. 丢弃非法key
        for key in invalid_keys:
            logger.debug(f"drop key: {key}")
            res_groups.pop(key, None)

        if new_groups:
            res_groups |= new_groups

        # 5. notes章节其余的key，与director章节有关联的直接合并，否则不合并
        # TODO 名称与directors章节重叠则合并？
        for note_key, note_items in note_groups.items():
            if note_key in pop_keys or note_key in invalid_keys:
                continue
            res_groups[note_key] = note_items

    def can_merge_key(self, key, note_key) -> bool:
        """
        判断两个key是否可以合并
        """

        def is_same_year_key(days_, key1, key2):
            if not days_:
                return False
            year = get_first_key(days_)[:4]
            if key2 in {year + key1, key1 + year}:
                return True
            return False

        # 其中一个key为默认key，且+underthe=另一个key的前缀，则合并
        # http://************:55647/#/project/remark/250811?treeId=42901&fileId=68591&schemaId=15&projectId=17&schemaKey=B80
        if self.is_default_key(note_key) and key.startswith(
            (f"{note_key}underthe", f"{standardize_group_key(note_key)}underthe")
        ):
            return True
        if self.is_default_key(key) and note_key.startswith(
            (f"{key}underthe", f"{standardize_group_key(key)}underthe")
        ):
            return True

        days, note_days = self.scheme_day_map[key], self.scheme_day_map[note_key]
        # 两个scheme只差一个年份，且没日期的scheme的adoption date在同一年，则合并
        # http://************:55647/#/project/remark/232881?treeId=38070&fileId=66435&schemaId=15&projectId=17&schemaKey=B82
        if is_same_year_key(days, key, note_key):
            return True
        if is_same_year_key(note_days, note_key, key):
            return True

        same_days = same_adopted_days(days, note_days)
        #  一个日期下有多个分组时，名称互相包含才认为是同一个key
        # http://************:55647/#/project/remark/235176?treeId=5614&fileId=66818&schemaId=15&projectId=17&schemaKey=B76
        if not same_days or (
            any(len(subtract_keys(self.day_scheme_map[d], {key, note_key})) > 1 for d in same_days)
            and not self.is_related_key(key, note_key)
        ):
            return False
        return True

    def use_note_key(self, key, note_key) -> bool:
        """
        与note章节的key合并时，是否使用note章节的key
        """

        if self.is_default_key(key) and not self.is_default_key(note_key):
            return True
        if P_YEAR.search(note_key) and not P_YEAR.search(key) and len(key) < len(note_key):
            return True
        return False

    # def get_group_key_from_syllabus(self, syllabus, check_neg=True) -> SyllabusGroup:
    #     if check_neg and self.neg_chapter_regs.search(syllabus["title"]):
    #         return SyllabusGroup(set())
    #     key_words = extract_group_key(syllabus["title"], self.share_type)
    #     if not key_words or key_words.issubset(set(self.default_group_keys)) and len(range(*syllabus["range"])) <= 2:
    #         return SyllabusGroup(set())
    #     parent_indices = set()
    #     # root_parent_index = 0
    #     final_chapter = None
    #     is_invalid = False
    #     for syll in self.pdfinsight.syllabus_reader.full_syll_path(syllabus)[::-1]:
    #         # http://************:55647/#/project/remark/233215?treeId=37654&fileId=66491&schemaId=15&projectId=17&schemaKey=B74
    #         if check_neg and self.neg_chapter_regs.search(syll["title"]):
    #             is_invalid = True
    #         if syll["parent"] in (-1, None):
    #             break
    #         # 章节关系有误：http://************:55647/#/project/remark/234893?treeId=9336&fileId=66771&schemaId=15&projectId=17&schemaKey=B76&page=39
    #         if (
    #             syll["index"] == syllabus["index"]
    #             or PdfinsightSyllabus.relation_of_2_syllabuses(syll["title"], syllabus["title"], distance=5) != -1
    #         ):
    #             continue
    #         parent_indices.add(syll["index"])
    #         if not (parent_words := extract_group_key(syll["title"], self.share_type)):
    #             continue
    #         parent_key = get_first_key(parent_words)
    #         # 排除有其他类型关键词的标题
    #         # TODO 这里未考虑incentive
    #         if any(extract_group_key(syll["title"], type_) for type_ in GROUP_KEY_REGS_MAP if type_ != self.share_type):
    #             continue
    #         # 父章节组名包含子章节，取父章节
    #         if not key_words or get_first_key(key_words) in parent_key:
    #             # 父章节的所有子章节中可以提取到多个分组时 不取父章节
    #             if not key_words:
    #                 key_words = parent_words
    #             elif self.get_group_counts(syll, parent_key) <= 1:
    #                 # http://************:55647/#/project/remark/234666?treeId=7850&fileId=66733&schemaId=15&projectId=17
    #                 key_words.update(parent_words)
    #                 final_chapter = syll
    #     if is_invalid:
    #         return SyllabusGroup(parent_indices)
    #     if not final_chapter:
    #         final_chapter = syllabus
    #     return SyllabusGroup(parent_indices, final_chapter, key_words)

    # def get_group_key_from_element(
    #     self, element, text=None, check_neg=True, multi=False, only_ele_keys=False
    # ) -> str | set[str] | None:
    #     """
    #     结果为None时表示元素块需要丢弃
    #     从元素中提取关键词，提取逻辑：
    #     1. 先提取最近章节的关键词
    #     2. 再提取元素关键词，若为表格从表名中提关键词
    #     3. 长度都为1时，两者中取不是默认关键词的一组
    #     4. 元素关键词长度>1时取两者交集
    #     5. 元素关键词太多时，保底用章节关键词
    #     TODO 排除一些特殊关键字，例如XDC
    #     """
    #
    #     # 先获取元素章节关键词
    #     chapter_group_keys, chapter_group_key = set(), ""
    #     if chapter := self.pdfinsight.syllabus_dict.get(element.get("syllabus")):
    #         chapter_group = self.get_group_key_from_syllabus(chapter, check_neg=check_neg)
    #         if (chapter_group_keys := chapter_group.keywords) and len(chapter_group.keywords) == 1:
    #             chapter_group_key = get_first_key(chapter_group_keys)
    #
    #     clean_text = text or get_elem_text(self.pdfinsight, element)
    #     if not clean_text:
    #         return chapter_group_key
    #
    #     elem_group_keys = extract_group_key(clean_text, self.share_type, multi=multi)
    #     if not elem_group_keys and not chapter_group_key:
    #         return ""
    #
    #     if elem_group_keys == {self.default_group_keys[0]}:
    #         return chapter_group_key or self.default_group_keys[0]
    #
    #     if len(elem_group_keys) >= 1 and self.default_group_keys[0] in elem_group_keys:
    #         elem_group_keys.remove(self.default_group_keys[0])
    #
    #     if not elem_group_keys:
    #         return chapter_group_key or self.default_group_keys[0]
    #     if only_ele_keys:
    #         # 只获取元素中的group_keys
    #         return elem_group_keys
    #     if cross_keys := elem_group_keys & chapter_group_keys:
    #         return get_first_key(cross_keys)
    #
    #     if len(elem_group_keys) == 1:
    #         return get_first_key(elem_group_keys)
    #     # 保底用章节关键字
    #     return chapter_group_key

    def get_group_counts(self, parent_syllabus, parent_keyword):
        # 仅遍历父章节的第一级子章节，若取到两个以上不同关键词，则不能取父章节关键词
        # 多个子章节关键词都相同： http://************:55647/#/project/remark/232881?treeId=38070&fileId=66435&schemaId=15&projectId=17&schemaKey=B93.1
        group_keys = set()
        for child_syll_index in parent_syllabus["children"]:
            child_syll = self.pdfinsight.syllabus_reader.syllabus_dict[child_syll_index]
            if P_IGNORE_CHAPTERS.search(child_syll["title"]):
                continue
            for type_ in {"option", "award"}:
                if keys := extract_group_key(child_syll["title"], type_):
                    group_keys.update(keys)
        if len(group_keys) == 1:
            return 1
        # 父章节为2021 RSU PLAN，子章节中有一个与父章节相同，其余全部是RSUS
        # http://************:55647/#/project/remark/233964?treeId=38033&fileId=66616&schemaId=15&projectId=17&schemaKey=B76
        return (
            1
            if any(key == parent_keyword for key in group_keys) and all(key in parent_keyword for key in group_keys)
            else len(group_keys)
        )

    def has_valid_children(self, syllabus):
        """
        判断章节下是否有有效章节
        """

    def count_children(self, child_titles, is_incentive):
        """
        TODO 排除trance 和 share award scheme I/II/III/IV
        """
        count = 0
        has_valid = False
        for title in child_titles:
            if P_IGNORE_CHAPTERS.search(title):
                continue
            has_valid = True
            if is_incentive or P_AS_INCENTIVE_TITLE.search(title) or self.is_current_type(title):
                count += 1
        return has_valid, count

    def debug_group_info(self, res_groups: dict[str, GroupInfo]):
        if logger.level != logging.DEBUG:
            return
        logger.debug(f"--------- [{self.share_type}] total count: {len(res_groups)} ---------")
        logger.debug("scheme_day_map: %s", dict(self.scheme_day_map))
        logger.debug("day_scheme_map: %s", dict(self.day_scheme_map))
        for key, info in res_groups.items():
            logger.debug(
                f"[{self.share_type}] [key={key}] is_subsidiary={info.is_subsidiary}, adopt_year={info.adopt_year}, adopt_days={info.adopt_days}, aliases={self.aliases[key]}"
            )
            elements = []
            elements_chapters = set()
            for group_item in info.items:
                if group_item.is_chapter:
                    logger.debug(
                        f"[{self.share_type}] [key={key}] chapter: page={group_item.page + 1}, index={group_item.index}, range={group_item.range}, chapter_idx={group_item.chapter_index}, title={group_item.title}"
                    )
                else:
                    elements.append(group_item.index)
                    chapter = self.pdfinsight.syllabus_dict[group_item.chapter_index]
                    elements_chapters.add(
                        f"(page={chapter['dest']['page_index'] + 1}, elem_index={chapter['element']}, title={chapter['title']})"
                    )
            if elements:
                # 如果包含了元素块，只打印索引
                logger.debug(f"[{self.share_type}] [key={key}] elements: {trans_indices_to_tuple(elements)}")
                logger.debug(f"[{self.share_type}] [key={key}] elements' chapters: " + ",".join(elements_chapters))
        return


async def get_or_create_share_meta(
    file: NewFile,
    question: NewQuestion,
    report_year: str,
    company: str,
    helper_answers: dict[str, list[dict]] = None,
) -> dict:
    metadata = {}
    share_meta = await FileShareMeta.find_by_fid(file.id)
    debug_share_group = ctx_debug_share_group.get()
    if not debug_share_group and share_meta:
        metadata["share_info"] = share_meta.share_info
        metadata["share_group"] = share_meta.share_group
        # 子公司分组信息
        metadata["share_subsidiary"] = share_meta.share_subsidiary
    else:
        pdfinsight = PdfinsightReader.from_path(file.pdfinsight_path(abs_path=True), pdf_hash=file.pdf)
        crude_elements = {
            share_type: get_crude_elements(question, pdfinsight, share_type) for share_type in ("option", "award")
        }
        metadata["share_info"] = get_share_info(pdfinsight, crude_elements)
        metadata.update(**{"share_group": {}, "share_subsidiary": {}})
        share_crude_elems = await get_jura21_helper_and_ai_schemes(pdfinsight, file, helper_answers=helper_answers)
        for share_type in ("option", "award"):
            group = ShareGroup(
                pdfinsight,
                share_type,
                share_crude_elems,
                metadata["share_info"],
                report_year=report_year,  # metadata["report_year"],
                company=company,
            )
            metadata["share_group"][share_type] = group.get_group_info()
            # 子公司分组信息
            metadata["share_subsidiary"][share_type] = get_subsidiaries_group(
                pdfinsight, share_type, crude_elements, metadata["share_info"][f"has_share_{share_type}"]
            )
        # 更新file_share_meta表记录
        data = {
            "fid": file.id,
            "share_info": metadata["share_info"],
            "group_info": {
                share_type: ShareGroup.to_dict(group) for share_type, group in metadata["share_group"].items()
            },
            "subsidiary_info": metadata["share_subsidiary"],
        }
        async with pw_db.atomic():
            log_str = "insert to"
            if debug_share_group and share_meta:
                # debug时，若记录存在则删除
                log_str = "overwrite"
                await pw_db.execute(FileShareMeta.delete().where(FileShareMeta.fid == file.id))
            try:
                record = await pw_db.create(FileShareMeta, **data)
                logger.info(f"{log_str} file_share_meta: file_id={record.fid}")
            except (peewee.IntegrityError, psycopg2.IntegrityError) as e:
                # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4972
                logger.warning(f"insert to file_share_meta failed [file_id={file.id}]: {e}")
    return metadata

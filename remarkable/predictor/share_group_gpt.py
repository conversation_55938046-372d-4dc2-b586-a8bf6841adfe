import logging
import re
from collections import defaultdict
from dataclasses import dataclass
from functools import lru_cache
from itertools import chain
from json import JSONDecodeError

from pydantic import BaseModel as PydanticBaseModel

from remarkable.common.common_pattern import R_MIDDLE_DASH
from remarkable.common.constants import PDFInsightClassEnum
from remarkable.common.pattern import MatchMulti, NeglectPattern
from remarkable.common.util import clean_txt, split_paragraph
from remarkable.pdfinsight.reader import Pd<PERSON><PERSON><PERSON>eader
from remarkable.predictor.common_pattern import P_SHARE_INCENTIVE, R_EN_MONTH, R_EN_NUM, R_SHARE_INCENTIVE
from remarkable.predictor.eltype import ElementClassifier
from remarkable.predictor.hkex_predictor.pattern import R_DAY
from remarkable.predictor.hkex_predictor.schemas.pattern import P_APPELLATION, P_DATE, R_AWARD_KEYWORD_LIST
from remarkable.services.chatgpt import OpenAIClient

logger = logging.getLogger(__name__)

R_PLAN = r"(scheme|plan)"
R_PRE_POST_IPO = r"(Pre|Post)[-\s]?IPO"
R_TYPE_PREFIX = rf"(first\s+|second\s+|{R_PRE_POST_IPO}\s+|SmarTone\s+|(?<!senior )management\s+|employee\s+|old\s+|(?<!\ba )new\s+|\d{{4}}\s+)"
# scheme的后缀，一般是年份，I II 1-4等
R_SCHEME_SUFFIX = r"((scheme|plan)\s*([IV1-4]+(\s*PLUS)?|(for\s*)?\d{4}|of\s*\s*subsidiaries)?\b)"
R_YEAR = r"\b(?P<year>20\d{2}\b\s*)"
R_QUOTES = r"[\"“”]"
P_RIGHT_QUOTES = re.compile(rf"{R_QUOTES}[)）][,，]?")
# rf"[(（\[](the\s*)?{R_QUOTES}(?!adopt)(?P<scheme>([-\w]+\s+){{2,4}}({R_PLAN}|RSU)){R_QUOTES}[)）\]]"
R_ALIAS_START = rf"(\bof\s*the\s*company\s*)?((was|is)?\s*adopted|mandate\s*limit|(adopted\s*)?on\s*([a-z0-9,，]+\s+){{1,3}})?\s*[(（](collectively[，,]\s*)?(the\s*)?{R_QUOTES}(?!Eligible|Participant)"
P_ALIAS_START = re.compile(R_ALIAS_START, re.I)
R_ALIAS_BEFORE = rf"( a\s*)?(?P<before>{R_TYPE_PREFIX}{{0,2}}(shares?\s*|option\s*|award(ed)?\s*|stock\s*|purchase\s*|incentive\s*|scheme\s*|plan\s*|restricted\s*|units?\s*|[AH]\s+){{2,}}|{R_TYPE_PREFIX}{{0,2}}rs[UA]s?\s*{R_PLAN}?){R_ALIAS_START}"
P_ALIAS_BEFORE = re.compile(R_ALIAS_BEFORE, re.I)
R_ALIAS = rf"(scheme|plan|rsus?|(share|stock)\s*unit|restricted\s*shares)\s*{R_ALIAS_START}(the\s*)?(?P<scheme>([-\w]+\s+){{1,5}}[-\w]+)(?<!day)(?<!date)(?<!participant)(?<!participants)(?<!persons)(?<!rules)(?<!deed){R_QUOTES}(\s*or\s[^）)]+?)?[)）]"
P_SCHEME_ALIAS = re.compile(R_ALIAS, re.I)
P_NEW = re.compile(r"^new\s*")
R_ADOPT = r"\b(adopt|set\s*up|establish|operat|approv|effect)"
# 判断句子是否为采纳日期句子
P_ADOPTED_SCHEME = NeglectPattern.compile(
    match=MatchMulti.compile(
        MatchMulti.compile(
            rf"{R_PLAN}|{R_SHARE_INCENTIVE}|\brsu",
            rf"{R_ADOPT}|\brefer\s*to\b",
            rf"{R_DAY}|{R_EN_MONTH}[,，\s]+(\d{{2}}|\d{{4}})\b|\b(in|on|at|of)\s+\d{{4}}",
            operator=all,
        ),
        R_ALIAS,
        operator=any,
    ),
    unmatch=r"modification|to\s*terminate|adopted\s*([2-9]|two|three|four|five|six|seven|eight|nine|ten|eleven|twelve)",
)
# 有以下标点符号表示是一个句子  '(3) Consultant C — introduce clients to the Company'
P_IS_SEN = re.compile(rf"^{R_MIDDLE_DASH}|[;；:：—]|\.$")
# 满足以下条件的句子，才使用GPT提取
P_VALID_FOR_GPT = MatchMulti.compile(rf"{R_ALIAS}|{R_DAY}|{R_YEAR}", P_IS_SEN, operator=all)
# Scheme名称正则
P_SCHEME_NAME = MatchMulti.compile(
    MatchMulti.compile(R_TYPE_PREFIX, R_PLAN, operator=all),
    r"(stock|share)\s*option",
    r"(option|award)\s*scheme",
    *R_AWARD_KEYWORD_LIST,
    P_SHARE_INCENTIVE,
    # http://100.64.0.105:55647/#/project/remark/233197?treeId=13539&fileId=66488&schemaId=15&projectId=17&schemaKey=B82
    r"share\s*grant\s*scheme",
    operator=any,
)

R_REMAIN_LIFE = [
    r"\b(be|is|are)\s*valid",
    r"\b(remain|life|period|duration|commencing|in\s*force)",
    rf"\b(\d+|{R_EN_NUM})\s*(year|month|day)",
]
P_REMAIN_LIFE = MatchMulti.compile(*R_REMAIN_LIFE, operator=any)
# 只处理标题满足以下正则的章节
P_SCHEME_TITLE = MatchMulti.compile(
    P_SCHEME_NAME,
    # http://100.64.0.105:55647/#/project/remark/233455?treeId=9357&fileId=66531&schemaId=15&projectId=9357&schemaKey=B82&page=151
    r"equity\s*settled\s*share-based\s*transactions",
    # http://100.64.0.105:55647/#/project/remark/233018?treeId=24500&fileId=66458&schemaId=15&projectId=17&schemaKey=B82
    r"equity\s*plans$",
    operator=any,
)

DEFAULT_GROUP_KEY_MAP = {
    "option": ["shareoptionscheme", "shareoption"],  # "shareoptionschemes"
    "award": [
        "shareawardscheme",
        # "shareawardschemes",
        "shareaward",
        "rsu",
        "rsa",
        "restrictedshareunit",
        "restrictedshareunits",
        "restrictedshareaward",
        "restrictedshareawardscheme",
    ],
}
# 一些特殊的key，比default_key的优先级还低，因为它可能是share的单位
SPECIAL_GROUP_KEYS = {
    "sharescheme",
    "restrictedshares",
    "rsus",
}
P_NUM_DATE = re.compile(r"^\d{4}-\d{2}-\d{2}$")


@dataclass
class Scheme:
    index: int  # 所属段落的index
    name: str | None = None
    orig_name: str | None = None
    alias: str | None = None
    orig_alias: str | None = None
    day_str: str | None = None
    # 是否从计划的生命周期提取，这种数据优先级较低
    from_life: bool = False

    def __str__(self):
        return f"name={self.name}, alias={self.alias}, day_str={self.day_str}"

    def __hash__(self):
        return hash((self.index, self.name, self.orig_name, self.day_str))


def ai_alias_and_adop_day(
    pdfinsight: PdfinsightReader,
    crude_elements: list[dict],
    fid: int,
) -> dict[int, list[Scheme]]:
    result = defaultdict(list)
    if not crude_elements:
        return result
    existed_indices = set()
    for ans in crude_elements:
        index = ans["element_index"]
        if index in existed_indices or ans["element_type"] != PDFInsightClassEnum.PARAGRAPH.value:
            continue
        existed_indices.add(index)
        text = clean_txt(ans["text"])
        try:
            element_type, element = pdfinsight.find_element_by_index(index)
        except IndexError:
            # TODO: fid: 60995
            logger.warning(f"{fid=}, {index=} not found in pdfinsight")
            continue
        if not element:
            continue
        if not ElementClassifier.like_paragraph(element):
            continue
        if not text or pdfinsight.is_chinese_elt(element) or not P_VALID_FOR_GPT.search(text):
            # logger.debug("Skip invalid answer: index=%s, score=%s, text=%s", index, ans["score"], text)
            continue

        # if not (chapters := pdfinsight.find_syllabuses_by_index(index)):
        #     continue
        # # TODO 这里后续考虑保留definitions章节的内容
        # if not any(P_SCHEME_TITLE.search(chap["title"]) for chap in chapters):
        #     continue
        for idx, schemes in get_scheme_results(index, text).items():
            result[idx].extend(schemes)
    return result


def get_scheme_results(index: int, text: str) -> dict[int, set]:
    result = defaultdict(set)
    for sentence in split_paragraph(text):
        if not sentence or not P_VALID_FOR_GPT.search(sentence) or P_APPELLATION.search(sentence):
            continue
        if P_REMAIN_LIFE.search(sentence) and (scheme := life_to_scheme(index, sentence)):
            result[index].add(scheme)
            continue
        if not P_ADOPTED_SCHEME.search(sentence) or not (ai_answer := extract_adop_date_by_openai(sentence)):
            continue
        logger.debug("- Q: [index=%s] %s", index, sentence)
        logger.debug("- A: %s", ai_answer)
        try:
            if schemes := gpt_answer_to_scheme(index, ai_answer, sentence):
                result[index].update(schemes)
        except JSONDecodeError:
            logger.warning("Json parsing error: %s", ai_answer)
    return result


class AdoptDateResModel(PydanticBaseModel):
    name: str
    alias: str | None = None
    adoption_date: str | None = None


class AdoptDateResModels(PydanticBaseModel):
    items: list[AdoptDateResModel] = None


def gpt_answer_to_scheme(elem_index: int, ai_answer: list[AdoptDateResModel], sentence: str) -> list[Scheme]:
    schemes = []

    default_keys = set(chain.from_iterable(DEFAULT_GROUP_KEY_MAP.values()))
    default_keys.update({f"{key}s" for key in default_keys})
    for res in ai_answer:
        orig_name, orig_alias, day_str = res.name, res.alias, res.adoption_date
        if day_str and not P_NUM_DATE.search(day_str):
            day_str = None
        if not orig_name:
            continue
        if orig_alias:
            orig_alias = orig_alias.lower()
            if orig_alias in ("plan", "scheme") or "adopt" in orig_alias:
                orig_alias = None
        if not orig_alias and not day_str:
            continue
        # 如果openai提取到的计划是以new开始，排除一下特殊情况
        name, alias = orig_name, orig_alias
        if name.startswith("new") and " a " + name in sentence:
            name = P_NEW.sub("", name)
        name = lower_keyword(clean_txt(name, remove_blank=True))
        if alias:
            alias = lower_keyword(clean_txt(alias, remove_blank=True))
            if alias == name:
                alias = None
            elif name in default_keys:
                name = alias
                alias = None
        schemes.append(
            Scheme(
                index=elem_index,
                name=name,
                orig_name=orig_name,
                alias=alias,
                orig_alias=orig_alias,
                day_str=day_str,
            )
        )
    logger.debug("- S: %s", "; ".join(str(s) for s in schemes))
    return schemes


async def get_schemes_from_b72_b82(pdfinsight: PdfinsightReader, crude_elements: list[dict]) -> list[Scheme]:
    """
    从question.crude_answer中提取B72和B82的初步定位答案，并从中提取开始日期作为排序依据
    """
    existed_indices = set()
    schemes = []
    for ans_element in crude_elements:
        elem_index = ans_element["element_index"]
        if elem_index in existed_indices or ans_element["element_type"] != PDFInsightClassEnum.PARAGRAPH.value:
            continue
        existed_indices.add(elem_index)
        text = clean_txt(ans_element.get("text"), remove_cn_text=True)
        if not text:
            continue
        try:
            _, element = pdfinsight.find_element_by_index(elem_index)
        except IndexError:
            continue
        if not (chapters := pdfinsight.find_syllabuses_by_index(elem_index)):
            continue
        if not any(P_SCHEME_TITLE.search(chap["title"]) for chap in chapters):
            continue
        if not (P_DATE.search(text) and P_REMAIN_LIFE.search(text)):
            continue
        for sentence in split_paragraph(text, remove_cn_text=True):
            if not (P_DATE.search(sentence) and P_REMAIN_LIFE.search(sentence)) or P_APPELLATION.search(sentence):
                continue
            if scheme := life_to_scheme(elem_index, sentence):
                schemes.append(scheme)
    return schemes


def life_to_scheme(elem_index, sentence):
    info: StartDateResModel = extract_start_date_by_openai(sentence)
    if not info or not info.start_date:
        return None
    if P_NUM_DATE.search(info.start_date):
        logger.debug(f"-- Q: [index={elem_index}] {sentence}")
        logger.debug(f"-- A: {info}")
        name = info.scheme or ""
        return Scheme(
            index=elem_index,
            name=name.replace(" ", "").lower(),
            orig_name=name,
            day_str=info.start_date,
            from_life=True,
        )


def lower_keyword(keyword, ignore_case=False):
    """
    一些默认值，如果不希望被过滤，可以在这个函数中保留原始大小写
    """
    if ignore_case:
        return keyword.lower()
    # 这两种情况保持大小写：ShareOptionScheme, ShareAwardScheme
    if keyword in ("ShareOptionScheme", "ShareAwardScheme"):
        return keyword
    return keyword.lower()


@lru_cache()
def extract_adop_date_by_openai(text) -> list[AdoptDateResModel]:
    """
    openai提取scheme的简称和采纳日期
    """
    messages = [
        {
            "role": "system",
            "content": """你是一位专业的上市公司审计专家。根据我提供的内容，请详细提取所有相关的购股权计划或股票奖励计划的名称、每个计划的简称及采纳日期。确保遵循以下具体要求：
1. `名称`：指购股权计划或股票奖励计划的全称，一般是`采纳`、`批准`、`实行`、`成立`等动词的宾语，若句子中没有这些动词，则是主语，提取名称时，不要提取固定修饰词`of the company`。在回答中请用属性`name`表示。
2. `简称`：必须是用双引号括起来的计划名称，如果句子中没有就不提取，如果提取到的内容包含Adoption或其他不是计划名称别名的内容则丢弃答案；提取时省略所有“the”，在回答中请用属性`alias`表示。
3. `采纳日期`：优先提取计划设立的日期或计划被采纳的日期，如果没有这些信息，尝试提取计划被批准的日期、计划实行的日期或计划生效的日期，如果文档提到“详情请参阅某日期的公司公告”，请提取最早的公告日期，此外的其他任何日期均不考虑。如果不确定是否为采纳日期，请不要提取。日期必须具体，并格式化为`YYYY-MM-DD`。在回答中请用属性`adoption_date`表示。
4. 如果一个句子中只有简称，名称请也使用简称。
5. 如果某个计划只提到了名称，却没有提取到简称或采纳日期，请忽略这个计划。
6. 如果一个句子中未提及任何简称或采纳日期，请返回`[]`。
7. 请严格按照规则提取，只提取原文相关内容，原则是允许提取不到，但不允许提到错误的信息。
回答应以严格的JSON格式提供，格式如下：
```json
[
    {"name": "scheme1的名称", "alias": "scheme1的简称", "adoption_date": "scheme1的采纳日期"},
    {"name": "scheme2的名称", "alias": "scheme2的简称", "adoption_date": "scheme2的采纳日期"}
]
```
""",
        },
        {
            "role": "user",
            "content": "With a view to saving costs, it was resolved by the Board to terminate the Share Award Plan with effect from 21 December 2020.",
        },
        {
            "role": "assistant",
            "content": "[]",
        },
        {
            "role": "user",
            "content": "As the 2024 Equity Incentive Plan has taken effect on November 10, 2023, the 2014 Stock Option Plan was terminated on the same date",
        },
        {
            "role": "assistant",
            "content": "[{'name': '2024 Equity Incentive Plan', 'adoption_date': '2023-11-10'}]",
        },
        {
            "role": "user",
            "content": "The Company adopted a restricted share unit scheme (the “RSU Scheme”) on June 22, 2021 and a share award scheme on 31 May 2022.",
        },
        {
            "role": "assistant",
            "content": '[{"name": "restricted share unit scheme", "alias": "RSU Scheme", "adoption_date": "2021-06-22"}, {"name": "share award scheme", "adoption_date": "2022-05-31"}]',
        },
        {
            "role": "user",
            "content": "The 2002 Share Option Scheme was terminated and a new share option scheme was adopted by the Shareholders on 23 August 2012 (the “2012 Share Option Scheme”).",
        },
        {
            "role": "assistant",
            "content": '[{"name": "share option scheme", "alias": "2012 Share Option Scheme", "adoption_date": "2012-08-23"}]',
        },
        {
            "role": "user",
            "content": "Please refer to the Company’s announcement dated May 21, 2018 for further details of the First Restricted Share Award Scheme, and the Employee Incentive Scheme was approved and adopted by our Board on 31 March 2020.",
        },
        {
            "role": "assistant",
            "content": '[{"name": "First Restricted Share Award Scheme", "adoption_date": "2018-05-21"}, {"name": "Employee Incentive Scheme", "adoption_date": "2020-03-31"}]',
        },
        {
            "role": "user",
            "content": "For details of the Scheme, please refer to the announcement of the Company dated 21 June 2022, 17 February 2023.",
        },
        {
            "role": "assistant",
            "content": '[{"name": "Scheme", "adoption_date": "2022-06-21"}]',
        },
        {
            "role": "user",
            "content": "For further details about the Share Option Scheme, please refer to the Company’s circular dated December 4, 2017.",
        },
        {
            "role": "assistant",
            "content": '[{"name": "Share Option Scheme", "adoption_date": "2017-12-04"}]',
        },
        {
            "role": "user",
            "content": "The Company has appointed a trustee to assist with the administration and vesting of awards granted pursuant to the employee incentive scheme (“the 2020 Employee Incentive Scheme”).",
        },
        {
            "role": "assistant",
            "content": '[{"name": "employee incentive scheme", "alias": "2020 Employee Incentive Scheme"}]',
        },
        {
            "role": "user",
            "content": "On December 31, 2018, the company adopted a new share award scheme (“2023 share award Scheme”).",
        },
        {
            "role": "assistant",
            "content": '[{"name": "share award scheme", "alias": "2023 share award Scheme", "adoption_date": "2018-12-31"}]',
        },
        {
            "role": "user",
            "content": "The Employee Incentive Scheme will be valid and effective for a period of ten years, commencing from the date of the first grant of the Awards, being 31 March 2020.",
        },
        {
            "role": "assistant",
            "content": "[]",  # {"name": "Employee Incentive Scheme", "adoption_date": "2020-03-31"}
        },
        # {
        #     "role": "user",
        #     "content": "The Share Option Scheme shall be valid and effective for a period of ten years commencing from the Adoption Date and will expire on 11 June 2022.",
        # },
        # {
        #     "role": "assistant",
        #     "content": '[]',
        # },
        {
            "role": "user",
            "content": "The 2012 Share Option Scheme became unconditional and effective on 27 August 2012.",
        },
        {
            "role": "assistant",
            "content": '[{"name": "2012 Share Option Scheme", "adoption_date": "2012-08-27"}]',
        },
        {
            "role": "user",
            "content": "(i) The maximum number of Shares in respect of which options may be granted under the 2002 Share Option Scheme shall not (when aggregated with any Shares subject to any other share option scheme(s) of the Company) exceed 10% of the issued share capital of the Company on 29 August 2002, the date on which the 2002 Share Option Scheme was adopted (the “2002 Scheme Mandate Limit”).",
        },
        {
            "role": "assistant",
            "content": '[{"name": "2002 Share Option Scheme", "alias": "2002 Scheme Mandate Limit", "adoption_date": "2012-08-27"}]',
        },
        {
            "role": "user",
            "content": "Pursuant to a resolution of the Board dated 11 April 2014, the Board approved the adoption of the Share Award Scheme under which shares of the Company may be awarded to selected employees for no cash consideration in accordance with its absolute discretion.",
        },
        {
            "role": "assistant",
            "content": '[{"name": "Share Award Scheme", "adoption_date": "2014-04-11"}]',
        },
        {
            "role": "user",
            "content": "Pursuant to an ordinary resolution passed at the extraordinary general meeting of the Company held on 30 June 2021, the Scheme 2012 was terminated and a new share option scheme (the “Scheme 2021”) was adopted.",
        },
        {
            "role": "assistant",
            "content": '[{"name": "share option scheme", "alias": "Scheme 2021", "adoption_date": "2021-06-30"}]',
        },
        {
            "role": "user",
            "content": "Pursuant to an ordinary resolution passed at the annual general meeting of the Company held on 30 June 2022, the new share option scheme (the “Share Option Scheme 2022”) was adopted by the Company",
        },
        {
            "role": "assistant",
            "content": '[{"name": "new share option scheme", "alias": "Share Option Scheme 2022", "adoption_date": "2022-06-30"}]',
        },
        # 这里配置预测
        {"role": "user", "content": text},
    ]
    openai_client = OpenAIClient()
    try:
        res = openai_client.send_message(
            messages, response_format=AdoptDateResModels, options={"temperature": 0.0, "model": "gpt-4o-mini"}
        )
        return res.parsed.items
    except Exception as e:
        logger.exception(e)
    return []


class StartDateResModel(PydanticBaseModel):
    start_date: str | None = None
    scheme: str | None = None


@lru_cache()
def extract_start_date_by_openai(text) -> StartDateResModel | None:
    """
    openai提取计划的开始日期
    """
    messages = [
        {
            "role": "system",
            "content": """你是一位专业的上市公司审计专家。根据我提供的句子，提取句子中的开始日期和计划名称。确保遵循以下具体要求：
1. `开始日期`：一般跟在关键词`from`后面，如果句子中没有提及开始日期，只提及了结束日期， 需要根据句子中提及的生命周期推算开始日期。如果不确定是否为开始日期，请不要提取。请将日期格式转为`YYYY-MM-DD`，在回答中用属性`start_date`表示。
2. `计划名称`：指购股权计划或股票奖励计划的全称，句子中若没有可以不提取，在回答中用属性`scheme`表示。
3. 如果一个句子中未提及任何日期，请返回`{}`。
4. 请严格按照规则提取，只提取原文相关内容，原则是允许提取不到，但不允许提到错误的信息。

回答应以严格的JSON格式提供，格式如下：
```json
{"start_date": "2022-01-01", "scheme": "scheme的名称"}

```
""",
        },
        {
            "role": "user",
            "content": "The Employee Incentive Scheme will be valid and effective for a period of ten years, commencing from the date of the first grant of the Awards, being 31 March 2020.",
        },
        {
            "role": "assistant",
            "content": '{"start_date": "2020-03-31", "scheme": "Employee Incentive Scheme"}',
        },
        {
            "role": "user",
            "content": "The Share Option Scheme shall be valid and effective for a period of ten years commencing from the Adoption Date and will expire on 11 June 2022.",
        },
        {
            "role": "assistant",
            "content": '{"start_date": "2012-06-11", "scheme": Share Option Scheme}',
        },
        # 这里配置预测
        {"role": "user", "content": text},
    ]
    openai_client = OpenAIClient()
    try:
        res = openai_client.send_message(
            messages, response_format=StartDateResModel, options={"temperature": 0.0, "model": "gpt-4o-mini"}
        )
        return res.parsed
    except Exception as e:
        logger.exception(e)
    return None


if __name__ == "__main__":
    text = """The 2002 Share Option Scheme was terminated and a new share option scheme was adopted by the Shareholders on 23 August 2012 (the “2012 Share Option Scheme”).
    """
    for i in range(5):  # noqa
        ans = extract_adop_date_by_openai(text)
        print(ans)
        print(gpt_answer_to_scheme(1, ans, text))

    text1 = """In order to enable the Company to continue granting awards of Shares and/or Share Stapled Units under the PCCW Share Award Schemes, on 12 August 2022, the Board approved the extension of the duration of each of the PCCW Share Award Schemes for a period of 10 years from 15 November 2022."""
    for i in range(5):  # noqa
        # print(extract_start_date_by_openai(text1))
        print(life_to_scheme(1, text1))

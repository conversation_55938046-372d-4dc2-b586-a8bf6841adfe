import re
from collections import defaultdict
from copy import copy

from remarkable.common.common import get_style_of_raw_cell, is_para_elt, is_paragraph_elt, is_table_elt, roman_to_int
from remarkable.common.common_pattern import P_CHAPTER_PREFIX, P_CONTINUED, P_NOTES, P_ONLY_NOTES
from remarkable.common.constants import PDFInsight<PERSON>lassEnum, TableType
from remarkable.common.pattern import MATCH_NEVER, PatternCollection
from remarkable.common.protocol import SearchPatternLike
from remarkable.common.util import P_CHNS, clean_txt, has_english_chars
from remarkable.pdfinsight.parser import ParsedTable, ParsedTableCell, parse_table
from remarkable.pdfinsight.reader import PdfinsightReader
from remarkable.pdfinsight.reader_util import UnifiedParaElement, is_merged_elt
from remarkable.predictor.hkex_predictor.schemas.pattern import P_NOTE_CHAPTER

P_NOT_SKIP_TABLE = PatternCollection([r"set out in the following table:$"], re.I)

P_BRACKET = re.compile(r"[（(【]")
EXCLUDE_SUPERSCRIPT = "^*#@[]{}【】()（）"
R_NUM = r"([\^*#@]|\d+|[a-z]|[ivx]{1,4})"
# 首字母大写
P_CAPITAL = re.compile(r"^[A-Z](?![A-Z])")
# 单元格本身就是脚注
P_CELL_SUPERSCRIPT = re.compile(r"^[\[（(]?([iv]{1,4}|\d{1,2}|[a-z])[)）\]]?$", re.I)
# 单元格内上标
P_SUPERSCRIPT = (
    # http://************:55647/#/project/remark/233191?treeId=10935&fileId=66487&schemaId=15&projectId=17&schemaKey=B77&page=140
    re.compile(rf"[(（\[{{【](Note\s*)?[(（]?(?P<dst>{R_NUM})[）)]?[】}}\]）)]($|\t)", re.I),
    # http://************:55647/#/project/remark/237999?treeId=37691&fileId=67289&schemaId=15&projectId=17&schemaKey=B74
    re.compile(rf"(Note\s*)[(（]?(?P<dst>{R_NUM})[）)]?($|\t)", re.I),
    re.compile(rf"(^|\S+)[(（\[{{【](?P<dst>{R_NUM})[】}}\]）)]($|\s)", re.I),
    re.compile(r"(^|\S+)[(（\[{【](?P<dst>(i{1,3}|iv|vi{0,3}|ix|xi{0,3}))[】}\]）)]", re.I),
    re.compile(r"^[(（\[{【]?(?P<dst>[\^*#@]+)[】}\]）)]?", re.I),
    re.compile(r"[(（\[{【]?(?P<dst>[\^*#@]+)[】}\]）)]?$", re.I),
)
# http://************:55647/#/project/remark/419432?treeId=10898&fileId=114701&schemaId=18&projectId=17&schemaKey=C2.4&page=199 index=1856
P_NO_NUM_NOTES = re.compile(r"[(（\[{【]Notes?[】}\]）)]($|\t)", re.I)


def get_cell_note(cell: ParsedTableCell) -> set[str]:
    chars = cell.raw_cell.get("chars")
    note_chars = []
    # 尝试从chars中取到上标
    tag_index = None
    for char_index, char in enumerate(chars):
        if char.get("tag") == "superscript":
            if char["text"] in EXCLUDE_SUPERSCRIPT:
                tag_index = char_index if tag_index is None else tag_index
            else:
                note_chars.append(char["text"])
    if note_chars:
        return {"".join(note_chars)}

    notes = set()
    if P_ONLY_NOTES.search(cell.table.col_header_texts[cell.colidx]) and P_CELL_SUPERSCRIPT.search(cell.clean_text):
        # 脚注是一列，则直接用文本
        # http://************:55647/#/project/remark/416471?treeId=7054&fileId=114369&schemaId=18&projectId=17&schemaKey=C2.1.1&page=151 tbl_index=1678
        target_text = cell.clean_text
    else:
        # 脚注在最后，为*： http://************:55647/#/project/remark/234378?treeId=5980&fileId=66685&schemaId=15&projectId=17&schemaKey=B76
        # 脚注在前，为*@#: https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/4147#note_485228
        if not tag_index:
            for tag in EXCLUDE_SUPERSCRIPT:
                if (index := cell.text.find(tag)) > 0:
                    tag_index = index
                    break
        target_text = clean_txt(cell.text[tag_index:], remove_cn_text=True)
    for pattern in P_SUPERSCRIPT:
        notes.update(matched.group("dst") for matched in pattern.finditer(target_text))
    # http://************:55647/#/project/remark/419432?treeId=10898&fileId=114701&schemaId=18&projectId=17&schemaKey=C2.4&page=199 index=1856
    # 单元格中的脚注为(note) 则直接取所有footnotes
    if not notes and P_NO_NUM_NOTES.search(target_text):
        notes.add("note")
    return notes


# 包含脚注的单元格
def get_foot_note_by_cells(
    table: ParsedTable, cells: list[ParsedTableCell], footnote_pattern: PatternCollection | None = None
):
    foot_note_elements = []
    superscripts = set()
    for cell in cells:
        superscripts.update(get_cell_note(cell))

    # 通过拿到的上标从表格的foot_note中获取元素
    for superscript in superscripts:
        if "note" == superscript:
            prefix_pattern = None
        elif "*" in superscript or "^" in superscript:
            superscript = superscript.replace("*", r"\*").replace("^", r"\^")
            # * 开头的脚注后面没有空格或者括号
            # http://************:55647/#/project/remark/257968?treeId=8163&fileId=68808&schemaId=15&projectId=17&schemaKey=B63.1&page=124 元素块=1249
            prefix_pattern = re.compile(rf"^(?P<begin1>notes?\s*)?(?P<begin2>[(（\[])?{superscript}(?P<end>\s?)", re.I)
        else:
            prefix_pattern = re.compile(
                rf"^(?P<begin1>notes?\s*)?(?P<begin2>[(（\[]?){superscript}(?P<end>[:：.）)\s\]])", re.I
            )
        p_next_prefix = None
        for elem in table.footnotes:
            text = clean_txt(elem.get("text") or "")
            if not text or P_ONLY_NOTES.search(text):
                continue
            if not prefix_pattern:
                # 单元格中仅有(note)时，直接取所有notes
                foot_note_elements.append(elem)
                continue
            if matched := prefix_pattern.search(text):
                if not p_next_prefix:
                    group_dict = matched.groupdict()
                    begin1, begin2 = group_dict.get("begin1") or "", group_dict.get("begin2") or ""
                    end = (group_dict.get("end") or "").replace(" ", r"\s")
                    begin2, end = rf"[{begin2}]" if begin2 else "", rf"[{end}]" if end else ""
                    p_next_prefix = re.compile(rf"{begin1}{begin2}([iv]{{1,4}}|\d{{1,2}}|[a-z]|[\^*#@]+){end}", re.I)
            # 上一个元素块匹配上了，则上一个带标题的脚注到下一个带标题的脚注之间的内容都要
            # http://************:55647/#/project/remark/257968?treeId=8163&fileId=68808&schemaId=15&projectId=17&schemaKey=B63.1&page=124 元素块=1249
            elif (
                foot_note_elements
                and p_next_prefix
                and (p_next_prefix.search(text) or any(p.search(text) for p in P_SUPERSCRIPT))
            ):
                break
            elif not foot_note_elements or not p_next_prefix:
                continue
            if footnote_pattern and not footnote_pattern.nexts(text):
                continue
            foot_note_elements.append(elem)
    return foot_note_elements


def get_follow_table_text(element_, table_, follow_start_pattern, follow_element_text):
    if table_ and len(table_.rows) == 1:
        # 处理段落被误识别成一行N列的表格
        return clean_txt(" ".join(cell.text for cell in table_.rows[0]))
    if table_ and len(table_.cols) == 1 and all(follow_start_pattern.nexts(row[0].text.strip()) for row in table_.rows):
        # 处理段落被误识别成1列N行的表格
        # https://gitpd.paodingai.com/cheftin/docs_scriber/-/issues/3168#note_415113
        return clean_txt("\n".join(row[0].text for row in table_.rows))
    if not any(has_english_chars(cell) for cell in element_["cells"].values()):
        # 表格全部是中文，说明是另一半中文页面，跳过
        # http://************:55647/#/project/remark/245409?treeId=8904&fileId=66207&schemaId=28&projectId=17&schemaKey=E(d)(iv)-2&page=37
        return ""
    if P_NOT_SKIP_TABLE.nexts(follow_element_text):
        return ""
    return None


def find_as_follow_paras(
    pdfinsight: PdfinsightReader,
    start_element: dict,
    follow_start_pattern: PatternCollection,
    *,
    ignore_pattern: PatternCollection = PatternCollection([]),
    ignore_syllabus_pattern: PatternCollection = PatternCollection([]),
    below_pattern: PatternCollection = PatternCollection([]),
    neglect_below_pattern: PatternCollection = PatternCollection([]),
    max_paragraphs_for_every_follow: int = 1,
    include_start: bool = True,
    need_continuous: bool = False,
    skip_chinese: bool = True,
):
    elements = [start_element] if include_start else []
    match_as_follow = False
    count_matched_paragraphs = 0
    chapter_level = -1
    if follow_syll_idx := start_element.get("syllabus"):
        chapter_level = pdfinsight.syllabus_dict.get(follow_syll_idx, {}).get("level") or -1
    next_elem_index = start_element["index"]
    follow_element_text = clean_txt(start_element["text"])
    start_left = start_element["outline"][0]
    from remarkable.pdfinsight.parser import parse_table

    prev_cleaned_text = None
    while True:
        next_elem_index += 1
        try:
            ele_type, element = pdfinsight.find_element_by_index(next_elem_index)
        except IndexError:
            break
        if not element:
            continue
        # 有可能跨页
        if element["page"] > start_element["page"] + 2:
            break

        if pdfinsight.is_skip_element(element, outline_left=start_left, skip_chinese=skip_chinese):
            continue

        if ele_type == PDFInsightClassEnum.TABLE.value:
            table = parse_table(element, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
            # 段落可能被识别为表格，一般是一行N列或者一列N行
            cleaned_text = get_follow_table_text(element, table, follow_start_pattern, follow_element_text)
            if cleaned_text is None:
                break
            if not cleaned_text:
                continue
        elif ele_type in (PDFInsightClassEnum.PARAGRAPH.value, PDFInsightClassEnum.FOOTNOTE.value):
            cleaned_text = clean_txt(element["text"])
        else:
            break
        # 跳过页头的特殊元素块
        if element["page"] >= start_element["page"] + 1:
            # 下一页的最开始如果是多个章节标题，或者包含continued、Corporate Governance Report关键字，则跳过
            # http://************:55647/#/project/remark/?treeId=8265&fileId=67307&schemaId=28&projectId=43974&schemaKey=AC-E(a)-Role%20and%20function
            if P_CONTINUED.search(cleaned_text) or ignore_pattern.nexts(cleaned_text):
                continue
            if pdfinsight.is_syllabus_title(element):
                # following是一个章节，但能匹配上below_pattern，则可以取
                # http://************:55647/#/project/remark/245409?treeId=8904&fileId=66207&schemaId=28&projectId=17&schemaKey=E(d)(iv)-2&page=37
                if not below_pattern.nexts(cleaned_text):
                    continue
        # 存在following为小章节或误识别为章节的情况，所以遇到当前内容所属章节同级或上级的章节才终止
        if pdfinsight.syllabus_reader.is_syllabus_elt(element) and (elem_syll_idx := element.get("syllabus")):
            level = (pdfinsight.syllabus_dict.get(elem_syll_idx) or {}).get("level") or 10
            if level <= chapter_level and not ignore_syllabus_pattern.nexts(cleaned_text):
                break
        if follow_start_pattern.nexts(cleaned_text):
            match_as_follow = True
            count_matched_paragraphs = 1
        elif match_as_follow:
            # 1.跨页或者跨栏连续段落的第二段不做pattern匹配
            if (page_merged := element.get("page_merged_paragraph")) and next_elem_index != page_merged[
                "paragraph_indices"
            ][0]:
                continue
            count_matched_paragraphs += 1
            # 2.max_paragraphs_for_every_follow>1时，仅匹配每组第一个段落，接下来的段落直接添加
            if count_matched_paragraphs > max_paragraphs_for_every_follow:
                break
        else:
            break
        # Note: 这里排除匹配neglect_below_pattern或不匹配below_pattern的段落
        if neglect_below_pattern.nexts(cleaned_text) or (below_pattern and not below_pattern.nexts(cleaned_text)):
            continue
        # 注意：如果没配置反向正则，这里可能会有表格
        # 提取到的序号是否需要连续
        if need_continuous and prev_cleaned_text:
            if not is_continus(prev_cleaned_text, cleaned_text):
                break
            prev_cleaned_text = cleaned_text
        else:
            prev_cleaned_text = cleaned_text
        elements.append(element)
    return elements


FOLLOW_NUM_FUNC_MAP = {
    re.compile(r"^[(（](?P<dst>[ivx]{1,4})[）)]", re.I): roman_to_int,
    re.compile(r"^[(（]?(?P<dst>[ivx]{1,4})[）)]?(\s|\.)", re.I): roman_to_int,
    re.compile(r"^[(（](?P<dst>[a-z①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳])[）)]", re.I): ord,
    re.compile(r"^[(（](?P<dst>[a-z])\1[）)]"): ord,
    re.compile(r"^[(（](?P<dst>\d+)[）)]"): int,
    re.compile(r"^[(（]?(?P<dst>[a-z①②③④⑤⑥⑦⑧⑨⑩⑪⑫⑬⑭⑮⑯⑰⑱⑲⑳])[）)]?(\s|\.)", re.I): ord,
    re.compile(r"^[(（]?(?P<dst>\d+)[）)]?(\s|\.)"): int,
}


def is_continus(prev_text, cur_text):
    prev_matches = []
    cur_matches = []
    for pattern, func in FOLLOW_NUM_FUNC_MAP.items():
        prev_match = pattern.match(prev_text)
        cur_match = pattern.match(cur_text)
        if prev_match and cur_match:
            return func(cur_match.group("dst")) - func(prev_match.group("dst")) == 1
        prev_matches.append(prev_match)
        cur_matches.append(cur_match)
    if any(prev_matches) or any(cur_matches):
        return False
    return True


def find_as_follow_tables(
    pdfinsight: PdfinsightReader,
    start_element: dict,
    *,
    ignore_pattern: SearchPatternLike = MATCH_NEVER,
):
    table_element = None
    start_left = start_element["outline"][0]
    next_elem_index = start_element["index"]
    while True:
        next_elem_index += 1
        try:
            ele_type, element = pdfinsight.find_element_by_index(next_elem_index)
        except IndexError:
            break
        if not element:
            continue
        # 考虑表格跨3页
        if element["page"] > start_element["page"] + 3:
            break
        if is_table_elt(element):
            table_element = element
            break
        if pdfinsight.is_skip_element(element, outline_left=start_left, skip_tbl_unit=True):
            continue
        if is_paragraph_elt(element) and ignore_pattern.search(clean_txt(element["text"])):
            continue
        if not is_table_elt(element):
            break
    if not table_element:
        return []
    return pdfinsight.continuous_tables(table_element, only_after=True)


def is_note_element(pdfinsight: PdfinsightReader, element: dict):
    if not is_paragraph_elt(element, strict=True):
        return False
    if P_NOTES.search(clean_txt(element["text"], remove_cn_text=True)):
        return True
    start_page, prev_index = element["page"], element["index"]
    while True:
        prev_index -= 1
        try:
            ele_type, prev_element = pdfinsight.find_element_by_index(prev_index)
        except IndexError:
            break
        # 页码超过3页则终止
        if prev_element["page"] < start_page - 3:
            return False
        if prev_element["index"] in pdfinsight.syllabus_reader.elt_syllabus_dict:
            return False
        if is_table_elt(prev_element):
            table = parse_table(prev_element, tabletype=TableType.TUPLE.value, pdfinsight_reader=pdfinsight)
            return element["index"] in table.note_indices
    return False


def get_style_of_cell(cell: ParsedTableCell):
    """
    结合styles_diff和styles中的样式，提取单元格中的英文样式
    styles和styles_diff含义： https://mm.paodingai.com/cheftin/pl/k9qkxd3xbins8mf5rw6qizazwh
    """
    # 找到第一个英文位置，遇到中文则终止
    en_char_index = None
    for i, text in enumerate(cell.text):
        if P_CHNS.search(text):
            continue
        en_char_index = str(i)
        break
    if not en_char_index:
        return None, None, None
    return get_style_of_raw_cell(cell.raw_cell, en_char_index)


def find_all_section_rows(rows) -> set[int]:
    """
    根据行标题的样式，找出分段标题行
    http://************:55647/#/project/remark/294229?treeId=13570&fileId=70745&schemaId=18&projectId=13570&schemaKey=C1.3
    http://************:55647/#/project/remark/200124?treeId=9450&fileId=65231&schemaId=15&projectId=9450&schemaKey=B9

    TODO: 下方文件的表格782提取行号时，第18/19/20/24行因为整行每个单元格都有值，所以过滤，不作为分段行，后续按需处理
    http://************:55647/#/project/remark/294205?treeId=22663&fileId=70741&schemaId=18&projectId=22663&schemaKey=C1.3&page=52
    """
    if len(rows[0]) < 3:
        # 超过两列的表格才判断分段
        return set()
    font_map, valid_row_indices = {}, set()
    font_weights, font_italics, font_names = defaultdict(list), defaultdict(list), defaultdict(list)
    for cell in (c for row in rows for c in row):
        # 排除列标题、最后一行
        if not cell.is_row_header or cell.is_col_header:
            continue
        font_name, weight, italic = get_style_of_cell(cell)
        if not font_name:
            continue
        font_map[cell.rowidx] = (font_name, weight, italic)
        valid_row_indices.add(cell.rowidx)
        font_weights[weight].append(cell.rowidx)
        font_italics[italic].append(cell.rowidx)
        font_names[font_name].append(cell.rowidx)
    section_rows = set()
    title_weight = "bold" if "bold" in font_weights else ("normal" if "normal" in font_weights else None)
    title_name = None
    if names := [name for name in font_names if name.lower().endswith(("-bold", "-italic"))]:
        title_name = names[0]
    for value_dict, title_value in ((font_weights, title_weight), (font_italics, True), (font_names, title_name)):
        # 每种字体样式的个数都相同，无法区分，则跳过
        if len(value_dict) != 2 or (not title_value and len({len(indices) for indices in value_dict.items()})) == 1:
            continue
        # 出现次数最少的作为分段标题
        title_value = title_value or min(value_dict.items(), key=lambda x: len(x[1]))[0]
        section_rows = value_dict[title_value]
        break
    check_rows = [row for row in rows if row[0].rowidx in valid_row_indices]
    if len(section_rows) <= 1:
        # 只有一个分段行，说明是total行，不是分段
        check_rows = [row for row in check_rows if row[0].rowidx not in section_rows]
    else:
        check_rows = [row for row in check_rows if row[0].rowidx in section_rows]
    result = []
    last_row_idx = rows[-1][0].rowidx
    for row in check_rows:
        row_texts = [clean_txt(cell.text, remove_cn_text=True) for cell in row]
        row_index = row[0].rowidx
        # 跳过全空的行
        if not row_texts[0] or not any(t for t in row_texts):
            continue
        # 最后一行如果样式符合，直接取
        if row_index == last_row_idx or len(set(row_texts)) == 1 or all(not cell.text for cell in row[1:-1]):
            result.append(row[0].rowidx)
    # 连续两个相邻的标题行，应该以第二个为准（可能是total或者小计）
    invalid_indices = set()
    for idx, next_idx in zip(result, result[1:]):
        if idx + 1 == next_idx != last_row_idx:
            invalid_indices.add(idx)
    result = set(result) - invalid_indices
    # 只有一个分段+一个total，则认为不分段
    return result if len(result - {last_row_idx}) > 1 else set()


def find_children_of_notes_chapter(pdfinsight: PdfinsightReader) -> list[dict]:
    """
    根据给定的root章节正则，提取root章节下的所有一级目录
    1. 先找第一个章节
    2. 提取第一个章节的样式
    3. 根据样式提取其他子章节
    """
    notes_start, notes_end = pdfinsight.get_root_syllabus_range(P_NOTE_CHAPTER)
    notes_children = []
    base_font_style = None
    last_number, is_roman = 0, False
    child_elements = {}
    for syllabus in pdfinsight.syllabus_reader.syllabuses:
        element_index = syllabus.get("element") or syllabus["range"][0]
        if not notes_start <= element_index < notes_end:
            continue
        _, element = pdfinsight.find_element_by_index(element_index)
        unified_element = UnifiedParaElement(element, pdfinsight)
        if syllabus["parent"] == -1 and P_NOTE_CHAPTER.search(unified_element.cleaned_title) and syllabus["children"]:
            child_elements[syllabus["index"]] = unified_element.font_style.size_and_color
        if element_index == notes_start or pdfinsight.is_skip_element(
            element, aim_types={PDFInsightClassEnum.PARAGRAPH.value}, skip_chinese=True, skip_merged=True
        ):
            continue
        prefix_fmt = unified_element.prefix_format
        if not prefix_fmt or prefix_fmt not in ("A", "A.", "1", "1.", "I", "I."):
            continue
        font_style = unified_element.font_style.size_and_color
        if not font_style:
            continue
        if base_font_style:
            if base_font_style != font_style:
                continue
        else:
            is_roman = prefix_fmt.startswith("I")
            base_font_style = font_style
        prefix_word = unified_element.prefix_words.replace(".", "")
        # 针对17A这种特例
        if prefix_word.startswith(f"{last_number + 1}"):
            last_number = last_number + 1
        elif prefix_word.startswith(str(last_number)):  # 这里可能是17B
            pass
        else:
            if prefix_word.isdigit():
                number = int(prefix_word)
            elif is_roman and prefix_word.isalpha():
                number = roman_to_int(prefix_word)
            elif len(prefix_word) == 1 and prefix_word.isalpha():
                number = ord(prefix_word) - 64
            else:
                continue
            # 序号不连续
            if number <= last_number or number != last_number + 1:
                continue
            last_number = number
        if notes_children and notes_children[-1]["range"][1] != element_index:
            # 修正上一个章节的结束位置
            last_syllabus = copy(notes_children[-1])
            last_syllabus["range"][1] = element_index
            notes_children[-1] = last_syllabus
        if syllabus["title"] == unified_element.added_space_text:
            notes_children.append(syllabus)
        else:
            # 修正序号后空格缺失问题
            new_syllabus = copy(syllabus)
            new_syllabus["title"] = unified_element.added_space_text
            notes_children.append(new_syllabus)
    if notes_children and notes_children[-1]["range"][-1] != notes_end:
        # 修正最后一个子章节的结束位置
        last_syllabus = copy(notes_children[-1])
        last_syllabus["range"][1] = notes_end
        notes_children[-1] = last_syllabus
    # 追加识别到的子章节
    if base_font_style:
        indices = {n["index"] for n in notes_children}
        notes_children.extend(
            pdfinsight.syllabus_reader.syllabus_dict[idx]
            for idx, font_style in child_elements.items()
            if font_style == base_font_style and idx not in indices
        )
    return notes_children


def split_note_num(note_title):
    sub_header = None
    has_bracket = False
    num = note_title.split("note ", maxsplit=1)[1]
    if "-" in num:
        num, sub_header = [n.strip() for n in num.split("-", maxsplit=1)]
    elif "•" in num:
        # http://************:55647/#/project/remark/290373?fileid=88751&projectId=17&treeId=17103&fileId=88751&schemaId=29
        num, sub_header = [n.strip() for n in num.split("•", maxsplit=1)]
    else:
        if "@" in num:
            num, sub_header = num.split("@", maxsplit=1)
        elif matched := P_BRACKET.search(num):
            start = matched.start()
            has_bracket = True
            # 如果是note 33(b) 这种形式，则需要先找33，再找(b)
            num, sub_header = num[:start], num[start:]
        elif len(num.split()) == 2:
            num, sub_header = num.split()
    return has_bracket, num, sub_header


def find_syllabuses_by_note_nums(
    pdfinsight: PdfinsightReader,
    note_chapter_children: list[dict],
    note_title_nums: set[str],
    aim_types: set[str] = None,
) -> list[dict]:
    """
    找notes章节下的note {N}，根据N匹配所属章节
    note_chapter_children: 提前算好的note章节下的所有子章节
    注意： 返回的list中，chapter可能没有index
    """
    if not note_title_nums or not note_chapter_children:
        return []
    syllabuses = []
    for note_title in note_title_nums:
        has_bracket, num, sub_header = split_note_num(note_title)
        note_syllabuses = [n for n in note_chapter_children if n["title"].startswith((f"{num} ", f"{num}. "))]
        if sub_header:
            # 指定了note子章节的子章节名称，则必须用名称过滤
            for note_syllabus in note_syllabuses:
                for sub_idx in note_syllabus["children"]:
                    if not (sub_syll := pdfinsight.syllabus_reader.syllabus_dict.get(sub_idx)):
                        continue
                    sub_title = sub_syll["title"]
                    if sub_title.startswith(sub_header) or sub_title.lower().endswith(sub_header.lower()):
                        syllabuses.append(sub_syll)
                        break
                else:
                    if has_bracket:
                        # 序号可能是Notes的序号，或者是未识别的章节序号
                        # http://************:55647/#/project/remark/293896?treeId=3842&fileId=70696&schemaId=18&projectId=17&schemaKey=C7.1 index=2968
                        start_element, end = None, 0
                        for index in range(*note_syllabus["range"]):
                            elem_type, element = pdfinsight.find_element_by_index(index)
                            if pdfinsight.is_skip_element(
                                element, aim_types=aim_types, skip_tbl_unit=True, skip_chinese=True, skip_merged=True
                            ):
                                continue
                            if not is_para_elt(element) or not element.get("text"):
                                continue
                            if element["text"].startswith(sub_header):
                                start_element = element
                                continue
                            if start_element and (
                                pdfinsight.syllabus_reader.is_syllabus_elt(element)
                                or P_CHAPTER_PREFIX.search(element["text"])
                            ):
                                end = element["index"]
                                break
                        if start_element:
                            start = start_element["index"]
                            final_syllabus = pdfinsight.syllabus_reader.elt_syllabus_dict.get(start)
                            if not final_syllabus:
                                final_syllabus = {
                                    # 注意这里没有index
                                    "title": clean_txt(start_element["text"], remove_cn_text=True),
                                    "children": [],
                                    "range": [start, end or note_syllabus["range"][-1]],
                                    "parent": note_syllabus["index"],
                                    "dest": {"box": start_element["outline"], "page_index": start_element["page"]},
                                }
                            syllabuses.append(final_syllabus)
        else:
            syllabuses.extend(note_syllabuses)
    return syllabuses


def get_nearst_table_title(pdfinsight, table, date_patterns: list[PatternCollection]):
    prev_element = None
    # 表格上方可能是分栏右侧的中文，跳过中文行和被合并行来找相邻标题
    for offset in range(1, 10):
        prev_index = table.index - offset
        if prev_index <= 0:
            break
        _, prev_element = pdfinsight.find_element_by_index(prev_index)
        if pdfinsight.is_chinese_elt(prev_element) or is_merged_elt(prev_element):
            continue
        break
    if is_para_elt(prev_element) and any(pattern.nexts(prev_element["text"]) for pattern in date_patterns):
        # 这里要取表格上方最近的日期，例如： for the year ended ...
        # http://************:55647/#/project/remark/379680?treeId=4473&fileId=113004&schemaId=18&projectId=17&schemaKey=C2.4&page=141 index=1494
        return prev_element["text"]
    return table.elements_above[0]["text"] if table.elements_above else table.possible_titles[-1]

import logging
import re

from remarkable.common.common_pattern import P_SEN_SEPARATOR
from remarkable.common.constants import AnswerV<PERSON>ueEnum
from remarkable.common.pattern import <PERSON>Multi, NeglectPattern, PatternCollection
from remarkable.common.util import clean_txt, mm_notify
from remarkable.predictor.common_pattern import R_EN_MONTH, R_PERCENT_STRICT, R_WEEKDAY
from remarkable.predictor.hkex_predictor.pattern import R_BE
from remarkable.predictor.hkex_predictor.schemas.c_rule_schema.c_rule_c7 import R_ANY_DATE
from remarkable.predictor.hkex_predictor.schemas.pattern import R_NOT, R_SHARE_CLASS, reg_words
from remarkable.predictor.models.base_model import BaseModel
from remarkable.predictor.utils import extract_date
from remarkable.services.chatgpt import OpenAIClient

logger = logging.getLogger(__name__)

R_ISSUED_SHARE = rf"(issued\s*{reg_words(0, 5)}Shares?|shares?\s*{reg_words(0, 5)}issue)"

R_TOTAL_VALUE = [
    # vote 数量等于 issued 数量
    rf"(total|the|aggregate)\s*number[^\d.,]+vote.*?({R_BE}|[:：])\s(?P<content>[\d,，]+).*(representing|equal).*issued\s*share",
    # total
    r"issued\s*shares?.*?(?P<content>[\d,，]+)\s*shares(?!\svoted?)",
    r"(?P<content>[\d,，]+)\s*Share[^\d.,]+in\s*issue",
    rf"(total|the|aggregate)\s*number[^\d.,]+{R_ISSUED_SHARE}.*?({R_BE}|[:：])\s(?P<content>[\d,，]+)",
    rf"total\s*of\s*(?P<content>[\d,，]+)\s*{R_ISSUED_SHARE}",
    rf"as\s*at\s*the\s*date.*?\sAGM[,，]?[^\d.,]*(?P<content>[\d,，]+)\s*({R_ISSUED_SHARE}|shares?)",
    r"as\s*at\s*the\s*date.*?\sAGM[,，]?\s*(the\s)?issued\s*shares[^\d.,]*(?P<content>[\d,，]+)",
    # subclass
    rf"including\s*(?P<content>[\d,，]+)\s*{R_SHARE_CLASS}\s*Shares",
    rf"including.*(and|&)\s*(?P<content>[\d,，]+)\s*{R_SHARE_CLASS}\s*Shares",
]
P_PASSED_DATE = PatternCollection(
    [
        rf"(?P<day>\d{{1,2}})(st|nd|rd|th)?[,，]?\s(?P<mon>{R_EN_MONTH})[,，]?\s(?P<year>\d{{4}})",
        rf"(?P<mon>{R_EN_MONTH})[,，]?\s(?P<day>\d{{1,2}})(st|nd|rd|th)?[,，]?\s(?P<year>\d{{4}})",
    ],
    re.I,
)


def convert_date_by_llm(text):
    format_date = OpenAIClient().send_message(
        [
            {
                "role": "system",
                "content": f"""。
请将以下时间文本转换为"DD/MM/YYYY"格式， 只返回转换结果，不要任何解释:

输入示例：28 MAY 2025
输出示例：28/05/2025

转换规则：
- 保持日期数字不变
- 将英文月份缩写转换为对应的数字（01-12）
- 保持年份不变
- 使用"/"作为分隔符
- 如果无法转换，请返回空字符串

请转换：{text}""",
            },
        ],
        options={"temperature": 0.0, "model": "gpt-4o-mini"},
    )
    return format_date


def format_poll_passed_date(answers, **kwargs):
    if not answers:
        return answers
    for predictor_ans in BaseModel.get_common_predictor_results(answers):
        cleand_date = clean_txt(predictor_ans.text)
        if format_date := extract_date(P_PASSED_DATE, cleand_date, str_format="%d/%m/%Y"):
            logger.debug(f"origin date {cleand_date}, format_poll_passed_date: {format_date}")
        elif format_date := convert_date_by_llm(cleand_date):
            logger.debug(f"origin date {cleand_date}, convert_date_by_llm: {format_date}")
        else:
            format_date = cleand_date
            mm_notify(f"{cleand_date} is not a valid date, please check it manually", error=True)
        predictor_ans.meta["passed_date"] = format_date
    return answers


def choose_nearst_value(answers, **kwargs):
    """
    当一个share class存在多个value时，取前面最近的一个
    """
    share_classes = [answer["Relevant share class"][0].text for answer in answers]
    cls_count = {}
    for cls in set(share_classes):
        cls_count[cls] = share_classes.count(cls)
    final_answers = []
    for cls, count in cls_count.items():
        if count > 1:
            cls_answers = [answer for answer in answers if answer["Relevant share class"][0].text == cls]
            distincts = []
            for answer in cls_answers:
                # 计算class和value之间的距离
                value_end = answer["Value"][0].element_results[0].end
                class_start = answer["Relevant share class"][0].element_results[0].start
                if class_start - value_end > 0:
                    distincts.append(class_start - value_end)
            if distincts:
                min_index = distincts.index(min(distincts))
                final_answers.extend([answer for idx, answer in enumerate(cls_answers) if idx == min_index])
        else:
            final_answers.extend([answer for answer in answers if answer["Relevant share class"][0].text == cls])
    return final_answers


predictor_options = [
    {
        "path": ["Total number of issued shares"],
        "sub_primary_key": ["Relevant share class"],
        "strict_group": True,
        "models": [
            {
                "name": "poll_shares_issued",
                "models": [
                    {
                        "name": "para_match",
                        "force_use_all_elements": True,
                        "para_separator": P_SEN_SEPARATOR,
                        "paragraph_pattern": R_TOTAL_VALUE,
                    }
                ],
            },
        ],
    },
    {
        "path": ["Number of treasury shares"],
        "sub_primary_key": ["Relevant share class"],
        "strict_group": True,
        "models": [
            {
                "name": "poll_shares_issued",
                "models": [
                    {
                        "name": "para_match",
                        "force_use_all_elements": True,
                        "para_separator": P_SEN_SEPARATOR,
                        "paragraph_pattern": [
                            r"(?P<content>[\d,，]+)\s*treasury\s*share",
                            rf"(?<!excluding )(?<![\d,])(?P<content>[\d,，]+)\s*{R_SHARE_CLASS}?\s*Shares\s*{R_BE}\s*treasury\s*share",
                            rf"(?<!excluding )(?<![\d,])(?P<content>[\d,，]+)\s*{R_SHARE_CLASS}?\s*Shares\s*[^\d.,]+\bas\s*treasury\s*share",
                        ],
                    },
                    {
                        "name": "para_match",
                        "force_use_all_elements": True,
                        "para_separator": P_SEN_SEPARATOR,
                        "paragraph_pattern": [
                            rf"(?P<content>{R_NOT}\s*([(（][\div]+[)）]\s)?((hold|have|had|held)\s)?(any\s)?\s*treasury\s*share)",
                            rf"(?P<content>(none|not)\s*of\s*which\s*{R_BE}\s*in\s*treasury)",
                            rf"(?P<content>(hold|have|had|held){R_NOT}\s*(ordinary )?shares\s*in\s*treasury)",
                            rf"{R_BE}\s*(?P<content>{R_NOT}.*treasury\s*shares?)\s*held",
                        ],
                    },
                ],
            }
        ],
    },
    {
        "path": ["Percentage"],
        "post_process": choose_nearst_value,
        "sub_primary_key": ["Relevant share class"],
        "strict_group": True,
        "models": [
            {
                "name": "poll_shares_issued",
                "models": [
                    {
                        "name": "special_cells",
                        "force_use_all_elements": True,
                        "multi_content": True,
                        "cell_pattern": [
                            NeglectPattern.compile(
                                match=MatchMulti.compile(
                                    r"mandate",
                                    "issue|allot",
                                    r"exceed|\bup\s*to|more than",
                                    rf"\s(\d{{1,2}}|ten|twenty){R_PERCENT_STRICT}",
                                    operator=all,
                                ),
                                unmatch=r"((re)?purchas(e|ing|ed)|(buy|bought)(-|\s)?backs?)",
                            ),
                            NeglectPattern.compile(
                                match=MatchMulti.compile(
                                    "issue of Shares",
                                    r"exceed|\bup\s*to|more than",
                                    rf"\s(\d{{1,2}}|ten|twenty){R_PERCENT_STRICT}",
                                    operator=all,
                                ),
                                unmatch=r"((re)?purchas(e|ing|ed)|(buy|bought)(-|\s)?backs?)",
                            ),
                        ],
                        "enum_pattern": [
                            rf"(^|\s)(?P<content>(\d{{1,2}}|ten|twenty){R_PERCENT_STRICT})",
                        ],
                    },
                    {
                        "name": "para_match",
                        "para_separator": P_SEN_SEPARATOR,
                        "sentence_pattern": [
                            rf"(exceed|\bup\s*to|more than)\s*(?P<content>(\d{{1,2}}|ten|twenty){R_PERCENT_STRICT})\s*of\s(?P<class>the\s(total|aggregate)\snumber\sof\s(the\s*)?shares?.*?in\sissue)",
                        ],
                    },
                ],
            }
        ],
    },
    {
        "path": ["Excluding treasury shares"],
        "models": [
            {
                "name": "para_match",
                "para_separator": P_SEN_SEPARATOR,
                "paragraph_pattern": [
                    r"(?P<content>excluding.*?treasury shares)",
                ],
                "neglect_pattern": [
                    MatchMulti.compile(r"\badditional shares", r"\bvoted in\b", operator=all),
                    MatchMulti.compile(r"\bshareholder", r"\bauthorised proxies\b", r"\bholding\b", operator=all),
                ],
            },
        ],
    },
    {
        "path": ["Whether the resolution has been passed"],
        "models": [
            {
                "name": "poll_resolution_pass",
                "force_use_all_elements": True,
                "skip_continued_table": False,  # 有些跨页表格合并 有问题先不跳过
            }
        ],
    },
    {
        "path": ["Passed date of the resolution"],
        "post_process": format_poll_passed_date,
        "models": [
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "force_use_all_elements": True,
                "para_separator": P_SEN_SEPARATOR,
                "paragraph_pattern": [
                    rf"HELD\s*ON\s*({R_WEEKDAY}\s*[,，]?\s*)?(?P<content>{R_ANY_DATE})",
                    rf"\bAGM\b\s*{R_BE}\s*HELD\s*ON\s*{R_WEEKDAY}??(?P<content>{R_ANY_DATE})",
                    rf"held.*\bAGM\b.*ON\s*{R_WEEKDAY}??(?P<content>{R_ANY_DATE})",
                    rf"\bAGM\b.*held.*on\s*(?P<content>{R_ANY_DATE})",
                    rf"\bAGM\b.*held.*(in order on|at).*?({R_WEEKDAY}\s*[,，]?\s*)?(?P<content>{R_ANY_DATE})",
                ],
            },
            # 标题和文档前面都没有披露时，取结尾处时间
            {
                "name": "para_match",
                "enum": AnswerValueEnum.PS.value,
                "force_use_all_elements": True,
                "paragraph_pattern": [
                    rf"^Hong\s*Kong[,，]\s*(?P<content>{R_ANY_DATE})\s*$",
                ],
            },
            # 取最后一页的倒数5个元素块中的时间
            {
                "name": "fixed_position",
                "positions": [-1, -2, -3, -4, -5],
                "pages": [-1],
                "regs": [rf"^(?P<dst>{R_ANY_DATE})$"],
                "enum": AnswerValueEnum.PS.value,
            },
        ],
    },
]


prophet_config = {"depends": {}, "predictor_options": predictor_options}

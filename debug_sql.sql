select *
from embedding
where index = 0;


WITH "temp_q" AS (SELECT "t1"."id", "t1"."fid", "t1"."mold", "t1"."deleted_utc", "t1"."updated_utc"
                  FROM "question" AS "t1"
                  WHERE (("t1"."mold" IN (24, 26, 5, 15)) AND ("t1"."deleted_utc" = 0)))
SELECT "t2"."fid",
       "temp_q"."id"       AS "qid",
       "temp_q"."updated_utc",
       "temp_q"."mold"     AS "mold_id",
       "t2"."stock_code",
       "t2"."stat_res",
       "t3"."headline",
       "t3"."url"          AS "hkexurl",
       "t3"."release_time" AS "release_date",
       "t2"."doc_type"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t3"
                    ON (("t2"."fid" = "t3"."fid") AND ("t3"."type" IN ('Q1', 'Interim', 'Q3', 'Final', 'ar')))
         INNER JOIN "file" AS "t4" ON ("t4"."id" = "t2"."fid")
         INNER JOIN "temp_q" ON ("t2"."fid" = "temp_q"."fid")
         INNER JOIN "hkex_companies_info" AS "t5" ON ("t2"."stock_code" = "t5"."stock_code")
WHERE ((((((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND
          ("t4"."deleted_utc" = 0)) AND ("t2"."doc_type" IN (1, 11, 12, 13, 14))) AND
        ("temp_q"."updated_utc" >= 1725333812.0)) AND ("temp_q"."updated_utc" <= 1725506612.0))
ORDER BY "temp_q"."updated_utc" DESC
\COPY (
select rule_group_rule_reference_through.id,
   rule_group_rule_reference_through.rulegroup_id,
   rule_group_rule_reference_through.rulereference_id,
   rule_reference.rule
from rule_group_rule_reference_through
join rule_reference on rule_reference.id = rule_group_rule_reference_through.rulereference_id
where rulegroup_id in
  (212, 213, 214, 215, 220, 221, 222, 223, 224, 225, 226, 227, 228, 229, 230, 231, 232, 234, 235, 236, 237, 238,
   239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 250, 251, 252, 253, 254, 255, 256, 257, 258, 259, 261,
   262, 263, 264, 265, 266, 267, 268, 269, 271, 272, 273, 274, 276, 277, 278, 279, 280, 281, 282, 283, 287, 288,
   289, 290, 294, 295, 296, 297, 301, 302, 303, 304, 305, 308, 309, 310, 311, 312, 315, 316, 317, 318, 319, 322,
   323, 324, 325, 326, 327, 329, 330, 331, 332, 333, 334, 335, 336, 337, 338, 339, 340, 341, 342)) TO '~/output.csv'
WITH (FORMAT CSV, HEADER);


SELECT *
FROM embedding
WHERE vector @@ plainto_tsquery('english', 'new A shares of the Company');

SELECT "t1"."id", "t1"."fid", "t1"."mold", "t1"."deleted_utc", "t1"."updated_utc"
FROM "question" AS "t1"
WHERE ("t1"."mold" IN (26, 15, 24))
  AND ("t1"."deleted_utc" = 0)
  AND ("t1"."fid" = 89370);


select distinct id
from file;
select distinct file_id
from embedding;

select stock_code
from addition_data
where stock_code LIKE '(D)%';

SELECT REGEXP_REPLACE(stock_code, '^\(D\)', '', 'g') AS new_value
FROM addition_data
where stock_code LIKE '(D)%';

UPDATE addition_data
SET stock_code = REGEXP_REPLACE(stock_code, '^\(D\)', '', 'g')
where stock_code LIKE '(D)%';

explain
select *
from question
where deleted_utc != 0;
explain
select *
from question
where mold = 5
  and fid = 89370;



select concat('https://jura.paodingai.com/#/hkex/result-announcement/report-review/', id, '?fileId=', fid,
              '&schemaId=24&rule=&ratio=ratio1') url
from question
where id in
      (298202, 298199, 298208, 298198, 298210, 298212, 298219, 298204, 298228, 298216, 298232, 298230, 298236, 298234,
       298224, 298226);

SELECT file_id, index, position, text, metadata, ts_rank_cd(vector, query, 32 /* rank/(rank+1) */ ) AS score
FROM embedding,
     to_tsquery('english',
                'threadbare | sofa_bed | independ | capital | allocation | fund_raise | transformable | measure | food_market | swap | third | bonus | gillyflower | readjustment | incentive | respect | parcelling | transmutable | cosmopolitan | cyberspace | warrant | appendage | Malcolm_stock | equiti | order | prise | inventory | general | Price | General | fund-raise | stock_up | share | portion_out | yield | stock_warrant | tired | Warrants | clear | refund | Incentive | sack_up | draw_together | carry | plowshare | fencesitter | enrollment | pose | apportioning | capital_letter | adjustment | gunstock | repurchas | fundrais | fellow_member | tie | grocery | direct | redemption | range | performance | apportion | Repurchase | allotment | warrantee | place | lay | Trading | third_gear | mugwump | Mandate | appraise | cap | time_value | cash_register | surgery | adhere | ploughshare | parentage | ancestry | quittance | fairness | rank | return | registration | proceed | internet | self-employed_person | sovereign | stemma | takings | Parties | repayment | bond | Shares | line | position | marketplace | cost | last | lucre | market | Bond | stock | stockpile | net_profit | autonomous | percentage | appreciate | trading | cognitive_process | Das_Kapital | commit | Share | banal | part | price | register | cognitive_operation | bloodline | store | capit | aim | apportionment | web | come_in | self-governing | member | alliance | chemical_bond | parcel | partake | James_Bond | Registration | swop | inducement | tierce | buy_back | divvy_up | come_out | station | value | livestock | Operation | put | send | registr | neckcloth | merchandise | authorization | hamper | shopworn | oper | damage | sanction | uppercase | convertible | go_forward | assess | toll | functioning | Washington | carry_on | lineage | bail_bond | adherence | portion | mandat | old-hat | third_base | Bonds | Mary_Leontyne_Price | indorsement | buy_in | stick | esteem | line_of_descent | profit | deal | company | phallus | guarantee | securities_industry | grocery_store | mandatory | mandate | show | ecumenical | sharehold | rate | stick_to | enrolment | authorisation | trade | Independent | commonplace | party | network | descent | adhesiveness | Equity | go_on | Repayment | market_place | warranty | subscription | convert | assignation | thirdly | terms | move | Members | blood | political_party | free-lance | extremity | issuance | valu | endorsement | one-third | economic_value | imprimatur | convertible_security | attach | partake_in | take | Julian_Bond | great | note_value | universal | translatable | Debt | issuing | prize | site | debt | buyback | trade_in | stock_certificate | sprout | parceling | justify | Fundraising | Leontyne_Price | bond_certificate | fund | Net | of | countenance | grade | target | surgical_operation | working_capital | invest | sell | Value | Proceeds | monetary_value | process | localise | bond_paper | storage_allocation | main | Issuance | Third | incent | repay | issuanc | motivator | net_income | surgical_procedure | earnings | freelancer | majuscule | tertiary | set | upper-case_letter | well-worn | point | farm_animal | file | Capital | broth | treasure | Allocation | bail | origin | oecumenical | subscript | full_general | shackle | issue | independent | attachment | continue | stock-purchase_warrant | hackneyed | post | Shareholders | exchangeable | adhesion | superior_general | mart | hold_fast | meshing | breed | blood_line | read | proceeds | Subscription | locate | parti | alloc | net | mesh | equity | identify | bind | registry | localize | evaluate | repurchase | record | final | commercialize | military_operation | sack | contribution | payoff | cross-file | bring_together | nett | standard | keep | worldwide | world-wide | Market | freelance | regist | switch | meshwork | commercialise | surgical_process | go_along | strain | mathematical_process | mental_process | 3rd | trammel | penis | mathematical_operation | valuate | Register | timeworn | operation | trite | caudex | free_lance | fundraise | chapiter | Placing | Convertible | profits | Stock | procedure | go | pedigree') query
WHERE query @@ vector
  AND file_id IN (66523)
  AND deleted_utc = 0
ORDER BY score DESC
LIMIT 20;



SELECT *
FROM embedding
WHERE (syllabus)[array_length(syllabus, 1)] = 'REMUNERATION COMMITTEE';

select file_id, count(*)
from embedding
group by file_id;
select count(distinct file_id)
from embedding;
select max(index)
from embedding
where file_id = 68519;


SELECT table_schema || '.' || table_name                                         AS full_table_name,
       pg_size_pretty(pg_total_relation_size(table_schema || '.' || table_name)) AS total_size,
       pg_size_pretty(pg_table_size(table_schema || '.' || table_name))          AS table_size,
       pg_size_pretty(pg_indexes_size(table_schema || '.' || table_name))        AS index_size
FROM information_schema.tables
WHERE table_schema = 'public'
ORDER BY pg_total_relation_size(table_schema || '.' || table_name) DESC;


SELECT pg_size_pretty(pg_total_relation_size('public.embedding')) AS total_size,
       pg_size_pretty(pg_table_size('public.embedding'))          AS table_size,
       pg_size_pretty(pg_indexes_size('public.embedding'))        AS index_size;

SELECT COUNT(*)                                           AS total_rows,
       pg_size_pretty(SUM(pg_column_size(preset_answer))) AS total_text_size,
       pg_size_pretty(SUM(pg_column_size(crude_answer)))  AS total_embedding_size,
       pg_size_pretty(SUM(pg_column_size(answer)))        AS total_syllabus_embedding_size
FROM question
where fid = 67845;

select count(*)
from embedding
where position = 0;

---  计算占用磁盘空间大小


SELECT COUNT(distinct file_id)                        as file_count,
       COUNT(*)                                       AS total_rows,
       pg_size_pretty(SUM(pg_column_size(embedding))) AS total_embedding_size,
       pg_size_pretty(SUM(pg_column_size(syllabus)))  AS total_syllabus_embedding_size
FROM syllabus_embedding;

SELECT COALESCE(file_id::text, 'TOTAL')                                                               AS file_id,
       COUNT(*)                                                                                       AS rows_count,
       pg_size_pretty(SUM(pg_column_size(text)))                                                      AS text_size,
       pg_size_pretty(SUM(pg_column_size(embedding)))                                                 AS embedding_size,
       pg_size_pretty(SUM(pg_column_size(vector)))                                                    AS vector_size,
       pg_size_pretty(SUM(pg_column_size(text) + pg_column_size(embedding) + pg_column_size(vector))) AS total_size
FROM embedding
WHERE position = 0
GROUP BY
    ROLLUP (file_id)
ORDER BY GROUPING(file_id) DESC,
         SUM(pg_column_size(text) + pg_column_size(embedding) + pg_column_size(vector)) DESC;
----
SELECT *
FROM pg_available_extensions;
-- misc/alembic/versions/20240923143107_create_table_embedding.py
-- 465页 2745KB

CREATE INDEX ON embedding USING hnsw (embedding vector_cosine_ops);

create index on embedding using gin (embedding);

select *
from syllabus_embedding
where 2300 = any (indexes);



-- \COPY ( select * from diagnosis where status_code=400)) TO '~/output.csv' WITH (FORMAT CSV, HEADER);
-- ./bin/db_util.sh sql "\COPY (SELECT * FROM diagnosis WHERE status_code=400) TO '/opt/scriber/output.csv' WITH (FORMAT CSV, HEADER);"


SELECT EXTRACT(EPOCH FROM TIMESTAMP '2024-01-14 05:08:26');

select distinct fid
from question
where updated_utc >= 1705176506
  and updated_utc <= 1705208906


SELECT mold, timezone('Asia/Shanghai', to_timestamp(updated_utc)) AS china_time
FROM question
WHERE fid = 96407
ORDER BY updated_utc DESC;



SELECT c.stock_code,
       CASE
           WHEN hm.name IS NOT NULL THEN hm.name
           WHEN c.company_name_en IS NOT NULL THEN c.company_name_en
           END          AS company_name,
       COUNT(1) OVER () AS full_count
FROM hkex_companies_info c
         LEFT JOIN
         (SELECT DISTINCT ON (stock_code) stock_code, name from hkex_file_meta) hm on c.stock_code = hm.stock_code

where true
  and c.stock_code like '00001'
order by c.stock_code
limit 10 offset 0;



select h.report_year::int as financial_year
from hkex_file_meta h
         inner join file f on f.id = h.fid
         inner join question q on q.fid = f.id
where h.report_year >= '2019' -- 只需查询指定年份以后的数据
  and q.mold in (5)
  and h.deleted_utc = 0
  and h.doc_type = 1
  and h.id in (select max(h.id)
               from hkex_file_meta h
                        inner join file f on f.id = h.fid
               where f.deleted_utc = 0
                 and h.doc_type = 1
                 and f.qid is not null
                 and h.qid is not null
                 and h.report_year >= '2019' -- 只需要近五年的年报记录
               group by h.stock_code, h.report_year)
  and h.stock_code = '00001'
order by h.published desc, h.doc_type;


select max(h.id)
from hkex_file_meta h
         inner join file f on f.id = h.fid
where f.deleted_utc = 0
  and h.doc_type = 1
  and f.qid is not null
  and h.qid is not null
  and h.report_year >= '2019' -- 只需要近五年的年报记录
  and h.stock_code = '00001'
group by h.stock_code,
         h.report_year. / bin / db_util.sh sql "\COPY (select * from history where meta::json ->>'adjustment' like  '%form <%') TO '/opt/scriber/output.csv' WITH (FORMAT CSV, HEADER);"

select *
from history
where meta::json ->> 'adjustment' like '%form <%';

UPDATE history
SET meta = jsonb_set(
        meta,
        '{adjustment}',
        to_jsonb(replace(meta ->> 'adjustment', 'form <', 'from <'))
           )
WHERE meta ->> 'adjustment' LIKE '%form <%';


UPDATE history
SET meta = jsonb_set(
        meta,
        '{adjustment}',
        to_jsonb(replace(meta ->> 'adjustment', 'form <', 'from <'))
           )
WHERE meta ->> 'adjustment' LIKE '%form <%'
  and id = 666840;


update rule_reference
set "order" = 1094
where id = 2729;
update rule_reference
set operation = 30
where id = 2729;

update rule_reference
set "order" = 1098
where id = 409;
update rule_reference
set operation = 30
where id = 409;

update rule_group_rule_reference_through
set rulegroup_id=25
where id = 4225;
update rule_group_rule_reference_through
set rulegroup_id=25
where id = 4224;


UPDATE admin_user
SET permission =
WHERE id = 6;


UPDATE admin_user
SET permission = '[
  {
    "perm": "remark"
  },
  {
    "perm": "manage_prj"
  },
  {
    "perm": "browse"
  },
  {
    "perm": "manage_mold"
  }
]'::json
WHERE id = 299;


select max(id)
from director;

SELECT *
FROM director
WHERE stock_code = '00001'
  and EXTRACT(YEAR FROM AGE('2023-10-02', appointment_date)) >= 9
SELECT AGE('2023-10-02', appointment_date), appointment_date
FROM "director" AS "t1"
WHERE (("t1"."stock_code" = '00001') AND (EXTRACT(YEAR FROM AGE('2000-05-30', appointment_date)) >= 8))

SELECT "t1"."id",
       "t1"."stock_code",
       "t1"."capacity",
       "t1"."position",
       AGE('2033-10-02', appointment_date),
       "t1"."appointment_date",
       "t1"."resignation_date",
       "t1"."created_utc"
FROM "director" AS "t1"
WHERE (((("t1"."stock_code" = '00001') AND
         ("t1"."capacity" IN ('Independent Non Executive Director - A/F', 'Independent Non Executive Director'))) AND
        ("t1"."resignation_date" IS NULL)) AND (EXTRACT(YEAR FROM AGE('2033-10-02', appointment_date)) >= 9))
explain
select *
from director
where english_name = 'CHAN Wai Fung';


SELECT *
FROM "hkex_file_meta" AS "t1"
WHERE ((("t1"."deleted_utc" = 0) AND ("t1"."stock_code" = '00001')) AND ("t1"."doc_type" = 1))
ORDER BY "t1"."published" DESC

select fid
from hkex_file_meta
where stock_code = '01848'
  and doc_type = 1;
update agm_meta
set convening_date='2024-06-03'
where agm_fid = 71130;

SELECT "t1"."id",
       "t1"."name",
       "t1"."fid",
       "t1"."company",
       "t1"."stock_code",
       "t1"."type",
       "t1"."headline",
       "t1"."url",
       "t1"."result",
       "t1"."release_time",
       "t1"."finished_utc",
       "t1"."hash",
       "t1"."size",
       "t1"."tu_hash",
       "t1"."pdf_hash",
       "t1"."meta",
       "t1"."status",
       "t1"."created_utc",
       "t1"."updated_utc",
       "t1"."deleted_utc"
FROM "hkex_file" AS "t1"
         INNER JOIN "agm_meta" AS "t2" ON ("t1"."fid" = "t2"."agm_fid")
WHERE ((("t1"."deleted_utc" = 0) AND ("t2"."deleted_utc" = 0)) AND
       ((("t1"."type" = 'AGM') AND ("t2"."agm_fid" >= 71167)) AND ("t2"."agm_fid" <= 9000)))


SELECT "t1"."id",
       "t1"."english_company",
       "t1"."chinese_company",
       "t1"."stock_code",
       "t1"."listing_status",
       "t1"."english_name",
       "t1"."chinese_name",
       "t1"."capacity",
       "t1"."position",
       "t1"."appointment_date",
       "t1"."resignation_date",
       "t1"."created_utc"
FROM "director" AS "t1"
WHERE "t1"."stock_code" = '03991'
    AND "t1"."appointment_date" <= '2024-05-24'
    AND "t1"."appointment_date" >= '2023-05-24'
--   AND (("t1"."resignation_date" IS NULL) OR ("t1"."resignation_date" >= '2024-05-24'))
    AND ("t1"."resignation_date" IS NULL)
   OR ("t1"."resignation_date" >= '2024-05-24')

select name
from admin_user
where name not like '%pai_%'
  and platform != 20
  and status == 1;


select count(h.fid)
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.report_year = '2024';

select count(h.fid)
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (2)
  and h.report_year = '2024';

select h.fid as fid
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.report_year = '2024'
  and h.fid not in
      (70517, 70525, 70527, 70528, 70529, 70530, 70534, 70536, 70537, 70538, 70539, 70543, 70544, 70551, 70557, 70558,
       70559, 70560, 70561, 70562, 70564, 70565, 70566, 70567, 70568, 70569, 70570, 70571, 70572, 70573, 70574, 70575,
       70576, 70577, 70578, 70579, 70580, 70582, 70581, 70583, 70584, 70585, 70586, 70587, 70588, 70589, 70590, 70591,
       70592, 70593, 70594, 70595, 70596, 70597, 70598, 70599, 70600, 70601, 70602, 70603, 70605, 70604, 70606, 70607,
       70608, 70609, 70610, 70612, 70613, 70614, 70615, 70617, 70618, 70619, 70620, 70621, 70622, 70623, 70624, 70625,
       70626, 70627, 70628, 70629, 70630, 70631, 70632, 70633, 70634, 70635, 70637, 70638, 70639, 70640, 70641, 70643,
       70642, 70644, 70645, 70647, 70646, 70648, 70649, 70650, 70651, 70652, 70653, 70654, 70655, 70656, 70658, 70662,
       70659, 70660, 70663, 70661, 70664, 70665, 70666, 70667, 70668, 70669, 70671, 70672, 70673, 70674, 70675, 70676,
       70677, 70678, 70679, 70680, 70681, 70683, 70682, 70684, 70685, 70686, 70687, 70688, 70689, 70690, 70691, 70692,
       70693, 70694, 70695, 70696, 70697, 70698, 70699, 70702, 70701, 70703, 70700, 70704, 70705, 70706, 70707, 70709,
       70708, 70712, 70710, 70713, 70711, 70714, 70715, 70716, 70717, 70718, 70719, 70720, 70721, 70722, 70723, 70724,
       70725, 70726, 70727, 70728, 70729, 70730, 70731, 70732, 70733, 70734, 70735, 70736, 70737, 70738, 70739, 70740,
       70741, 70742, 70743, 70744, 70745, 70746, 70747, 70749, 70748, 70751, 70752, 70754, 70755, 70756, 70757, 70758,
       70759, 70760, 70761, 70762, 70763, 70764, 70765, 70766, 70767, 70768, 70769, 70770, 70771, 70772, 70773, 70775,
       70776, 70777, 70778, 70779, 70780, 70781, 70783, 70782, 70784, 70785, 70786, 70787, 70788, 70789, 70790, 70791,
       70792, 70793, 70794, 70795, 70796, 70797, 70798, 70799, 70800, 70801, 70802, 70803, 70805, 70806, 70807, 70809,
       70810, 70811, 70812, 70813, 70814, 70815, 70816, 70817, 70818, 70819, 70820, 70821, 70823, 70822, 70824, 70826,
       70828, 70829, 70830, 70832, 70833, 70836, 70838, 70841, 70842, 70843, 70845, 70846, 70847, 70848, 70849, 70850,
       70855, 70860, 70862, 70864, 70865, 70866, 70867, 70868, 70871, 70872, 70873, 70874, 70878, 70881, 70883, 70887,
       70888, 70889, 70890, 70891, 70894, 70895, 70898, 70900, 70903, 70905, 70906, 70907, 70908, 70911, 70913, 70916,
       70917, 70918, 70919, 70920, 70922, 70923, 70924, 70929, 70930, 70932, 71302, 70934, 70936, 70938, 70940, 70942,
       70944, 70946, 70951, 70952, 70953, 70955, 70957, 70958, 70959, 70962, 70963, 70965, 70967, 70968, 70969, 70972,
       70973, 70974, 70976, 70975, 70977, 70979, 70980, 70981, 70983, 70984, 70986, 70987, 70991, 70994, 70995, 70996,
       70997, 70998, 70999, 71007, 71011, 71020, 71028, 71288, 71289, 71290, 71291, 71292, 71294, 71293, 71295, 71296,
       71297, 71298, 71299, 71300, 71301, 71303, 71304, 71305, 71306, 71307, 71308, 71309, 71310, 71311, 71313, 71312,
       71314, 71315, 71316, 71317, 71318, 71319, 71320, 71321, 71322, 71323, 71324, 71325, 71326, 71327, 71329, 71330,
       71331, 71333, 71332, 71334, 71335, 71336, 71337, 71338, 71339, 71340, 71341, 71342, 71343, 71344, 71345, 71346,
       71347, 71348, 71349, 71350, 71352, 71351, 71353, 71354, 71355, 71356, 71357, 71358, 71359, 71360, 71361, 71362,
       71363, 71364, 71365, 71366, 71367, 71368, 71369, 71370, 71371, 71372, 71373, 71374, 71375, 71376, 71377, 71378,
       71379, 71380, 71381, 71382, 71383, 71384, 71385, 71386, 71387, 71388, 71389, 71390, 71391, 71392, 71393, 71394,
       71395, 71396, 71397, 71398, 71399, 71400, 71401, 71402, 71403, 71404, 71405, 71406, 71407, 71408, 71409, 71410,
       71411, 71412, 71413, 71414, 71415, 71416, 71417, 71418, 71419, 71420, 71421, 71422, 71423, 71424, 71425, 71426,
       71427, 71428, 71429, 71430, 71431, 71432, 71433, 71434, 71435, 71436, 71437, 71438, 71439, 71440, 71441, 71442,
       71443, 71444, 71445, 71446);

select count(h.fid)
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.report_year = '2024'
  and h.fid not in
      (70517, 70525, 70527, 70528, 70529, 70530, 70534, 70536, 70537, 70538, 70539, 70543, 70544, 70551, 70557, 70558,
       70559, 70560, 70561, 70562, 70564, 70565, 70566, 70567, 70568, 70569, 70570, 70571, 70572, 70573, 70574, 70575,
       70576, 70577, 70578, 70579, 70580, 70582, 70581, 70583, 70584, 70585, 70586, 70587, 70588, 70589, 70590, 70591,
       70592, 70593, 70594, 70595, 70596, 70597, 70598, 70599, 70600, 70601, 70602, 70603, 70605, 70604, 70606, 70607,
       70608, 70609, 70610, 70612, 70613, 70614, 70615, 70617, 70618, 70619, 70620, 70621, 70622, 70623, 70624, 70625,
       70626, 70627, 70628, 70629, 70630, 70631, 70632, 70633, 70634, 70635, 70637, 70638, 70639, 70640, 70641, 70643,
       70642, 70644, 70645, 70647, 70646, 70648, 70649, 70650, 70651, 70652, 70653, 70654, 70655, 70656, 70658, 70662,
       70659, 70660, 70663, 70661, 70664, 70665, 70666, 70667, 70668, 70669, 70671, 70672, 70673, 70674, 70675, 70676,
       70677, 70678, 70679, 70680, 70681, 70683, 70682, 70684, 70685, 70686, 70687, 70688, 70689, 70690, 70691, 70692,
       70693, 70694, 70695, 70696, 70697, 70698, 70699, 70702, 70701, 70703, 70700, 70704, 70705, 70706, 70707, 70709,
       70708, 70712, 70710, 70713, 70711, 70714, 70715, 70716, 70717, 70718, 70719, 70720, 70721, 70722, 70723, 70724,
       70725, 70726, 70727, 70728, 70729, 70730, 70731, 70732, 70733, 70734, 70735, 70736, 70737, 70738, 70739, 70740,
       70741, 70742, 70743, 70744, 70745, 70746, 70747, 70749, 70748, 70751, 70752, 70754, 70755, 70756, 70757, 70758,
       70759, 70760, 70761, 70762, 70763, 70764, 70765, 70766, 70767, 70768, 70769, 70770, 70771, 70772, 70773, 70775,
       70776, 70777, 70778, 70779, 70780, 70781, 70783, 70782, 70784, 70785, 70786, 70787, 70788, 70789, 70790, 70791,
       70792, 70793, 70794, 70795, 70796, 70797, 70798, 70799, 70800, 70801, 70802, 70803, 70805, 70806, 70807, 70809,
       70810, 70811, 70812, 70813, 70814, 70815, 70816, 70817, 70818, 70819, 70820, 70821, 70823, 70822, 70824, 70826,
       70828, 70829, 70830, 70832, 70833, 70836, 70838, 70841, 70842, 70843, 70845, 70846, 70847, 70848, 70849, 70850,
       70855, 70860, 70862, 70864, 70865, 70866, 70867, 70868, 70871, 70872, 70873, 70874, 70878, 70881, 70883, 70887,
       70888, 70889, 70890, 70891, 70894, 70895, 70898, 70900, 70903, 70905, 70906, 70907, 70908, 70911, 70913, 70916,
       70917, 70918, 70919, 70920, 70922, 70923, 70924, 70929, 70930, 70932, 71302, 70934, 70936, 70938, 70940, 70942,
       70944, 70946, 70951, 70952, 70953, 70955, 70957, 70958, 70959, 70962, 70963, 70965, 70967, 70968, 70969, 70972,
       70973, 70974, 70976, 70975, 70977, 70979, 70980, 70981, 70983, 70984, 70986, 70987, 70991, 70994, 70995, 70996,
       70997, 70998, 70999, 71007, 71011, 71020, 71028, 71288, 71289, 71290, 71291, 71292, 71294, 71293, 71295, 71296,
       71297, 71298, 71299, 71300, 71301, 71303, 71304, 71305, 71306, 71307, 71308, 71309, 71310, 71311, 71313, 71312,
       71314, 71315, 71316, 71317, 71318, 71319, 71320, 71321, 71322, 71323, 71324, 71325, 71326, 71327, 71329, 71330,
       71331, 71333, 71332, 71334, 71335, 71336, 71337, 71338, 71339, 71340, 71341, 71342, 71343, 71344, 71345, 71346,
       71347, 71348, 71349, 71350, 71352, 71351, 71353, 71354, 71355, 71356, 71357, 71358, 71359, 71360, 71361, 71362,
       71363, 71364, 71365, 71366, 71367, 71368, 71369, 71370, 71371, 71372, 71373, 71374, 71375, 71376, 71377, 71378,
       71379, 71380, 71381, 71382, 71383, 71384, 71385, 71386, 71387, 71388, 71389, 71390, 71391, 71392, 71393, 71394,
       71395, 71396, 71397, 71398, 71399, 71400, 71401, 71402, 71403, 71404, 71405, 71406, 71407, 71408, 71409, 71410,
       71411, 71412, 71413, 71414, 71415, 71416, 71417, 71418, 71419, 71420, 71421, 71422, 71423, 71424, 71425, 71426,
       71427, 71428, 71429, 71430, 71431, 71432, 71433, 71434, 71435, 71436, 71437, 71438, 71439, 71440, 71441, 71442,
       71443, 71444, 71445, 71446);


select fid
from hkex_file_meta
where fid in (86768, 88812, 86007)

select *
from hkex_file_meta
where doc_type = 31;


SELECT "t1"."convening_date"
FROM "agm_meta" AS "t1"
WHERE ((("t1"."deleted_utc" = 0) AND ("t1"."stock_code" = '03991')) AND ("t1"."convening_date" < '2024-05-24'))
ORDER BY "t1"."convening_date" DESC
LIMIT 1
select distinct agm_meta.agm_fid
from agm_meta
where stock_code in
      ('00012', '03883', '01821', '01821', '00968', '03382', '00034', '01818', '00968', '00099', '00099', '08403',
       '01246', '01207', '01901', '03618', '00251', '01122', '03618', '00124', '01207', '00876', '00876', '00216',
       '00306', '00093', '00861', '00363', '00869', '00596', '00978', '00194', '01026', '02378', '02378', '08657',
       '02378', '01098', '03839', '03808', '02502', '02150', '02502', '03808', '00156', '02008', '00289', '00194',
       '00156', '00156', '00934', '06030', '06030', '00934', '00934', '06030', '00156', '06030', '08201', '00934',
       '01098', '01098', '03808', '03808', '00423', '00156', '03808', '00298', '00298', '00889', '03808', '00298',
       '00998', '03808', '00889', '01548', '00998', '00998', '00889', '00194', '00127', '01548', '01010', '09658',
       '02205', '06033', '02407', '00127', '00041', '00045', '01010', '01548', '00298', '00828', '00041', '00045',
       '08269', '00041', '01055', '00041', '01055', '00828', '00045', '00423', '00041', '00511', '00511', '01213',
       '00041', '00041', '00045', '01055', '00045', '00045', '00045', '00258', '00117', '00117', '00117', '00117',
       '00008', '00008', '01478', '00655', '00117', '00117', '01478', '00511', '00258', '00117', '00117', '08213',
       '01270', '02202', '00511', '00511', '08121', '00511', '00511', '01821', '02202', '08121', '01109', '00007',
       '06099', '08083', '02202', '02708', '01252', '01821', '00968', '00968', '00686', '00655', '01821', '00968',
       '00458', '01519', '00686', '00968', '01310', '01310', '00968', '00968', '00458', '00458', '01310', '02299',
       '00458', '01519', '01310', '00034', '02700', '01740', '03618', '00218', '03618', '01466', '01310', '01310',
       '01310', '01310', '02700', '03382', '00587', '01310', '02858', '03382', '00686', '02299', '08307', '03618',
       '00360', '03618', '00976', '03618', '03618', '00976', '00976', '00604', '00183', '03618', '03618', '03618',
       '00976', '00226', '00226', '03618', '00360', '00604', '00976', '02858', '00247', '00093', '03883', '00093',
       '00093', '00360', '02142', '06099', '02142', '06099', '08657', '06099', '08657', '08657', '08657', '01631',
       '06099', '08432', '06099', '01631', '01630', '08657', '01630', '08657', '00806', '06823', '08657', '00412',
       '08432', '00247', '02858', '02858', '01247', '09991', '02858', '09991', '02858', '06189', '02858', '02858',
       '02858', '00110', '00110', '00806', '00851', '00851', '01351', '00247', '00247', '06033', '01247', '00851',
       '01478', '01715', '00851', '00156', '02188', '00066', '03813', '01599', '00806', '08206', '01359', '01359',
       '00691', '00521', '01359', '01359', '01478', '00828', '00691', '01478', '00077', '01359', '01359', '06908',
       '01478', '03813', '00828', '01005', '00119', '01005', '01221', '01221', '00806', '00806', '08146', '01221',
       '00636', '00077', '01221', '06823', '00369', '00077', '00393', '00156', '03382', '08206', '02199', '06686',
       '01452', '00348', '00393', '06928', '02799', '01452', '06686', '01513', '00343', '08375', '00868', '00393',
       '01588', '00083', '08370', '00343', '00343', '00077', '00077', '00077', '00016', '08375', '00489', '00489',
       '00016', '08536', '00128', '00128', '02282', '00226', '01513', '02282', '02282', '02282', '09995', '02282',
       '01797', '02282', '02282', '02282', '08195', '00016', '00128', '00016', '00006', '00016', '09995', '01255',
       '01623', '01623', '08536', '09995', '00924', '01525', '02102', '00770', '00128', '00006', '00006', '00011',
       '00006', '01579', '00924', '00770', '00128', '00128', '00128', '00011', '00011', '00011', '03382', '00343',
       '00128', '00128', '00128', '00011', '00011', '00343', '02102', '08370', '00128', '00011', '00343', '00343',
       '00343', '01463', '00607', '00868', '00722', '00295', '00607', '00295', '00018', '00156', '00018', '02002',
       '03662', '00834', '02002', '00018', '02002', '00912', '08473', '00018', '00868', '00018', '00834', '08473',
       '03662', '03666', '00731', '00912', '00868', '00731', '02096', '00868', '00722', '00277', '02096', '00868',
       '00868', '02002', '00868', '06888', '06888', '00607', '02002', '01299', '02388', '02388', '00607', '01629',
       '01521', '03382', '00392', '00468', '00538', '02096', '01299', '02588', '00099', '01299', '00864', '01521',
       '00277', '01181', '02588', '02388', '02388', '09999', '02588', '00864', '01181', '02388', '01181', '00418',
       '02388', '01181', '02588', '01263', '02388', '02588', '01263', '03382', '02588', '02588', '03382', '02168',
       '02388', '02588', '00480', '00178', '00178', '00611', '00828', '00197', '00197', '02666', '02666', '02628',
       '06929', '01635', '01635', '00178', '02666', '00178', '02666', '02666', '00183', '02666', '02666', '02666',
       '00178', '00178', '06929', '00178', '06889', '00178', '02666', '02666', '02666', '00183', '00183', '00196',
       '00874', '01206', '00379', '01179', '02003', '08283', '01686', '01686', '09999', '01179', '01179', '01686',
       '01686', '00482', '01686', '01999', '00482', '00728', '02378', '00288', '01179', '01686', '01686', '01686',
       '02003', '00099', '00552', '00480', '08372', '00288', '09969', '00480', '00480', '00754', '09999', '00552',
       '08372', '01608', '00480', '02638', '00552', '00480', '00480', '00873', '00754', '01398', '00754', '09923',
       '01286', '00754', '09923', '02638', '01286', '00480', '01608', '00124', '00171', '02638', '01540', '09999',
       '02168', '01540', '01540', '02638', '01258', '09698', '06996', '00023', '01675', '01398', '01398', '01246',
       '00023', '01258', '02883', '09698', '01398', '01398', '01398', '00716', '00171', '01451', '00266', '06988',
       '00266', '09698', '09698', '00311', '08373', '01137', '01451', '03700', '01339', '08373', '06117', '09983',
       '09983', '06988', '00369', '02888', '00738', '00738', '00775', '00775', '09977', '01339', '09977', '01137',
       '01339', '09977', '01339', '01201', '01339', '00267', '01339', '00267', '02888', '00975', '00858', '03698',
       '06988', '00316', '03302', '01591', '02888', '01591', '00267', '02888', '00267', '00267', '00267', '03302',
       '00267', '00267', '06860', '00858', '00975', '01201', '00189', '00399', '00053', '00399', '00399', '00226',
       '00978', '00399', '03309', '00399', '00189', '00053', '01515', '03759', '00226', '00689', '00675', '00348',
       '00348', '00675', '02195', '00226', '01586', '02273', '00215', '00662', '00662', '08285', '00689', '00226',
       '02195', '02866', '02407', '00677', '02113', '00689', '01950', '00226', '01072', '00286', '00993', '00993',
       '00538', '06818', '00677', '00286', '00116', '02789', '01072', '01586', '00468', '00646', '00116', '01658',
       '08285', '01072', '01072', '00468', '01072', '01202', '01072', '01072', '01072', '01072', '00468', '01752',
       '01752', '00468', '01072', '01713', '02866', '00001', '00001', '00001', '00001', '09998', '02038', '01036',
       '00002', '01036', '02051', '01443', '01443', '01655', '01655', '02500', '00090', '01036', '01036', '02038',
       '01443', '00001', '08635', '00302', '00001', '00001', '00001', '01443', '09998', '01036', '01036', '01277',
       '00884', '01443', '01036', '01443', '01036', '00002', '01036', '01036', '01036', '01160', '00338', '00338',
       '06999', '00090', '00002', '01986', '01986', '00338', '00338', '00187', '01986', '00002', '00338', '00002',
       '08430', '00002', '01340', '01340', '00187', '01199', '01712', '02779', '08472', '08472', '06188', '00838',
       '00936', '00936', '00635', '01726', '08428', '08428', '00373', '00635', '01726', '00513', '00513', '00373',
       '00635', '00599', '00635', '00709', '00709', '00599', '03613', '00599', '09857', '01090', '03759', '00709',
       '00909', '00599', '00709', '00709', '03613', '00709', '03759', '00709', '09857', '00709', '00709', '03759',
       '01105', '02433', '06196', '01818', '03398', '08606', '08140', '01202', '02433', '00646', '02433', '01818',
       '01202', '06196', '01277', '01612', '01826', '01826', '01816', '00213', '00144', '00179', '01612', '01612',
       '01929', '00179', '00213', '00425', '00425', '00101', '00144', '00179', '00144', '00144', '00179', '00179',
       '00179', '00101', '00179', '00179', '01929', '00144', '00144', '01929', '01929', '01929', '01312', '01929',
       '08606', '03322', '01039', '00010', '00213', '02328', '00063', '02328', '01312', '00010', '00063', '02328',
       '03322', '01034', '02400', '06690', '00063', '09982', '02400', '06690', '08456', '00139', '09982', '01246',
       '08071', '02678', '00363', '06690', '01578', '06690', '00363', '00363', '00992', '00992', '00992', '02350',
       '02350', '00992', '02420', '02772', '00412', '00412', '01402', '02772', '09988', '09988', '02420', '00648',
       '01402', '02898', '00412', '02898', '00412', '00412', '00412', '08439', '09988', '01901', '00412', '09988',
       '01260', '00733', '00097', '00636', '00083', '06058', '00097', '00097', '00733', '00097', '00083', '00083',
       '00338', '09939', '00097', '00097', '00097', '00338', '00097', '00338', '00338', '06058', '01856', '08460',
       '06862', '09688', '09688', '06862', '01328', '01561', '01328', '01328', '06862', '06178', '00165', '00013',
       '09688', '00984', '00984', '01679', '00476', '00476', '09688', '00336', '00013', '00013', '00013', '00013',
       '00013', '00013', '01332', '00013', '01332', '00095', '08328', '00337', '03309', '01156', '03993', '01156',
       '03309', '08328', '01114', '00131', '08328', '06178', '00336', '03309', '03309', '03309', '03309', '03309',
       '01372', '01372', '01372', '08057', '00536', '00511', '08328', '08328', '08328', '00938', '08057', '01114',
       '00938', '00235', '01114', '08176', '08402', '00235', '02356', '02356', '00242', '00432', '00432', '02869',
       '08148', '01563', '00242', '00242', '00536', '00533', '01563', '00536', '01496', '02356', '01853', '01496',
       '01853', '00983', '00241', '03998', '01508', '03998', '00869', '00869', '01508', '03998', '00869', '06610',
       '06893', '06893', '01508', '01508', '01558', '01508', '09908', '08181', '00160', '00831', '00831', '01038',
       '02013', '02013', '02013', '01038', '01908', '00279', '00272', '00272', '00272', '00272', '09908', '01038',
       '00016', '02013', '01038', '01038', '08493', '00016', '08493', '01038', '01038', '00016', '01038', '00315',
       '00331', '01908', '00377', '03860', '00315', '01965', '01815', '01908', '01815', '00315', '02427', '00315',
       '08227', '02427', '01623', '09863', '01676', '01991', '00685', '01965', '01676', '01449', '08035', '00554',
       '00536', '01676', '01060', '00367', '01965', '01676', '02360', '02360', '00505', '03668', '00871', '00379',
       '00951', '00505', '03668', '00951', '00685', '00685', '03668', '01060', '03668', '01060', '00367', '00280',
       '00280', '00280', '09886', '00830', '01060', '00392', '00367', '00685', '00367', '01798', '00145', '01907',
       '02322', '00716', '00145', '00145', '01907', '00280', '01270', '00533', '00830', '00145', '00716', '00716',
       '08081', '00522', '00522', '00522', '00280', '00716', '00280', '09886', '00716', '00716', '00716', '00280',
       '00280', '01525', '00522', '00522', '00280', '08081', '03997', '00522', '03997', '08247', '00522', '00522',
       '00224', '01270', '00915', '01044', '00002', '01638', '00990', '01044', '02319', '01798', '02439', '02439',
       '00224', '01044', '01270', '01044', '06055', '08501', '01044', '00836', '00212', '01270', '01044', '08247',
       '01044', '01026', '01044', '01044', '01891', '01044', '06055', '01058', '00111', '02858', '08247', '08247',
       '02356', '00632', '00632', '01750', '00316', '08279', '00440', '00915', '00815', '00554', '03868', '01750',
       '00331', '00650', '03868', '01597', '08516', '03868', '08279', '08279', '00815', '00915', '01712', '08279',
       '03868', '03868', '01597', '01039', '00915', '00205', '00440', '03868', '00331', '03868', '00915', '01112',
       '00440', '00813', '08083', '01134', '03869', '00440', '00627', '03836', '01058', '01112', '00331', '01134',
       '00655', '00655', '01951', '01134', '00655', '00655', '06993', '06993', '00655', '06993', '00655', '00331',
       '06610', '00331', '06993', '01112', '00331', '00331', '06993', '02199', '02199', '02199', '01895', '00229',
       '00229', '01895', '01895', '00983', '01895', '00071', '08050', '00071', '00071', '01846', '08050', '00683',
       '01846', '08239', '00241', '00215', '00215', '00683', '00215', '00411', '02459', '00683', '00683', '00411',
       '00683', '00683', '00327', '00327', '01928', '01928', '01508', '01928', '00197', '00709', '00197', '00125',
       '00193', '00750', '00709', '00393', '00125', '00709', '01104', '01104', '00012', '00012', '00012', '00012',
       '02199', '00012', '01079', '00345', '01079', '00018', '06193', '06193', '00018', '00279', '00018', '00345',
       '02199', '00345', '00251', '02018', '02382', '02018', '01207', '00316', '01689', '02018', '01207', '01799',
       '01635', '01818', '01818', '00235', '02018', '00194', '02018', '01729', '02382', '02018', '01729', '00217',
       '01207', '01901', '01480', '00316', '00316', '00316', '00316', '00316', '02199', '00333', '02388', '00333',
       '00215', '00215', '01627', '08201', '02388', '00235', '06886', '02199', '00235', '08201', '02382', '06913',
       '02382', '06886', '08201', '00106', '00105', '06886', '00105', '01627', '01495', '00106', '06913', '06913',
       '00884', '06913', '02382', '02382', '00301', '00235', '01992', '02382', '02348', '06913', '02348', '00306',
       '02008', '00251', '00251', '00315', '02128', '00315', '03839', '01098', '01098', '00289', '03808', '00315',
       '03808', '03808', '03808', '00861', '00315', '00861', '02128', '00041', '02096', '00041', '00861', '01928',
       '00012', '01270', '08072', '00071', '00012')
order by agm_fid;


select agm_fid, count(*) as num
from agm_meta
group by agm_fid
order by num desc;

DELETE
FROM agm_meta
WHERE id IN (SELECT id
             FROM (SELECT id,
                          agm_fid,
                          ROW_NUMBER() OVER (PARTITION BY agm_fid ORDER BY id) as rn
                   FROM agm_meta) t
             WHERE rn > 1)


-- ./bin/db_util.sh sql "\COPY (select fid, 'http://100.64.0.105:55647/#/search?fileid=' || fid as url, url as hkex_news_url, name from hkex_file where hkex_file.type = 'AGM' and fid in (79495,79248,79254,79266,81580,79286,81920,81923,84869,89046,89052,79450,78630,79455,82022,89055,89069,89070,79488,78676,84159,84977,86188,78678,88322,87534,87549,79582,87544,78766,78786,78787,81115,79661,81117,78789,81139,78694,78768,81157,81141,78755,78700,78712,78754,78752,78701,81144,78704,89243,78753,78769,78771,78793,78796,81185,81153,78858,78868,78808,78863,78799,78877,78909,78856,78872,78899,78890,78896,81159,78895,78973,78885,78908,78859,81632,78989,78976,81635,81178,78978,81188,81192,78952,78990,78995,79005,79082,79063,79072,79060,79070,79147,79153,79833,87427,79059,79078,79844,81190,79076,79085,79065,79166,86974,81193,81197,79139,81198,81196,79252,79142,86979,79191,79553,87435,88364,79245,79268,79279,87437,79250,79280,79310,79329,79284,79357,79360,79281,79278,79313,79317,79381,79393,79307,79324,79362,81232,79293,79468,84212,79456,81247,79365,79375,81229,81591,81298,81595,79379,79392,79447,81248,79452,79438,79442,79469,79474,79464,81766,79441,79454,79459,79463,87559,87566,79453,81253,79476,79475,81307,79500,81264,79549,79563,79552,79562,79578,79565,79584,79583,79586,79604,79551,84228,79575,79598,79635,79579,79643,79580,79963,79980,79588,85152,84234,81300,81302,81309,81292,81299,81305,81313,79627,81297,81306,81304,79655,79668,79978,79691,81311,81316,79779,81318,79832,81133,79877,79904,79987,80009,79829,79843,79913,79839,79841,81640,79927,79834,80243,79827,79837,81353,79912,81652,79884,81356,79891,81360,79969,79973,80005,80249,79967,79977,80260,84256,81137,81589,80010,88370,80071,82687,84255,84998,80177,80068,81438,81473,81429,80869,84264,81483,81485,81491,80242,80253,80255,81427,80263,80279,80389,80338,87584,81433,80323,80328,80331,80412,80326,80344,80322,80325,80332,80353,80405,80397,80402,80392,80408,81474,80465,80591,80609,80518,81466,80608,81590,81471,81487,80573,81490,81488,81492,81493,80707,80919,80854,81520,80719,80918,80724,81146,80797,80828,80861,80844,80864,80772,80880,80825,80776,80786,81610,80821,80863,80878,80808,84303,80872,81521,81544,84302,80866,80875,80852,80867,80889,80894,80911,80847,80909,80926,80946,81577,86627,82989,84308,80996,81926,81022,80935,81551,81010,84429,81009,81024,84313,80997,81041,80998,81037,81575,81004,81013,81578,82003,80999,84408,81689,81684,81799,81643,81648,81655,81738,81706,81696,81729,81775,81688,81690,81743,81686,81687,81695,81774,81772,81811,86692,81845,81893,81818,81876,81880,84427,81832,81836,81868,81875,81866,84424,87536,81902,81831,81869,81877,81888,81892,81919,84402,84407,81899,84386,84401,84411,84450,84404,81972,84400,84409,84416,84423,84414,82100,81965,84413,81964,84417,81978,82024,84454,82040,84431,82013,82020,82023,82197,84441,82058,82092,82106,84874,82060,82193,82091,82098,82099,84462,82268,82209,84497,82206,82194,82204,82224,82232,82274,82348,82233,82242,82244,84494,79979,82461,82491,82498,82351,82341,82340,84504,82346,82354,82440,82482,87645,84493,89125,82451,82401,82448,82406,82447,82455,84501,87650,82483,84509,82565,82558,82561,87567,82563,85798,82555,82616,83574,87571,82809,82680,82684,82696,82675,84589,87576,82888,82721,82713,82908,82807,87579,84581,81163,82863,82829,82818,82826,87586,82993,82897,82905,82913,82962,82852,82867,82887,82900,82873,82992,82894,82986,83604,82917,82916,83013,82978,82982,82985,82977,82983,82987,82991,83004,83031,83033,82996,82995,83154,83168,83200,84936,83214,83317,83215,83177,83191,83052,83081,84619,84646,83208,83217,84932,83189,83169,83122,83136,83157,83161,83182,83202,83222,83225,83658,83186,83211,83183,83221,83213,83220,83229,85354,85357,84633,84637,83324,83267,83262,85358,85362,83270,85360,83327,83351,83343,87210,83429,87619,87624,87713,83495,83494,83498,83518,83476,83479,83500,83511,83515,83480,83482,83499,83506,83527,83504,83537,83540,83547,83560,83568,83558,83542,83578,83555,83572,84757,83637,83595,83645,83908,83635,83606,83629,83639,83654,84786,83700,83697,83963,83835,83843,84789,84793,83864,83845,83960,83840,83830,83834,83842,83844,83836,83849,83866,83831,83861,83865,83868,84005,83863,83910,84060,83987,84816,83962,84811,84047,84813,84014,84052,84818,84056,84063,87744,84053,84104,87746,84102,84870,84987,88512,84990,85046,85050,85045,85047,85093,85122,85156,85157,85106,85121,85172,85181,85094,85108,87792,85158,87791,85165,85153,85169,85182,89053,85164,85178,85290,85306,85404,85305,85282,85361,85413,85355,85356,85350,85402,85538,85532,85535,85552,85542,87908,85625,85645,85539,85593,85592,85513,85517,85565,85784,85653,85652,85644,85648,85674,85666,80232,87928,85807,85730,85729,85803,87926,85948,85954,87925,87912,85788,85804,85812,85801,85805,85809,85916,85919,85937,86079,81194,87932,87940,87936,85922,85935,85946,85926,89171,85949,89177,86120,86123,88308,86011,86076,86085,86016,86083,87983,86095,87993,86111,86143,86149,86175,86180,86183,86181,86192,86194,86178,86184,88301,86170,86176,86193,86195,86191,86261,88002,86223,86215,88013,88016,88022,86229,86252,86242,86243,86241,86259,88018,88023,86362,88274,86366,86435,86360,88299,88298,86363,86404,86371,86389,86399,80404,86652,88088,88108,86502,86543,86556,86078,86624,86647,86493,86538,86537,86598,86656,86671,86583,86596,86572,86628,86587,86625,86636,86798,86805,86815,88129,88153,86618,86626,88109,86638,86696,86752,86698,86804,86985,86093,88157,86843,86858,86859,86863,88139,86760,86761,86809,86814,86848,86855,86836,86852,86839,88180,86853,86851,86960,86976,87268,86999,86885,86917,87003,86890,86935,86934,86958,86996,86986,86995,87081,87005,87099,88190,86978,88189,86992,88329,86997,87000,87006,87018,81314,88174,87160,89121,87229,87281,87228,86554,87251,87152,88264,87241,87250,87261,87196,87246,87234,87222,87231,87275,87214,87232,87253,87255,87263,83989,87273,87252,88270,87249,87279,88242,87438,87441,87394,88225,88233,87357,88252,88256,87405,87420,87423,87462,87402,87424,87431,87475,87483,87436,87485,87451,88263,88287,88260,87477,88254,88261,88269,87484,88368,88366,88375,88417,88384,88399,88427,88414,86141,88420,88496,88475,88486,88624,88488,88507,86154,89136,88627,89142,88622,88633,88634,88631,88689,88691,88779,88785,86201,88784,81657,81659,84260,84261,88360,88361,88363,88817,88837,88819,88835,88842,86231,88851,88367,88843,78636,89010,89319,89005,89050,89120,88977,89006,89038,89174,89008,89002,89016,89321,89001,89018,89048,89059,89131,89118,89123,89117,89122,89132,88376,89088,89094,89137,89140,89296,89246,89192,88382,89176,89248,89324,89359,89320,89249,89274,89295,89298,89306,89282,89349,89352,89350,89315,89318,84140,89178,89316,89363,89357,89360,78668,78695,78634,78637,86369,84148,86374,78792,78765,78772,78775,78784,86373,78800,78802,78811,84151,86376,84155,84157,78975,84310,78993,84161,88788,86981,88463)) TO '~/output.csv' WITH (FORMAT CSV, HEADER);"

select *
from embedding
where text ilike '\bISSB\b';
select *
from embedding
where text ilike 'ISSB';

SELECT "t1"."file_id", "t1"."index", "t1"."position", "t1"."text", "t1"."metadata"
FROM "embedding" AS "t1"
         INNER JOIN (SELECT "t1"."index"
                     FROM "embedding" AS "t1"
                     WHERE ((("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71324))) AND
                            ("t1"."text" ~* '([[:<:]]ISSB[[:>:]])|([[:<:]]IFRS S[12][[:>:]])|ISSB:'))
                     GROUP BY "t1"."index") AS "subquery"
                    ON (("t1"."index" = "subquery"."index") AND
                        ("t1"."text" ~* '([[:<:]]ISSB[[:>:]])|([[:<:]]IFRS S[12][[:>:]])|ISSB:'))
WHERE (("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71324)))
ORDER BY "t1"."index"
LIMIT 20

select *
from embedding
where text ~ 'category \d'
  and file_id = 71352

SELECT "t1"."file_id", "t1"."index", "t1"."position", "t1"."text", "t1"."metadata"
FROM "embedding" AS "t1"
         INNER JOIN (SELECT "t1"."index"
                     FROM "embedding" AS "t1"
                     WHERE ((("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71352))) AND ("t1"."text" ~ 'Scope [12]'))
                     GROUP BY "t1"."index") AS "subquery"
                    ON (("t1"."index" = "subquery"."index") AND ("t1"."text" ~ 'Scope [12]'))
WHERE (("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71352)))
ORDER BY "t1"."id" DESC
LIMIT 20

SELECT "t1"."file_id", "t1"."index", "t1"."position", "t1"."text", "t1"."metadata"
FROM "embedding" AS "t1"
         INNER JOIN (SELECT "t1"."index"
                     FROM "embedding" AS "t1"
                     WHERE ((("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71398))) AND
                            ("t1"."text" ~* 'category.*percent'))
                     GROUP BY "t1"."index") AS "subquery"
                    ON (("t1"."index" = "subquery"."index") AND ("t1"."text" ~* 'category.*percent'))
WHERE (("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71398)))
ORDER BY "t1"."id" DESC
LIMIT 20

SELECT 'category 1' ~ '(category \d|scope 3)'; -- 返回 true
SELECT 'scope 3' ~ '(category \d|scope 3)'; -- 返回 true
SELECT 'category xyz' ~ '(category \d|scope 3)'; -- 返回 false


SELECT "t1"."indexes",
       (1 - ("t1"."embedding" <=>
             '[0.0386069230735302,0.02166755124926567,0.048902709037065506,0.0034858533181250095,0.00704862829297781,-0.0010990259470418096,0.004320225678384304,0.04677938297390938,0.026662252843379974,-0.004799572750926018,0.03923281654715538,-0.01827125810086727,-0.025349464267492294,-0.023746265098452568,0.05130176246166229,0.005095880012959242,-0.0072335912846028805,-0.001971007324755192,0.008957864716649055,0.0020190889481455088,0.028028983622789383,0.057091645896434784,-0.0006284898263402283,0.015445339493453503,-0.020323753356933594,0.020590530708432198,-0.026769518852233887,-0.017304837703704834,0.010145305655896664,-0.0038270745426416397,0.01836259290575981,-0.03264937922358513,0.029344718903303146,0.021151091903448105,-0.010249159298837185,0.007123075425624847,-0.0029904553666710854,0.014166801236569881,-0.004874055273830891,-0.040217090398073196,0.0025462035555392504,-0.025922227650880814,0.02112407051026821,0.018741454929113388,0.014829475432634354,0.023122534155845642,-0.038303885608911514,-0.01968010887503624,0.018504437059164047,0.024718152359128,-0.03243694826960564,0.0005678861052729189,0.0430091954767704,0.0261369775980711,-0.01350355427712202,0.003972569014877081,0.00849550124257803,0.02306334674358368,0.008791020140051842,0.0007980318041518331,-0.012893704697489738,-0.012110170908272266,0.050486013293266296,-0.019768385216593742,-0.006540406960994005,-0.002096942625939846,-0.014150530099868774,0.06220593675971031,0.008089561946690083,0.017328811809420586,0.0011016821954399347,0.011167556047439575,-0.026003718376159668,-0.007857079617679119,-0.007142112124711275,-0.005458062049001455,-0.034711163491010666,-0.01764395833015442,-0.003678068518638611,0.005467289127409458,-0.016702575609087944,0.022679166868329048,-0.015881499275565147,0.02817607671022415,-0.003450620686635375,-0.045308277010917664,-0.056002356112003326,-0.005340032745152712,-0.04796997457742691,-0.012140138074755669,-0.04221322387456894,0.03590407967567444,-0.02157311700284481,0.03919411078095436,0.00905209593474865,-0.0018719978397712111,-0.007317259907722473,0.016128195449709892,0.005321172531694174,0.03503566235303879,0.02284722588956356,-0.029277091845870018,-0.009041843004524708,-0.02351694367825985,0.021159734576940536,0.026097038760781288,-0.03901059925556183,0.006631019525229931,0.009656640700995922,-0.013376389630138874,-0.059562087059020996,-0.005655491258949041,0.0016646423609927297,0.04489952325820923,-0.008418828248977661,-0.005787888541817665,-0.05822958052158356,0.0011227196082472801,-0.01326291635632515,0.01904989592730999,-0.03649580478668213,0.0053685493767261505,0.01065328810364008,-0.007042412180453539,0.021419312804937363,-0.00834533479064703,-0.019518861547112465,-0.0016218924429267645,-0.02914356254041195,-0.019466150552034378,0.02816060371696949,0.013981951400637627,0.02372073009610176,-0.012199481017887592,-0.02163497358560562,-0.00190679170191288,-0.025757892057299614,0.03218036890029907,-0.023178691044449806,-0.031324949115514755,0.025524746626615524,-0.00799864623695612,0.0042596799321472645,-0.008677249774336815,-0.024381518363952637,-0.016130203381180763,-0.008741904981434345,-0.04060044884681702,0.012727833352982998,0.003536957548931241,-0.005400105845183134,-0.01484607346355915,-0.01706600748002529,-0.026177704334259033,0.05305697023868561,-0.011185145936906338,-0.0014757663011550903,0.01048712432384491,-0.0031022680923342705,0.015025325119495392,-0.002005413407459855,0.0510031059384346,-0.022773796692490578,-0.00873856246471405,0.004294545855373144,-0.01435363944619894,-0.017757020890712738,-0.01614290103316307,-0.07354830950498581,-0.0034489419776946306,-0.007802875712513924,-0.023656822741031647,0.0004613243800122291,-0.04201408103108406,-0.015248844400048256,-0.013550324365496635,-0.009893535636365414,-0.027010714635252953,0.0036475113593041897,-0.047605618834495544,-0.0041426848620176315,0.04320656135678291,0.010265927761793137,0.004423090722411871,-0.05349121242761612,0.02764376439154148,0.007345958147197962,0.025353677570819855,0.03160092234611511,0.015356026589870453,0.02380317635834217,0.017112739384174347,0.03535217046737671,0.03507488593459129,-0.044955287128686905,-0.028536595404148102,-0.001942609203979373,0.04335802420973778,-0.03379026800394058,0.010357898660004139,0.001985562965273857,-0.016290731728076935,0.01506775338202715,0.012252533808350563,-0.0834619477391243,0.007713468745350838,-0.03795313090085983,0.008933000266551971,0.022020619362592697,-0.009437530301511288,0.03968397155404091,0.010948849841952324,0.008162695914506912,0.020955126732587814,-0.012745147570967674,-0.030160920694470406,-0.014937127009034157,0.039086271077394485,0.02640548162162304,0.03161340206861496,0.007453317753970623,0.008311322890222073,-0.0004270562494639307,0.023849548771977425,-0.012628764845430851,0.08219623565673828,-0.003342283656820655,0.014704377390444279,-0.0024249462876468897,-0.08470407128334045,0.015903735533356667,-0.009456152096390724,-0.017895391210913658,-0.019246781244874,0.01649809069931507,-0.0014268149388954043,0.028908416628837585,-0.014020439237356186,-0.002479419345036149,0.0016814921982586384,0.03153805807232857,0.047601357102394104,-0.002250116551294923,0.017433449625968933,0.015801770612597466,0.0038891835138201714,0.008212805725634098,-0.024682218208909035,0.009208827279508114,0.04768583923578262,0.03284638375043869,0.029118891805410385,0.0072805858217179775,-0.005235336255282164,-0.04139760136604309,-0.010849600657820702,-0.019693603739142418,0.022596945986151695,-0.005464608781039715,-0.009826017543673515,0.009157668799161911,0.005979442968964577,-0.017707476392388344,-0.020176663994789124,-0.010982709936797619,0.019582383334636688,0.03653104975819588,0.020248105749487877,0.006566138938069344,0.024262089282274246,-0.0034411344677209854,-0.0012760012177750468,-0.020982913672924042,0.004005402326583862,-0.00835233461111784,0.0021834245417267084,0.007222221232950687,0.018503660336136818,-0.009240057319402695,0.049725331366062164,0.04656681790947914,-0.005432190373539925,0.017567628994584084,0.006746649742126465,-0.011146938428282738,0.0036750261206179857,0.03553382679820061,-0.00012668888666667044,0.02253398858010769,0.0363534651696682,0.022741034626960754,-0.03922043368220329,0.01819590851664543,-0.026809196919202805,0.030529333278536797,0.008025551214814186,0.03848213702440262,-0.058085907250642776,-0.0042686136439442635,0.040645428001880646,0.025852203369140625,0.02403119020164013,-0.02711249701678753,-0.010039795190095901,0.01588689349591732,0.005499767139554024,0.004353005904704332,-0.04270293191075325,-0.014376320876181126,-0.019497357308864594,0.0004086093103978783,0.0031335337553173304,0.03494091331958771,-0.03284534439444542,0.005800729617476463,0.016891010105609894,0.025234146043658257,-0.022797150537371635,-0.013076884672045708,0.009013202041387558,0.016021618619561195,-0.0009283329709433019,-0.010889304801821709,-0.0036277880426496267,0.024584835395216942,-0.005735329817980528,0.011911028996109962,0.012346499599516392,0.013739186339080334,0.001029516919516027,0.0027366140857338905,-0.01943986304104328,0.00012684427201747894,-0.0007320527802221477,-0.014900530688464642,-0.0038770423270761967,0.016465287655591965,0.004415929317474365,-0.0049788025207817554,-0.03472616523504257,0.00019800248264800757,-0.015584557317197323,-0.013419357128441334,-0.01357216015458107,-0.013986151665449142,-0.0034045453649014235,-0.016251549124717712,0.03254702687263489,0.022456537932157516,0.049157485365867615,-0.01261075958609581,-0.028220858424901962,0.0322563536465168,-0.012451295740902424,0.013310784474015236,-0.007085527293384075,0.02195291966199875,-0.0287746861577034,-0.006262272596359253,0.029114019125699997,-0.05781135708093643,0.009916285052895546,0.02047852799296379,-0.015964223071932793,0.019113142043352127,0.03614174947142601,-0.0378374308347702,0.006291191093623638,-0.03420975059270859,-0.020727651193737984,0.03750180825591087,-0.006546027027070522,-0.02845233865082264,0.030448630452156067,-0.021558962762355804,-0.013035792857408524,0.050548069179058075,0.02522328495979309,0.016806267201900482,-0.017830612137913704,0.015941960737109184,-0.06322608888149261,-0.02108268067240715,-0.009504158049821854,0.014664177782833576,-0.0030298554338514805,0.023539630696177483,-0.009827349334955215,0.014492223039269447,0.015598982572555542,0.033105675131082535,-0.007946583442389965,0.0010603218106552958,-0.018684005364775658,-0.018870670348405838,-0.009595521725714207,0.016326745972037315,0.04608943685889244,0.051964081823825836,-0.038387738168239594,-0.03263930231332779,-0.023167047649621964,-0.018417757004499435,-0.014169088564813137,-0.019565636292099953,-0.008573019877076149,-0.03793003410100937,0.018865574151277542,-0.014316694810986519,-0.02483086846768856,0.018803857266902924,-0.05806541442871094,0.02822781912982464,-0.013456194661557674,0.046577583998441696,-0.03765052184462547,-0.014435652643442154,0.003885425627231598,-0.02130693569779396,0.024015463888645172,0.001039700349792838,-0.016290538012981415,-0.0038914289325475693,0.01097116805613041,-0.013688971288502216,-0.011349212378263474,0.00459138210862875,-0.01525054033845663,0.009331236593425274,-0.050670720636844635,-0.05796905979514122,0.001188463531434536,-0.007905072532594204,0.015542559325695038,-0.0038939258083701134,-0.013516315259039402,0.013466102071106434,-0.006840118672698736,-0.0018404395086690784,-0.02813754789531231,-0.03234042599797249,0.006501760799437761,-0.010472293943166733,-0.044854048639535904,-0.047655895352363586,0.0006714443443343043,0.012867451645433903,0.012643156573176384,-0.026564791798591614,-0.01636500284075737,-0.010767499916255474,0.005699263885617256,0.022320032119750977,0.0168963260948658,-0.04935983568429947,0.004654688760638237,0.03430552035570145,-0.0055297124199569225,-0.02945862151682377,0.020656654611229897,0.00041024028905667365,0.006718836724758148,0.0060719698667526245,0.036666031926870346,-0.022817857563495636,-0.026387328281998634,-0.052022796124219894,0.016412435099482536,-0.007759931031614542,-0.013346138410270214,0.018016859889030457,0.012891028076410294,0.003580209333449602,-0.006879810709506273,0.003962649032473564,-0.02168951742351055,0.005945843178778887,-0.05373867601156235,-0.017082810401916504,-0.03484218940138817,0.011005675420165062,0.005782996769994497,-0.0035614906810224056,8.499739487888291e-05,0.02233043871819973,0.08113518357276917,0.02930317260324955,0.008568982593715191,-0.04623707756400108,0.005501372739672661,0.013767601922154427,-0.005736441817134619,0.028351295739412308,-0.014695861376821995,-0.046657610684633255,-0.03686821460723877,0.00674577197059989,-0.01756899803876877,0.012065516784787178,-0.014470978640019894,0.01846669614315033,0.011544426903128624,-0.00027269512065686285,-0.006952269934117794,0.018334969878196716,0.009329087100923061,-0.05515529587864876,-0.03100619465112686,0.02024146541953087,0.03993987292051315,0.009668204002082348,0.0339549221098423,0.004952938761562109,-0.0288202166557312,-0.01361494604498148,-0.010115131735801697,-0.010414892807602882,-0.007561394013464451,-0.004087923560291529,-0.007103865034878254,0.030429046601057053,0.019701780751347542,-0.005007769912481308,-0.0046594031155109406,-0.010482512414455414,0.017034493386745453,-0.0038123312406241894,-0.07220666855573654,-0.03734322264790535,-0.006469706539064646,0.01171820517629385,-0.014774851500988007,-0.011510500684380531,-0.00019559494103305042,0.014172476716339588,0.0011777232866734266,0.01793101243674755,-0.01809772290289402,-0.019900374114513397,-0.009424149990081787,0.02528584934771061,-0.009296434931457043,-0.015842942520976067,-0.05282994359731674,0.011007032357156277,-0.030425462871789932,-0.007888597436249256,0.01779189147055149,-0.013956236653029919,0.010092280805110931,0.0014948486350476742,0.01801457442343235,0.010719564743340015,-0.0184786356985569,-0.017366906628012657,0.025188522413372993,-0.02166336216032505,-0.027736853808164597,0.019867928698658943,-0.005155580118298531,-0.009247295558452606,-0.014333361759781837,-0.007284186314791441,-0.03851496800780296,-0.030803129076957703,0.018458765000104904,0.01301299873739481,-0.01872858591377735,-0.03253243491053581,0.02897806279361248,-0.008805138058960438,-0.00498393876478076,-0.01781202293932438,0.029462657868862152,0.02006404846906662,-0.0066202739253640175,0.02866235189139843,-0.004800830036401749,0.010187774896621704,-0.005993695463985205,0.006322042550891638,-0.018528593704104424,-0.010510009713470936,0.004691600799560547,-0.03799901157617569,0.024336466565728188,-0.007018494885414839,0.019881054759025574,0.004318672697991133,-0.013955166563391685,-0.03290873393416405,-0.008184834383428097,-0.028209317475557327,0.0304915439337492,0.00289278756827116,0.024637708440423012,0.010634029284119606,0.03504426032304764,0.014212073758244514,0.023749301210045815,0.0016250356566160917,0.02427278831601143,-0.05244604870676994,-0.02148049883544445,-0.014187542721629143,0.011194570921361446,0.01172094326466322,-0.022753214463591576,0.008011423982679844,0.008351542055606842,-6.953438423806801e-05,0.006130187772214413,-0.0029027601704001427,-0.002412213245406747,-0.017494261264801025,0.01030539907515049,0.006225104443728924,0.009037470445036888,0.006048220209777355,-8.179667202057317e-05,0.006538806948810816,-0.0033549496438354254,0.02394680678844452,-0.03259764611721039,0.024286994710564613,0.049641381949186325,0.004372461698949337,-0.01333580445498228,0.03149427846074104,0.0067706648260355,0.014819646254181862,-0.02301754802465439,-0.016169577836990356,-0.04122626781463623,-0.031630054116249084,-0.01092920359224081,0.02150852233171463,-0.006270845420658588,0.0017857656348496675,-0.012421654537320137,0.02187402918934822,-0.004087433684617281,0.014636940322816372,-0.012351235374808311,-0.0031403189059346914,-0.004857945255935192,0.008570806123316288,0.011260350234806538,-0.021126562729477882,0.008264794014394283,-0.018981516361236572,-0.02412351407110691,0.005255460739135742,-0.0024232682771980762,0.037833597511053085,-0.0013062313664704561,-0.017682945355772972,0.0038645323365926743,0.005902625620365143,0.03167551010847092,-0.030770111829042435,0.0022039839532226324,0.0011463966220617294,-0.0002689253306016326,-0.01703200489282608,0.0018045752076432109,-0.042712096124887466,1.7108181054936722e-05,0.003912713378667831,0.015551258809864521,-0.04997490718960762,0.03737649694085121,0.009377804584801197,-0.01517079584300518,0.024601807817816734,-0.0033438755199313164,-0.015135759487748146,-0.033349283039569855,0.016177598387002945,0.02544727362692356,0.01641366258263588,0.010033599101006985,0.011622276157140732,-0.011523805558681488,-0.00024185435904655606,-0.030851038172841072,0.024358296766877174,-0.005884462967514992,0.0056336126290261745,0.019860683009028435,0.009112218394875526,0.026995331048965454,0.04186462238430977,-0.008384067565202713,-0.01279553771018982,-0.0064507401548326015,0.020475516095757484,-0.0019358735298737884,-0.0391533300280571,-0.09402865916490555,0.019530922174453735,0.019500967115163803,-0.013727768324315548,-0.02447306551039219,-0.03484143316745758,0.012342385947704315,-0.022378943860530853,-0.0005747226532548666,0.029152508825063705,-0.01169949397444725,0.002977904165163636,-0.01809113472700119,-0.01929587498307228,0.0064511485397815704,0.01070827804505825,0.04592396318912506,0.008517609909176826,-0.020693618804216385,0.030745111405849457,-0.022155500948429108,0.009302584454417229,0.001848826534114778,-0.01726374588906765,-0.032854098826646805,-0.004664392210543156,0.025480782613158226,-0.0003210848371963948,0.003198235295712948,0.007227455731481314,0.009427809156477451,0.007719670422375202,-0.005053460132330656,-0.002800591988489032,0.0035192465875297785,-0.030357837677001953,0.02228448912501335,-0.0008897955412976444,0.0036667264066636562,-0.019545797258615494,-0.006772436201572418,0.03614277020096779,-0.025196144357323647,-0.026344552636146545,0.0005104768788442016,0.006567667704075575,0.014157161116600037,-0.00108996476046741,0.03448469564318657,-0.017448369413614273,0.018307948485016823,-0.020939761772751808,0.009456406347453594,0.016362110152840614,0.004800904542207718,0.028609396889805794,-0.009695000946521759,-0.012513560242950916,0.015994664281606674,-0.008139253593981266,0.0006020397413522005,-0.016519855707883835,-0.0020134381484240294,-0.0056479619815945625,-0.01781047321856022,0.0202335212379694,0.021375250071287155,-0.03968917578458786,0.014133057557046413,-0.006673365365713835,-0.029552314430475235,0.004037435632199049,-0.0023860714863985777,0.011852583847939968,-0.0202122051268816,0.02376849576830864,-0.0082807382568717,0.004043182823807001,0.005917797796428204,-0.00012987590162083507,-0.0012120740721002221,-0.00853478629142046,-0.0029202729929238558,-0.015349346213042736,0.03353447467088699,-0.01988525502383709,-0.0028968441765755415,-0.0026071493048220873,0.002064598025754094,-0.023964285850524902,0.008827541954815388,-0.00039619632298126817,-0.0019376272102817893,0.0008516935049556196,0.016973448917269707,0.018535500392317772,0.010487367399036884,0.007731163874268532,-0.08438212424516678,-0.027918841689825058,0.022981248795986176,-0.018281547352671623,0.014274372719228268,0.0004872162244282663,0.0036148594226688147,0.018484199419617653,0.009355123154819012,-0.027392830699682236,0.029145365580916405,-0.008725486695766449,0.01659638062119484,-0.009670700877904892,0.01008589006960392,0.008347295224666595,-0.006970267277210951,0.007585667539387941,0.0028650742024183273,0.010891738347709179,-0.013063953258097172,0.008021541871130466,-0.01981830969452858,0.01871306635439396,-0.002211476443335414,-0.025785749778151512,-0.009031206369400024,0.004928593523800373,0.020901285111904144,-0.0452541708946228,0.012538204900920391,0.029332425445318222,4.644856016966514e-05,0.006867764517664909,0.005696096923202276,-0.01717127114534378,-0.03308325260877609,0.01041516661643982,-0.009026731364428997,0.019032523036003113,0.0008108243928290904,0.0007048777770251036,-0.002622335683554411,0.01929711550474167,-0.006632877979427576,-0.0034174970351159573,0.00934987235814333,-0.00300448015332222,0.030803795903921127,-0.008098017424345016,0.014003023505210876,-0.008849561214447021,0.03540365397930145,-0.011203928850591183,-0.01921825483441353,0.028163312003016472,0.011085916310548782,-0.010979264974594116,-0.00740286847576499,-0.018273664638400078,-0.013667674735188484,-0.0053319986909627914,0.021796036511659622,-0.012263570912182331,-0.03926483541727066,0.012089040130376816,-0.019171347841620445,-0.00279378448612988,-0.006041129119694233,0.009937881492078304,0.03648499399423599,0.02460109442472458,-0.009836703538894653,0.01737174019217491,0.02201392687857151,-0.020101947709918022,-0.005993856582790613,-0.014500179328024387,0.014078815467655659,-0.00449036993086338,0.02059549279510975,0.01274842768907547,-0.007803564891219139,0.00721731735393405,-0.019600708037614822,0.0025737497489899397,-0.018844526261091232,0.009988573379814625,-0.0032470659352838993,-0.0273904986679554,0.0177410077303648,-0.041983313858509064,0.0017952572088688612,0.018229329958558083,0.008994065225124359,0.006471517030149698,-0.01404568087309599,-0.03268956020474434,-0.03550786152482033,-0.008915429003536701,0.02857688069343567,0.03722824528813362,0.0016985356342047453,-0.017096010968089104,0.013663345016539097,0.017418205738067627,0.0018019434064626694,-0.03946677967905998,0.000495578336995095,0.019390836358070374,-0.027127886191010475,-0.01236709300428629,0.015604809857904911,-0.00018196622841060162,0.014336848631501198,0.030927414074540138,-0.011228542774915695,-0.0013114429311826825,-0.028674867004156113,-0.022508077323436737,0.0023239587899297476,0.003810793161392212,-0.027536770328879356,-0.0002607539645396173,-0.004626107402145863,0.012369506992399693,0.006689044181257486,0.013616613112390041,0.0011756496969610453,0.013891519047319889,0.014597613364458084,0.047587763518095016,-0.02158941887319088,-0.006722866091877222,-0.018235212191939354,0.02342039905488491,-0.0023563529830425978,0.005908353254199028,0.03410705178976059,-0.0023552386555820704,0.01617930456995964,0.03386703133583069,0.018248941749334335,0.0352577343583107,0.009382974356412888,-0.01665143296122551,-0.03993604704737663,0.0008163772872649133,-0.0007612033514305949,0.010664377361536026,0.020671004429459572,0.022764049470424652,0.00986841693520546,-0.011442516930401325,-0.028310580179095268,0.006413506343960762,-0.03165799006819725,-0.0036363934632390738,0.012463556602597237,0.01767897978425026,-0.00695110484957695,0.03562241047620773,-0.053316790610551834,-0.01578379236161709,-0.01941235549747944,0.010252708569169044,-0.016475338488817215,-0.0266090277582407,0.007005513180047274,-0.009999672882258892,-0.010776782408356667,0.012877075932919979,0.006127503700554371,0.011829588562250137,0.02072603814303875,-0.0022613678593188524,-0.0067197238095104694,-0.003383867908269167,0.01682727225124836,0.0069181895814836025,0.0025859561283141375,-0.012385080568492413,0.013763696886599064,0.018460456281900406,0.011728193610906601,0.0064268349669873714,0.01379615068435669,-0.005027300678193569,0.022922851145267487,0.0004455960006453097,0.006143066566437483,0.001711220946162939,-0.00010059395572170615,0.008864758536219597,0.002596171572804451,0.0030071090441197157,0.02492532879114151,0.008940503932535648,-0.0318266786634922,-0.019355671480298042,0.03567899763584137,-0.024371791630983353,0.02144045941531658,0.009920496493577957,0.022419949993491173,-0.011718598194420338,0.013646772131323814,-0.013955263420939445,-0.018726898357272148,0.00032433870364911854,0.0113093676045537,-0.0024164298083633184,-0.03446529433131218,0.022237403318285942,0.012657240964472294,-0.010428274050354958,-0.019846037030220032,0.007717515807598829,-0.013605129905045033,-0.007871711626648903,-0.009255179204046726,-0.019679224118590355,0.005295383743941784,0.0433003194630146,-0.006524098105728626,0.008256874047219753,-0.02284810319542885,-0.0016141919186338782,0.008168344385921955,0.0037129013799130917,-0.00905547197908163,0.020942674949765205,0.012803932651877403,-0.012481754645705223,0.007556249387562275,-0.01634473353624344,-0.025159381330013275,0.0042537483386695385,0.015010692179203033,-0.011348556727170944,0.006745616439729929,-0.0098818838596344,0.005981479771435261,-0.03251617029309273,-0.014245073311030865,-0.02311360277235508,-0.0021543754264712334,0.009257941506803036,-0.009089600294828415,-0.011834065429866314,0.007064307574182749,0.0032996118534356356,-0.0008011194295249879,0.01145108975470066,0.003324124263599515,0.02881145291030407,-0.0024975722189992666,-0.01667027547955513,0.009554951451718807,0.020360248163342476,-0.010391167365014553,0.004245213232934475,-0.0007781177409924567,-0.05125214532017708,-0.008404150605201721,0.01213482953608036,-0.0050893924199044704,0.009313559159636497,0.0053646257147192955,0.011474878527224064,-0.0008017529617063701,-0.009921078570187092,0.03449544683098793,0.015229103155434132,-0.021450307220220566,0.012890445068478584,0.007074544206261635,-0.020075742155313492,0.013655543327331543,0.01741895079612732,-0.004973707254976034,0.02095552533864975,-0.014954539947211742,0.014467093162238598,-0.013751419261097908,-0.030736714601516724,-0.009710988961160183,-0.019800202921032906,-0.0046396139077842236,0.00023780656920280308,-0.014663486741483212,-0.0021168107632547617,-0.004936226177960634,-0.007233834825456142,-0.005488882306963205,-0.021071644499897957,0.01381067093461752,0.004892517346888781,-0.02362489141523838,0.014847244136035442,-0.00909944623708725,0.019386958330869675,-0.0030750110745429993,0.008438533172011375,0.009922204539179802,-0.004883875139057636,-0.0036598676815629005,0.02118445746600628,-0.01838010735809803,-0.018696743994951248,-0.007537576369941235,0.016164371743798256,-0.03305917605757713,0.0005603332538157701,0.00932055339217186,0.02531415782868862,0.005125220865011215,-0.00554606132209301,0.006280189845710993,-0.0009959483286365867,0.014583893120288849,0.016103897243738174,0.0009810097981244326,-0.017790311947464943,-0.020640121772885323,-0.017981499433517456,0.005753315985202789,-0.0013148117577657104,0.004606636241078377,0.006201179698109627,0.006649303715676069,0.013460487127304077,-0.026445938274264336,-0.010844319127500057,-0.009137149900197983,0.028086889535188675,-0.004918960854411125,-0.0032793316058814526,0.006251661106944084,-0.010082616470754147,0.008801352232694626,-0.007146426010876894,-0.004663978237658739,-0.01802891306579113,0.0022065762896090746,0.01428560446947813,0.004570811986923218,0.004931652918457985,-0.02086901105940342,0.009543618187308311,0.08581499010324478,0.012612502090632915,0.02305315062403679,0.026148345321416855,0.008002262562513351,0.004048406146466732,0.012182526290416718,-0.013871530070900917,-0.016597049310803413,4.5473807404050604e-05,0.012153418734669685,0.005184385925531387,-0.006534124258905649,-0.0030282516963779926,0.007169988937675953,-0.01980271190404892,-0.006965450942516327,0.0075986324809491634,0.005081926938146353,-0.01692967675626278,0.008602196350693703,0.011258350685238838,-0.010680540464818478,-0.0017794461455196142,-0.004330210853368044,-0.007262008264660835,0.0026682752650231123,-0.00710982596501708,0.0011434294283390045,0.0022099672351032495,0.03826787695288658,0.015831103548407555,0.003228266490623355,0.018332092091441154,-0.01874593459069729,-0.02394809201359749,0.007230873219668865,0.03227466717362404,0.0015929511282593012,-0.02068265713751316,-0.03378134220838547,-0.00017661432502791286,0.0014394487952813506,0.0015611627604812384,0.006656401790678501,-0.006404683459550142,-0.03287048637866974,-0.004657869692891836,0.006408059038221836,-0.041348136961460114,-0.005214405711740255,-0.022597908973693848,-0.01064992230385542,-0.010430585592985153,-0.004606757778674364,0.01861616037786007,0.00669937115162611,0.018404165282845497,0.0037227158900350332,0.008285031653940678,3.100840331171639e-05,-0.019324298948049545,-0.00043250888120383024,0.0014138654805719852,0.019248783588409424,-0.005050864536315203,0.012297690846025944,-0.003052379935979843,0.007435885723680258,-0.004686394240707159,0.027255503460764885,0.010111062787473202,0.005987617652863264,-0.0016631424659863114,0.0031937367748469114,0.006557621993124485,0.015747075900435448,-0.016122207045555115,-0.0047066183760762215,0.004733636975288391,0.010700607672333717,-0.009579243138432503,0.009260253980755806,-0.013481328263878822,-0.007582461461424828,-0.00980858039110899,-0.015342101454734802,0.014872226864099503,-0.0031662755645811558,-0.01691250130534172,0.009117087349295616,0.014332630671560764,0.01600264571607113,-0.01224270835518837,-0.03270541504025459,0.013612885028123856,-0.007394764106720686,-0.007218893151730299,0.003938282374292612,-0.0028286073356866837,0.017450038343667984,-0.004264798015356064,-0.010483900085091591,0.034429822117090225,-0.00901692546904087,-0.007406158838421106,0.002717244904488325,-0.03962898254394531,-0.012029486708343029,-0.004122230689972639,-0.006991672795265913,-0.013701748102903366,-0.002496142638847232,-0.01397809386253357,0.008615984581410885,-0.004215772729367018,-0.0006992005510255694,0.002433133777230978,-0.003943947609513998,-0.016468491405248642,0.008220765739679337,0.006425956729799509,-0.010862470604479313,-0.0024589155800640583,0.0009945925557985902,0.0046652317978441715,0.0011812591692432761,0.01707892119884491,0.02498534694314003,0.015358447097241879,0.027273433282971382,0.009899435564875603,0.005616714712232351,0.008802240714430809,-0.039030201733112335,0.051672857254743576,-0.0021575409919023514,0.012390635907649994,0.017793113365769386,-0.004189814906567335,-0.023004909977316856,0.008257524110376835,-0.03099125251173973,0.013305402360856533,-0.002236671047285199,-0.00447149807587266,-0.0057481080293655396,-0.024661513045430183,-0.03118530474603176,-0.0009303752449341118,-0.005650755017995834,0.010456307791173458,0.003146994626149535,0.019204156473279,-0.008126182481646538,0.0008891363395377994,0.02560442127287388,-0.01729997619986534,0.013649332337081432,0.016048457473516464,-0.01372922956943512,-0.016634415835142136,-0.018399691209197044,-0.015555595979094505,0.0028536065947264433,-0.02753578871488571,-0.01139803510159254,0.02519478276371956,-0.008794066496193409,-0.004713755566626787,0.0048491377383470535,0.007238119374960661,-0.025174982845783234,0.01020644698292017,-0.004764513112604618,0.005558454897254705,0.009104003198444843,0.02444830723106861,-0.028046827763319016,0.013368632644414902,0.002922826912254095,-0.007801339495927095,0.003084993688389659,0.009808645583689213,0.013233336620032787,0.012932928279042244,0.02343352884054184,-0.01581677980720997,0.00803337525576353,0.003472201991826296,0.02906767465174198,-0.022237835451960564,0.006469537504017353,0.01739775948226452,0.029373837634921074,0.005835606250911951,0.0007726547191850841,0.004086678847670555,-0.03711147978901863,-0.012288104742765427,-0.01497059129178524,0.0050900825299322605,-0.00987050961703062,0.03279099240899086,0.023175276815891266,0.017704328522086143,0.00741499662399292,-0.006820612587034702,-0.021350977942347527,0.02134772017598152,0.01833236962556839,0.005528926849365234,0.015775758773088455,0.021345248445868492,-0.007135198917239904,0.01561056450009346,-0.02328290231525898,0.0026052596513181925,0.0077107441611588,0.024798741564154625,0.01203198079019785,0.002641471568495035,-0.01120197493582964,-0.000583906308747828,-0.01026695966720581,0.001559247961267829,-0.0029141863342374563,0.0038118960801512003,-0.01819843240082264,-0.013668405823409557,0.020887339487671852,-0.020734598860144615,-0.002390566049143672,-0.0026943080592900515,0.011238164268434048,0.008330532349646091,-0.031080709770321846,-0.025534145534038544,-0.007893185131251812,-0.002024939516559243,0.01001137588173151,-0.04562881961464882,0.0052123297937214375,0.003126719733700156,0.00229892460629344,-0.021226223558187485,0.015489201992750168,-0.01581657864153385,-0.011491421610116959,-0.02281554602086544,0.02445421926677227,0.002450759755447507,-0.012615693733096123,-0.017199682071805,-0.01105610653758049,-0.004449480213224888,0.0012648574775084853,-0.0249752476811409,0.0015160978073254228,-0.0020498952362686396,-0.009584492072463036,-0.019478419795632362,0.017848065122961998,-0.009092360734939575,0.0006192033761180937,0.013882906176149845,0.007056536618620157,-0.007163883186876774,0.0052700350061059,-0.021384011954069138,0.006743551231920719,0.0028741939458996058,0.02985280752182007,-0.021723417565226555,-0.00504518486559391,0.013218475505709648,0.013352740556001663,0.016106033697724342,-0.023733539506793022,0.013196500018239021,0.0028700982220470905,0.01939084753394127,-0.018450267612934113,-0.003628247883170843,0.021556325256824493,-0.012134106829762459,-0.025716155767440796,0.003885461948812008,0.01700337417423725,-0.00635384488850832,0.0051576211117208,0.011103905737400055,-0.02637443318963051,-0.03162521868944168,-0.008686657063663006,-0.004012381657958031,-0.004182631149888039,-0.04582998901605606,0.01357133686542511,0.004754442255944014,-0.008275180123746395,-0.00023990242334548384,0.03218153864145279,-0.0001964064285857603,-0.023628437891602516,-0.0014598681591451168,0.01776166260242462,0.0033520611468702555,0.006506633944809437,-0.033839840441942215,-0.025128977373242378,-0.007307081948965788,-0.0325266495347023,-0.0008990546921268106,-0.01198447123169899,-0.008654619567096233,0.012025673873722553,-0.01795128732919693,-0.009973404929041862,0.008537470363080502,0.01932443678379059,0.011022535152733326,0.0036080668214708567,-0.01870058663189411,0.1124279722571373,0.013862082734704018,-0.006832176353782415,0.004406678024679422,-0.023123912513256073,-0.008388274349272251,0.002132329624146223,-0.0065133944153785706,0.03710097447037697,-0.003343689488247037,0.006084071937948465,0.0013144022086635232,-0.007141769398003817,0.031647562980651855,0.010331149213016033,-0.010360649786889553,0.026319852098822594,-0.004002794157713652,-0.013862746767699718,-0.003759459126740694,0.027061041444540024,-0.0006820682319812477,-0.008716241456568241,0.026171507313847542,-0.0009496788843534887,-0.0050631193444132805,-0.015359025448560715,-0.013196943327784538,0.007656104862689972,-0.003976921550929546,-0.011549347080290318,0.01048405934125185,0.030894175171852112,0.003951659891754389,-0.009483568370342255,-0.02307324856519699,-0.013593600131571293,0.004435861483216286,-0.007706064265221357,-0.005477780010551214,0.009672516025602818,0.0061852033250033855,-0.0106190862134099,0.022242186591029167,-0.014531893655657768,-0.00048244133358821273,-0.007414901629090309,0.012956943362951279,0.026361454278230667,-0.04307142645120621,0.018067125231027603,-0.02195063792169094,-0.002424718579277396,0.009486628696322441,0.032761260867118835,-0.047356266528367996,0.023253872990608215,0.015541925095021725,0.02515709586441517,0.0018094759434461594,0.009140140376985073,0.018275069072842598,-0.014537543058395386,0.017389018088579178,0.011214564554393291]')) AS "score"
FROM "syllabus_embedding" AS "t1"
WHERE (("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (70573)))
ORDER BY (1 - ("t1"."embedding" <=>
               '[0.0386069230735302,0.02166755124926567,0.048902709037065506,0.0034858533181250095,0.00704862829297781,-0.0010990259470418096,0.004320225678384304,0.04677938297390938,0.026662252843379974,-0.004799572750926018,0.03923281654715538,-0.01827125810086727,-0.025349464267492294,-0.023746265098452568,0.05130176246166229,0.005095880012959242,-0.0072335912846028805,-0.001971007324755192,0.008957864716649055,0.0020190889481455088,0.028028983622789383,0.057091645896434784,-0.0006284898263402283,0.015445339493453503,-0.020323753356933594,0.020590530708432198,-0.026769518852233887,-0.017304837703704834,0.010145305655896664,-0.0038270745426416397,0.01836259290575981,-0.03264937922358513,0.029344718903303146,0.021151091903448105,-0.010249159298837185,0.007123075425624847,-0.0029904553666710854,0.014166801236569881,-0.004874055273830891,-0.040217090398073196,0.0025462035555392504,-0.025922227650880814,0.02112407051026821,0.018741454929113388,0.014829475432634354,0.023122534155845642,-0.038303885608911514,-0.01968010887503624,0.018504437059164047,0.024718152359128,-0.03243694826960564,0.0005678861052729189,0.0430091954767704,0.0261369775980711,-0.01350355427712202,0.003972569014877081,0.00849550124257803,0.02306334674358368,0.008791020140051842,0.0007980318041518331,-0.012893704697489738,-0.012110170908272266,0.050486013293266296,-0.019768385216593742,-0.006540406960994005,-0.002096942625939846,-0.014150530099868774,0.06220593675971031,0.008089561946690083,0.017328811809420586,0.0011016821954399347,0.011167556047439575,-0.026003718376159668,-0.007857079617679119,-0.007142112124711275,-0.005458062049001455,-0.034711163491010666,-0.01764395833015442,-0.003678068518638611,0.005467289127409458,-0.016702575609087944,0.022679166868329048,-0.015881499275565147,0.02817607671022415,-0.003450620686635375,-0.045308277010917664,-0.056002356112003326,-0.005340032745152712,-0.04796997457742691,-0.012140138074755669,-0.04221322387456894,0.03590407967567444,-0.02157311700284481,0.03919411078095436,0.00905209593474865,-0.0018719978397712111,-0.007317259907722473,0.016128195449709892,0.005321172531694174,0.03503566235303879,0.02284722588956356,-0.029277091845870018,-0.009041843004524708,-0.02351694367825985,0.021159734576940536,0.026097038760781288,-0.03901059925556183,0.006631019525229931,0.009656640700995922,-0.013376389630138874,-0.059562087059020996,-0.005655491258949041,0.0016646423609927297,0.04489952325820923,-0.008418828248977661,-0.005787888541817665,-0.05822958052158356,0.0011227196082472801,-0.01326291635632515,0.01904989592730999,-0.03649580478668213,0.0053685493767261505,0.01065328810364008,-0.007042412180453539,0.021419312804937363,-0.00834533479064703,-0.019518861547112465,-0.0016218924429267645,-0.02914356254041195,-0.019466150552034378,0.02816060371696949,0.013981951400637627,0.02372073009610176,-0.012199481017887592,-0.02163497358560562,-0.00190679170191288,-0.025757892057299614,0.03218036890029907,-0.023178691044449806,-0.031324949115514755,0.025524746626615524,-0.00799864623695612,0.0042596799321472645,-0.008677249774336815,-0.024381518363952637,-0.016130203381180763,-0.008741904981434345,-0.04060044884681702,0.012727833352982998,0.003536957548931241,-0.005400105845183134,-0.01484607346355915,-0.01706600748002529,-0.026177704334259033,0.05305697023868561,-0.011185145936906338,-0.0014757663011550903,0.01048712432384491,-0.0031022680923342705,0.015025325119495392,-0.002005413407459855,0.0510031059384346,-0.022773796692490578,-0.00873856246471405,0.004294545855373144,-0.01435363944619894,-0.017757020890712738,-0.01614290103316307,-0.07354830950498581,-0.0034489419776946306,-0.007802875712513924,-0.023656822741031647,0.0004613243800122291,-0.04201408103108406,-0.015248844400048256,-0.013550324365496635,-0.009893535636365414,-0.027010714635252953,0.0036475113593041897,-0.047605618834495544,-0.0041426848620176315,0.04320656135678291,0.010265927761793137,0.004423090722411871,-0.05349121242761612,0.02764376439154148,0.007345958147197962,0.025353677570819855,0.03160092234611511,0.015356026589870453,0.02380317635834217,0.017112739384174347,0.03535217046737671,0.03507488593459129,-0.044955287128686905,-0.028536595404148102,-0.001942609203979373,0.04335802420973778,-0.03379026800394058,0.010357898660004139,0.001985562965273857,-0.016290731728076935,0.01506775338202715,0.012252533808350563,-0.0834619477391243,0.007713468745350838,-0.03795313090085983,0.008933000266551971,0.022020619362592697,-0.009437530301511288,0.03968397155404091,0.010948849841952324,0.008162695914506912,0.020955126732587814,-0.012745147570967674,-0.030160920694470406,-0.014937127009034157,0.039086271077394485,0.02640548162162304,0.03161340206861496,0.007453317753970623,0.008311322890222073,-0.0004270562494639307,0.023849548771977425,-0.012628764845430851,0.08219623565673828,-0.003342283656820655,0.014704377390444279,-0.0024249462876468897,-0.08470407128334045,0.015903735533356667,-0.009456152096390724,-0.017895391210913658,-0.019246781244874,0.01649809069931507,-0.0014268149388954043,0.028908416628837585,-0.014020439237356186,-0.002479419345036149,0.0016814921982586384,0.03153805807232857,0.047601357102394104,-0.002250116551294923,0.017433449625968933,0.015801770612597466,0.0038891835138201714,0.008212805725634098,-0.024682218208909035,0.009208827279508114,0.04768583923578262,0.03284638375043869,0.029118891805410385,0.0072805858217179775,-0.005235336255282164,-0.04139760136604309,-0.010849600657820702,-0.019693603739142418,0.022596945986151695,-0.005464608781039715,-0.009826017543673515,0.009157668799161911,0.005979442968964577,-0.017707476392388344,-0.020176663994789124,-0.010982709936797619,0.019582383334636688,0.03653104975819588,0.020248105749487877,0.006566138938069344,0.024262089282274246,-0.0034411344677209854,-0.0012760012177750468,-0.020982913672924042,0.004005402326583862,-0.00835233461111784,0.0021834245417267084,0.007222221232950687,0.018503660336136818,-0.009240057319402695,0.049725331366062164,0.04656681790947914,-0.005432190373539925,0.017567628994584084,0.006746649742126465,-0.011146938428282738,0.0036750261206179857,0.03553382679820061,-0.00012668888666667044,0.02253398858010769,0.0363534651696682,0.022741034626960754,-0.03922043368220329,0.01819590851664543,-0.026809196919202805,0.030529333278536797,0.008025551214814186,0.03848213702440262,-0.058085907250642776,-0.0042686136439442635,0.040645428001880646,0.025852203369140625,0.02403119020164013,-0.02711249701678753,-0.010039795190095901,0.01588689349591732,0.005499767139554024,0.004353005904704332,-0.04270293191075325,-0.014376320876181126,-0.019497357308864594,0.0004086093103978783,0.0031335337553173304,0.03494091331958771,-0.03284534439444542,0.005800729617476463,0.016891010105609894,0.025234146043658257,-0.022797150537371635,-0.013076884672045708,0.009013202041387558,0.016021618619561195,-0.0009283329709433019,-0.010889304801821709,-0.0036277880426496267,0.024584835395216942,-0.005735329817980528,0.011911028996109962,0.012346499599516392,0.013739186339080334,0.001029516919516027,0.0027366140857338905,-0.01943986304104328,0.00012684427201747894,-0.0007320527802221477,-0.014900530688464642,-0.0038770423270761967,0.016465287655591965,0.004415929317474365,-0.0049788025207817554,-0.03472616523504257,0.00019800248264800757,-0.015584557317197323,-0.013419357128441334,-0.01357216015458107,-0.013986151665449142,-0.0034045453649014235,-0.016251549124717712,0.03254702687263489,0.022456537932157516,0.049157485365867615,-0.01261075958609581,-0.028220858424901962,0.0322563536465168,-0.012451295740902424,0.013310784474015236,-0.007085527293384075,0.02195291966199875,-0.0287746861577034,-0.006262272596359253,0.029114019125699997,-0.05781135708093643,0.009916285052895546,0.02047852799296379,-0.015964223071932793,0.019113142043352127,0.03614174947142601,-0.0378374308347702,0.006291191093623638,-0.03420975059270859,-0.020727651193737984,0.03750180825591087,-0.006546027027070522,-0.02845233865082264,0.030448630452156067,-0.021558962762355804,-0.013035792857408524,0.050548069179058075,0.02522328495979309,0.016806267201900482,-0.017830612137913704,0.015941960737109184,-0.06322608888149261,-0.02108268067240715,-0.009504158049821854,0.014664177782833576,-0.0030298554338514805,0.023539630696177483,-0.009827349334955215,0.014492223039269447,0.015598982572555542,0.033105675131082535,-0.007946583442389965,0.0010603218106552958,-0.018684005364775658,-0.018870670348405838,-0.009595521725714207,0.016326745972037315,0.04608943685889244,0.051964081823825836,-0.038387738168239594,-0.03263930231332779,-0.023167047649621964,-0.018417757004499435,-0.014169088564813137,-0.019565636292099953,-0.008573019877076149,-0.03793003410100937,0.018865574151277542,-0.014316694810986519,-0.02483086846768856,0.018803857266902924,-0.05806541442871094,0.02822781912982464,-0.013456194661557674,0.046577583998441696,-0.03765052184462547,-0.014435652643442154,0.003885425627231598,-0.02130693569779396,0.024015463888645172,0.001039700349792838,-0.016290538012981415,-0.0038914289325475693,0.01097116805613041,-0.013688971288502216,-0.011349212378263474,0.00459138210862875,-0.01525054033845663,0.009331236593425274,-0.050670720636844635,-0.05796905979514122,0.001188463531434536,-0.007905072532594204,0.015542559325695038,-0.0038939258083701134,-0.013516315259039402,0.013466102071106434,-0.006840118672698736,-0.0018404395086690784,-0.02813754789531231,-0.03234042599797249,0.006501760799437761,-0.010472293943166733,-0.044854048639535904,-0.047655895352363586,0.0006714443443343043,0.012867451645433903,0.012643156573176384,-0.026564791798591614,-0.01636500284075737,-0.010767499916255474,0.005699263885617256,0.022320032119750977,0.0168963260948658,-0.04935983568429947,0.004654688760638237,0.03430552035570145,-0.0055297124199569225,-0.02945862151682377,0.020656654611229897,0.00041024028905667365,0.006718836724758148,0.0060719698667526245,0.036666031926870346,-0.022817857563495636,-0.026387328281998634,-0.052022796124219894,0.016412435099482536,-0.007759931031614542,-0.013346138410270214,0.018016859889030457,0.012891028076410294,0.003580209333449602,-0.006879810709506273,0.003962649032473564,-0.02168951742351055,0.005945843178778887,-0.05373867601156235,-0.017082810401916504,-0.03484218940138817,0.011005675420165062,0.005782996769994497,-0.0035614906810224056,8.499739487888291e-05,0.02233043871819973,0.08113518357276917,0.02930317260324955,0.008568982593715191,-0.04623707756400108,0.005501372739672661,0.013767601922154427,-0.005736441817134619,0.028351295739412308,-0.014695861376821995,-0.046657610684633255,-0.03686821460723877,0.00674577197059989,-0.01756899803876877,0.012065516784787178,-0.014470978640019894,0.01846669614315033,0.011544426903128624,-0.00027269512065686285,-0.006952269934117794,0.018334969878196716,0.009329087100923061,-0.05515529587864876,-0.03100619465112686,0.02024146541953087,0.03993987292051315,0.009668204002082348,0.0339549221098423,0.004952938761562109,-0.0288202166557312,-0.01361494604498148,-0.010115131735801697,-0.010414892807602882,-0.007561394013464451,-0.004087923560291529,-0.007103865034878254,0.030429046601057053,0.019701780751347542,-0.005007769912481308,-0.0046594031155109406,-0.010482512414455414,0.017034493386745453,-0.0038123312406241894,-0.07220666855573654,-0.03734322264790535,-0.006469706539064646,0.01171820517629385,-0.014774851500988007,-0.011510500684380531,-0.00019559494103305042,0.014172476716339588,0.0011777232866734266,0.01793101243674755,-0.01809772290289402,-0.019900374114513397,-0.009424149990081787,0.02528584934771061,-0.009296434931457043,-0.015842942520976067,-0.05282994359731674,0.011007032357156277,-0.030425462871789932,-0.007888597436249256,0.01779189147055149,-0.013956236653029919,0.010092280805110931,0.0014948486350476742,0.01801457442343235,0.010719564743340015,-0.0184786356985569,-0.017366906628012657,0.025188522413372993,-0.02166336216032505,-0.027736853808164597,0.019867928698658943,-0.005155580118298531,-0.009247295558452606,-0.014333361759781837,-0.007284186314791441,-0.03851496800780296,-0.030803129076957703,0.018458765000104904,0.01301299873739481,-0.01872858591377735,-0.03253243491053581,0.02897806279361248,-0.008805138058960438,-0.00498393876478076,-0.01781202293932438,0.029462657868862152,0.02006404846906662,-0.0066202739253640175,0.02866235189139843,-0.004800830036401749,0.010187774896621704,-0.005993695463985205,0.006322042550891638,-0.018528593704104424,-0.010510009713470936,0.004691600799560547,-0.03799901157617569,0.024336466565728188,-0.007018494885414839,0.019881054759025574,0.004318672697991133,-0.013955166563391685,-0.03290873393416405,-0.008184834383428097,-0.028209317475557327,0.0304915439337492,0.00289278756827116,0.024637708440423012,0.010634029284119606,0.03504426032304764,0.014212073758244514,0.023749301210045815,0.0016250356566160917,0.02427278831601143,-0.05244604870676994,-0.02148049883544445,-0.014187542721629143,0.011194570921361446,0.01172094326466322,-0.022753214463591576,0.008011423982679844,0.008351542055606842,-6.953438423806801e-05,0.006130187772214413,-0.0029027601704001427,-0.002412213245406747,-0.017494261264801025,0.01030539907515049,0.006225104443728924,0.009037470445036888,0.006048220209777355,-8.179667202057317e-05,0.006538806948810816,-0.0033549496438354254,0.02394680678844452,-0.03259764611721039,0.024286994710564613,0.049641381949186325,0.004372461698949337,-0.01333580445498228,0.03149427846074104,0.0067706648260355,0.014819646254181862,-0.02301754802465439,-0.016169577836990356,-0.04122626781463623,-0.031630054116249084,-0.01092920359224081,0.02150852233171463,-0.006270845420658588,0.0017857656348496675,-0.012421654537320137,0.02187402918934822,-0.004087433684617281,0.014636940322816372,-0.012351235374808311,-0.0031403189059346914,-0.004857945255935192,0.008570806123316288,0.011260350234806538,-0.021126562729477882,0.008264794014394283,-0.018981516361236572,-0.02412351407110691,0.005255460739135742,-0.0024232682771980762,0.037833597511053085,-0.0013062313664704561,-0.017682945355772972,0.0038645323365926743,0.005902625620365143,0.03167551010847092,-0.030770111829042435,0.0022039839532226324,0.0011463966220617294,-0.0002689253306016326,-0.01703200489282608,0.0018045752076432109,-0.042712096124887466,1.7108181054936722e-05,0.003912713378667831,0.015551258809864521,-0.04997490718960762,0.03737649694085121,0.009377804584801197,-0.01517079584300518,0.024601807817816734,-0.0033438755199313164,-0.015135759487748146,-0.033349283039569855,0.016177598387002945,0.02544727362692356,0.01641366258263588,0.010033599101006985,0.011622276157140732,-0.011523805558681488,-0.00024185435904655606,-0.030851038172841072,0.024358296766877174,-0.005884462967514992,0.0056336126290261745,0.019860683009028435,0.009112218394875526,0.026995331048965454,0.04186462238430977,-0.008384067565202713,-0.01279553771018982,-0.0064507401548326015,0.020475516095757484,-0.0019358735298737884,-0.0391533300280571,-0.09402865916490555,0.019530922174453735,0.019500967115163803,-0.013727768324315548,-0.02447306551039219,-0.03484143316745758,0.012342385947704315,-0.022378943860530853,-0.0005747226532548666,0.029152508825063705,-0.01169949397444725,0.002977904165163636,-0.01809113472700119,-0.01929587498307228,0.0064511485397815704,0.01070827804505825,0.04592396318912506,0.008517609909176826,-0.020693618804216385,0.030745111405849457,-0.022155500948429108,0.009302584454417229,0.001848826534114778,-0.01726374588906765,-0.032854098826646805,-0.004664392210543156,0.025480782613158226,-0.0003210848371963948,0.003198235295712948,0.007227455731481314,0.009427809156477451,0.007719670422375202,-0.005053460132330656,-0.002800591988489032,0.0035192465875297785,-0.030357837677001953,0.02228448912501335,-0.0008897955412976444,0.0036667264066636562,-0.019545797258615494,-0.006772436201572418,0.03614277020096779,-0.025196144357323647,-0.026344552636146545,0.0005104768788442016,0.006567667704075575,0.014157161116600037,-0.00108996476046741,0.03448469564318657,-0.017448369413614273,0.018307948485016823,-0.020939761772751808,0.009456406347453594,0.016362110152840614,0.004800904542207718,0.028609396889805794,-0.009695000946521759,-0.012513560242950916,0.015994664281606674,-0.008139253593981266,0.0006020397413522005,-0.016519855707883835,-0.0020134381484240294,-0.0056479619815945625,-0.01781047321856022,0.0202335212379694,0.021375250071287155,-0.03968917578458786,0.014133057557046413,-0.006673365365713835,-0.029552314430475235,0.004037435632199049,-0.0023860714863985777,0.011852583847939968,-0.0202122051268816,0.02376849576830864,-0.0082807382568717,0.004043182823807001,0.005917797796428204,-0.00012987590162083507,-0.0012120740721002221,-0.00853478629142046,-0.0029202729929238558,-0.015349346213042736,0.03353447467088699,-0.01988525502383709,-0.0028968441765755415,-0.0026071493048220873,0.002064598025754094,-0.023964285850524902,0.008827541954815388,-0.00039619632298126817,-0.0019376272102817893,0.0008516935049556196,0.016973448917269707,0.018535500392317772,0.010487367399036884,0.007731163874268532,-0.08438212424516678,-0.027918841689825058,0.022981248795986176,-0.018281547352671623,0.014274372719228268,0.0004872162244282663,0.0036148594226688147,0.018484199419617653,0.009355123154819012,-0.027392830699682236,0.029145365580916405,-0.008725486695766449,0.01659638062119484,-0.009670700877904892,0.01008589006960392,0.008347295224666595,-0.006970267277210951,0.007585667539387941,0.0028650742024183273,0.010891738347709179,-0.013063953258097172,0.008021541871130466,-0.01981830969452858,0.01871306635439396,-0.002211476443335414,-0.025785749778151512,-0.009031206369400024,0.004928593523800373,0.020901285111904144,-0.0452541708946228,0.012538204900920391,0.029332425445318222,4.644856016966514e-05,0.006867764517664909,0.005696096923202276,-0.01717127114534378,-0.03308325260877609,0.01041516661643982,-0.009026731364428997,0.019032523036003113,0.0008108243928290904,0.0007048777770251036,-0.002622335683554411,0.01929711550474167,-0.006632877979427576,-0.0034174970351159573,0.00934987235814333,-0.00300448015332222,0.030803795903921127,-0.008098017424345016,0.014003023505210876,-0.008849561214447021,0.03540365397930145,-0.011203928850591183,-0.01921825483441353,0.028163312003016472,0.011085916310548782,-0.010979264974594116,-0.00740286847576499,-0.018273664638400078,-0.013667674735188484,-0.0053319986909627914,0.021796036511659622,-0.012263570912182331,-0.03926483541727066,0.012089040130376816,-0.019171347841620445,-0.00279378448612988,-0.006041129119694233,0.009937881492078304,0.03648499399423599,0.02460109442472458,-0.009836703538894653,0.01737174019217491,0.02201392687857151,-0.020101947709918022,-0.005993856582790613,-0.014500179328024387,0.014078815467655659,-0.00449036993086338,0.02059549279510975,0.01274842768907547,-0.007803564891219139,0.00721731735393405,-0.019600708037614822,0.0025737497489899397,-0.018844526261091232,0.009988573379814625,-0.0032470659352838993,-0.0273904986679554,0.0177410077303648,-0.041983313858509064,0.0017952572088688612,0.018229329958558083,0.008994065225124359,0.006471517030149698,-0.01404568087309599,-0.03268956020474434,-0.03550786152482033,-0.008915429003536701,0.02857688069343567,0.03722824528813362,0.0016985356342047453,-0.017096010968089104,0.013663345016539097,0.017418205738067627,0.0018019434064626694,-0.03946677967905998,0.000495578336995095,0.019390836358070374,-0.027127886191010475,-0.01236709300428629,0.015604809857904911,-0.00018196622841060162,0.014336848631501198,0.030927414074540138,-0.011228542774915695,-0.0013114429311826825,-0.028674867004156113,-0.022508077323436737,0.0023239587899297476,0.003810793161392212,-0.027536770328879356,-0.0002607539645396173,-0.004626107402145863,0.012369506992399693,0.006689044181257486,0.013616613112390041,0.0011756496969610453,0.013891519047319889,0.014597613364458084,0.047587763518095016,-0.02158941887319088,-0.006722866091877222,-0.018235212191939354,0.02342039905488491,-0.0023563529830425978,0.005908353254199028,0.03410705178976059,-0.0023552386555820704,0.01617930456995964,0.03386703133583069,0.018248941749334335,0.0352577343583107,0.009382974356412888,-0.01665143296122551,-0.03993604704737663,0.0008163772872649133,-0.0007612033514305949,0.010664377361536026,0.020671004429459572,0.022764049470424652,0.00986841693520546,-0.011442516930401325,-0.028310580179095268,0.006413506343960762,-0.03165799006819725,-0.0036363934632390738,0.012463556602597237,0.01767897978425026,-0.00695110484957695,0.03562241047620773,-0.053316790610551834,-0.01578379236161709,-0.01941235549747944,0.010252708569169044,-0.016475338488817215,-0.0266090277582407,0.007005513180047274,-0.009999672882258892,-0.010776782408356667,0.012877075932919979,0.006127503700554371,0.011829588562250137,0.02072603814303875,-0.0022613678593188524,-0.0067197238095104694,-0.003383867908269167,0.01682727225124836,0.0069181895814836025,0.0025859561283141375,-0.012385080568492413,0.013763696886599064,0.018460456281900406,0.011728193610906601,0.0064268349669873714,0.01379615068435669,-0.005027300678193569,0.022922851145267487,0.0004455960006453097,0.006143066566437483,0.001711220946162939,-0.00010059395572170615,0.008864758536219597,0.002596171572804451,0.0030071090441197157,0.02492532879114151,0.008940503932535648,-0.0318266786634922,-0.019355671480298042,0.03567899763584137,-0.024371791630983353,0.02144045941531658,0.009920496493577957,0.022419949993491173,-0.011718598194420338,0.013646772131323814,-0.013955263420939445,-0.018726898357272148,0.00032433870364911854,0.0113093676045537,-0.0024164298083633184,-0.03446529433131218,0.022237403318285942,0.012657240964472294,-0.010428274050354958,-0.019846037030220032,0.007717515807598829,-0.013605129905045033,-0.007871711626648903,-0.009255179204046726,-0.019679224118590355,0.005295383743941784,0.0433003194630146,-0.006524098105728626,0.008256874047219753,-0.02284810319542885,-0.0016141919186338782,0.008168344385921955,0.0037129013799130917,-0.00905547197908163,0.020942674949765205,0.012803932651877403,-0.012481754645705223,0.007556249387562275,-0.01634473353624344,-0.025159381330013275,0.0042537483386695385,0.015010692179203033,-0.011348556727170944,0.006745616439729929,-0.0098818838596344,0.005981479771435261,-0.03251617029309273,-0.014245073311030865,-0.02311360277235508,-0.0021543754264712334,0.009257941506803036,-0.009089600294828415,-0.011834065429866314,0.007064307574182749,0.0032996118534356356,-0.0008011194295249879,0.01145108975470066,0.003324124263599515,0.02881145291030407,-0.0024975722189992666,-0.01667027547955513,0.009554951451718807,0.020360248163342476,-0.010391167365014553,0.004245213232934475,-0.0007781177409924567,-0.05125214532017708,-0.008404150605201721,0.01213482953608036,-0.0050893924199044704,0.009313559159636497,0.0053646257147192955,0.011474878527224064,-0.0008017529617063701,-0.009921078570187092,0.03449544683098793,0.015229103155434132,-0.021450307220220566,0.012890445068478584,0.007074544206261635,-0.020075742155313492,0.013655543327331543,0.01741895079612732,-0.004973707254976034,0.02095552533864975,-0.014954539947211742,0.014467093162238598,-0.013751419261097908,-0.030736714601516724,-0.009710988961160183,-0.019800202921032906,-0.0046396139077842236,0.00023780656920280308,-0.014663486741483212,-0.0021168107632547617,-0.004936226177960634,-0.007233834825456142,-0.005488882306963205,-0.021071644499897957,0.01381067093461752,0.004892517346888781,-0.02362489141523838,0.014847244136035442,-0.00909944623708725,0.019386958330869675,-0.0030750110745429993,0.008438533172011375,0.009922204539179802,-0.004883875139057636,-0.0036598676815629005,0.02118445746600628,-0.01838010735809803,-0.018696743994951248,-0.007537576369941235,0.016164371743798256,-0.03305917605757713,0.0005603332538157701,0.00932055339217186,0.02531415782868862,0.005125220865011215,-0.00554606132209301,0.006280189845710993,-0.0009959483286365867,0.014583893120288849,0.016103897243738174,0.0009810097981244326,-0.017790311947464943,-0.020640121772885323,-0.017981499433517456,0.005753315985202789,-0.0013148117577657104,0.004606636241078377,0.006201179698109627,0.006649303715676069,0.013460487127304077,-0.026445938274264336,-0.010844319127500057,-0.009137149900197983,0.028086889535188675,-0.004918960854411125,-0.0032793316058814526,0.006251661106944084,-0.010082616470754147,0.008801352232694626,-0.007146426010876894,-0.004663978237658739,-0.01802891306579113,0.0022065762896090746,0.01428560446947813,0.004570811986923218,0.004931652918457985,-0.02086901105940342,0.009543618187308311,0.08581499010324478,0.012612502090632915,0.02305315062403679,0.026148345321416855,0.008002262562513351,0.004048406146466732,0.012182526290416718,-0.013871530070900917,-0.016597049310803413,4.5473807404050604e-05,0.012153418734669685,0.005184385925531387,-0.006534124258905649,-0.0030282516963779926,0.007169988937675953,-0.01980271190404892,-0.006965450942516327,0.0075986324809491634,0.005081926938146353,-0.01692967675626278,0.008602196350693703,0.011258350685238838,-0.010680540464818478,-0.0017794461455196142,-0.004330210853368044,-0.007262008264660835,0.0026682752650231123,-0.00710982596501708,0.0011434294283390045,0.0022099672351032495,0.03826787695288658,0.015831103548407555,0.003228266490623355,0.018332092091441154,-0.01874593459069729,-0.02394809201359749,0.007230873219668865,0.03227466717362404,0.0015929511282593012,-0.02068265713751316,-0.03378134220838547,-0.00017661432502791286,0.0014394487952813506,0.0015611627604812384,0.006656401790678501,-0.006404683459550142,-0.03287048637866974,-0.004657869692891836,0.006408059038221836,-0.041348136961460114,-0.005214405711740255,-0.022597908973693848,-0.01064992230385542,-0.010430585592985153,-0.004606757778674364,0.01861616037786007,0.00669937115162611,0.018404165282845497,0.0037227158900350332,0.008285031653940678,3.100840331171639e-05,-0.019324298948049545,-0.00043250888120383024,0.0014138654805719852,0.019248783588409424,-0.005050864536315203,0.012297690846025944,-0.003052379935979843,0.007435885723680258,-0.004686394240707159,0.027255503460764885,0.010111062787473202,0.005987617652863264,-0.0016631424659863114,0.0031937367748469114,0.006557621993124485,0.015747075900435448,-0.016122207045555115,-0.0047066183760762215,0.004733636975288391,0.010700607672333717,-0.009579243138432503,0.009260253980755806,-0.013481328263878822,-0.007582461461424828,-0.00980858039110899,-0.015342101454734802,0.014872226864099503,-0.0031662755645811558,-0.01691250130534172,0.009117087349295616,0.014332630671560764,0.01600264571607113,-0.01224270835518837,-0.03270541504025459,0.013612885028123856,-0.007394764106720686,-0.007218893151730299,0.003938282374292612,-0.0028286073356866837,0.017450038343667984,-0.004264798015356064,-0.010483900085091591,0.034429822117090225,-0.00901692546904087,-0.007406158838421106,0.002717244904488325,-0.03962898254394531,-0.012029486708343029,-0.004122230689972639,-0.006991672795265913,-0.013701748102903366,-0.002496142638847232,-0.01397809386253357,0.008615984581410885,-0.004215772729367018,-0.0006992005510255694,0.002433133777230978,-0.003943947609513998,-0.016468491405248642,0.008220765739679337,0.006425956729799509,-0.010862470604479313,-0.0024589155800640583,0.0009945925557985902,0.0046652317978441715,0.0011812591692432761,0.01707892119884491,0.02498534694314003,0.015358447097241879,0.027273433282971382,0.009899435564875603,0.005616714712232351,0.008802240714430809,-0.039030201733112335,0.051672857254743576,-0.0021575409919023514,0.012390635907649994,0.017793113365769386,-0.004189814906567335,-0.023004909977316856,0.008257524110376835,-0.03099125251173973,0.013305402360856533,-0.002236671047285199,-0.00447149807587266,-0.0057481080293655396,-0.024661513045430183,-0.03118530474603176,-0.0009303752449341118,-0.005650755017995834,0.010456307791173458,0.003146994626149535,0.019204156473279,-0.008126182481646538,0.0008891363395377994,0.02560442127287388,-0.01729997619986534,0.013649332337081432,0.016048457473516464,-0.01372922956943512,-0.016634415835142136,-0.018399691209197044,-0.015555595979094505,0.0028536065947264433,-0.02753578871488571,-0.01139803510159254,0.02519478276371956,-0.008794066496193409,-0.004713755566626787,0.0048491377383470535,0.007238119374960661,-0.025174982845783234,0.01020644698292017,-0.004764513112604618,0.005558454897254705,0.009104003198444843,0.02444830723106861,-0.028046827763319016,0.013368632644414902,0.002922826912254095,-0.007801339495927095,0.003084993688389659,0.009808645583689213,0.013233336620032787,0.012932928279042244,0.02343352884054184,-0.01581677980720997,0.00803337525576353,0.003472201991826296,0.02906767465174198,-0.022237835451960564,0.006469537504017353,0.01739775948226452,0.029373837634921074,0.005835606250911951,0.0007726547191850841,0.004086678847670555,-0.03711147978901863,-0.012288104742765427,-0.01497059129178524,0.0050900825299322605,-0.00987050961703062,0.03279099240899086,0.023175276815891266,0.017704328522086143,0.00741499662399292,-0.006820612587034702,-0.021350977942347527,0.02134772017598152,0.01833236962556839,0.005528926849365234,0.015775758773088455,0.021345248445868492,-0.007135198917239904,0.01561056450009346,-0.02328290231525898,0.0026052596513181925,0.0077107441611588,0.024798741564154625,0.01203198079019785,0.002641471568495035,-0.01120197493582964,-0.000583906308747828,-0.01026695966720581,0.001559247961267829,-0.0029141863342374563,0.0038118960801512003,-0.01819843240082264,-0.013668405823409557,0.020887339487671852,-0.020734598860144615,-0.002390566049143672,-0.0026943080592900515,0.011238164268434048,0.008330532349646091,-0.031080709770321846,-0.025534145534038544,-0.007893185131251812,-0.002024939516559243,0.01001137588173151,-0.04562881961464882,0.0052123297937214375,0.003126719733700156,0.00229892460629344,-0.021226223558187485,0.015489201992750168,-0.01581657864153385,-0.011491421610116959,-0.02281554602086544,0.02445421926677227,0.002450759755447507,-0.012615693733096123,-0.017199682071805,-0.01105610653758049,-0.004449480213224888,0.0012648574775084853,-0.0249752476811409,0.0015160978073254228,-0.0020498952362686396,-0.009584492072463036,-0.019478419795632362,0.017848065122961998,-0.009092360734939575,0.0006192033761180937,0.013882906176149845,0.007056536618620157,-0.007163883186876774,0.0052700350061059,-0.021384011954069138,0.006743551231920719,0.0028741939458996058,0.02985280752182007,-0.021723417565226555,-0.00504518486559391,0.013218475505709648,0.013352740556001663,0.016106033697724342,-0.023733539506793022,0.013196500018239021,0.0028700982220470905,0.01939084753394127,-0.018450267612934113,-0.003628247883170843,0.021556325256824493,-0.012134106829762459,-0.025716155767440796,0.003885461948812008,0.01700337417423725,-0.00635384488850832,0.0051576211117208,0.011103905737400055,-0.02637443318963051,-0.03162521868944168,-0.008686657063663006,-0.004012381657958031,-0.004182631149888039,-0.04582998901605606,0.01357133686542511,0.004754442255944014,-0.008275180123746395,-0.00023990242334548384,0.03218153864145279,-0.0001964064285857603,-0.023628437891602516,-0.0014598681591451168,0.01776166260242462,0.0033520611468702555,0.006506633944809437,-0.033839840441942215,-0.025128977373242378,-0.007307081948965788,-0.0325266495347023,-0.0008990546921268106,-0.01198447123169899,-0.008654619567096233,0.012025673873722553,-0.01795128732919693,-0.009973404929041862,0.008537470363080502,0.01932443678379059,0.011022535152733326,0.0036080668214708567,-0.01870058663189411,0.1124279722571373,0.013862082734704018,-0.006832176353782415,0.004406678024679422,-0.023123912513256073,-0.008388274349272251,0.002132329624146223,-0.0065133944153785706,0.03710097447037697,-0.003343689488247037,0.006084071937948465,0.0013144022086635232,-0.007141769398003817,0.031647562980651855,0.010331149213016033,-0.010360649786889553,0.026319852098822594,-0.004002794157713652,-0.013862746767699718,-0.003759459126740694,0.027061041444540024,-0.0006820682319812477,-0.008716241456568241,0.026171507313847542,-0.0009496788843534887,-0.0050631193444132805,-0.015359025448560715,-0.013196943327784538,0.007656104862689972,-0.003976921550929546,-0.011549347080290318,0.01048405934125185,0.030894175171852112,0.003951659891754389,-0.009483568370342255,-0.02307324856519699,-0.013593600131571293,0.004435861483216286,-0.007706064265221357,-0.005477780010551214,0.009672516025602818,0.0061852033250033855,-0.0106190862134099,0.022242186591029167,-0.014531893655657768,-0.00048244133358821273,-0.007414901629090309,0.012956943362951279,0.026361454278230667,-0.04307142645120621,0.018067125231027603,-0.02195063792169094,-0.002424718579277396,0.009486628696322441,0.032761260867118835,-0.047356266528367996,0.023253872990608215,0.015541925095021725,0.02515709586441517,0.0018094759434461594,0.009140140376985073,0.018275069072842598,-0.014537543058395386,0.017389018088579178,0.011214564554393291]'))
    DESC
LIMIT 20


SELECT "t1"."data", "t2"."fid", "t3"."stock_code", "t3"."report_year"
FROM "answer" AS "t1"
         JOIN "question" AS "t2" ON ("t1"."qid" = "t2"."id")
         JOIN "hkex_file_meta" AS "t3" ON ("t3"."fid" = "t2"."fid")
WHERE (((("t1"."deleted_utc" = 0) AND ("t2"."deleted_utc" = 0)) AND ("t3"."deleted_utc" = 0)) AND ("t2"."mold" = 18))


-- 查询文件连接
SELECT qa.fid,
       qa.field,
       replace(replace(ua.value, '["', ''), '"]', '') as                           user_value,
       qa.value                                       as                           predict_value,
--        concat('http://localhost:9003/#/project/remark/', qa.id, '?treeId=', file.tree_id, '&fileId=', qa.fid, '&schemaId=', qa.mold, '&projectId=', file.pid, '&schemaKey=', substr(qa.field, 0, length(qa.field)-1 )) local_url,
       concat('[file=', qa.fid, ', stock_code=', fm.stock_code,
              '](https://scriber-jura4.test.paodingai.com/#/project/remark/', qa.id, '?treeId=', file.tree_id,
              '&fileId=', qa.fid, '&schemaId=', qa.mold, '&projectId=', file.pid, '&schemaKey=',
              replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20'), ')') url
FROM (SELECT id,
             fid,
             mold,
             (item ->> 'key')::jsonb ->> 1 as field,
             (item ->> 'key')::jsonb ->> 2 as words,
             item ->> 'value'              AS value
      FROM (SELECT id, fid, mold, item
            FROM question,
                 json_array_elements(preset_answer -> 'userAnswer' -> 'items') item
            WHERE mold = 18
              AND deleted_utc = 0) q -- AND (item ->> 'key') LIKE '%Beginning amount%'
     ) qa
         JOIN (SELECT qid,
                      (item ->> 'key')::jsonb ->> 1 as field,
                      (item ->> 'key')::jsonb ->> 2 as words,
                      item,
                      item ->> 'value'              AS value
--      FROM (SELECT qid, JSONB_ARRAY_ELEMENTS((data -> 'userAnswer' -> 'items')::jsonb) AS item FROM answer) a
               FROM (SELECT qid, item
                     from answer,
                          json_array_elements(data -> 'userAnswer' -> 'items') item
                     WHERE deleted_utc = 0) a -- AND (item ->> 'key') LIKE '%Beginning amount%'
) ua
              ON qa.id = ua.qid AND (qa.field = ua.field AND qa.words = ua.words)
         JOIN file ON file.id = qa.fid
         JOIN hkex_file_meta fm ON fm.fid = qa.fid
WHERE qa.field LIKE 'C1:%'
  AND replace(replace(ua.value, '["', ''), '"]', '') = 'Positive Statement'
ORDER BY fid

select fid
from hkex_file_meta h
         join agm_meta a on h.fid = a.agm_fid
where h.doc_type = 30
  and a.convening_date is null

select agm_meta.agm_fid
from agm_meta
where convening_date is null

SELECT "t1"."id",
       "t1"."main_rule",
       "t1"."gem_rule",
       "t1"."rule",
       "t1"."rule_description",
       "t1"."batch",
       "t1"."order",
       "t1"."gem_description",
       "t1"."activated_at",
       "t1"."main_alias",
       "t1"."gem_alias",
       "t1"."operation",
       "t1"."created_utc",
       "t1"."updated_utc",
       "t1"."deleted_utc"
FROM "rule_reference" AS "t1"
WHERE (("t1"."deleted_utc" < 2) AND ("t1"."batch" = 'jura 6 policy esg'))
ORDER BY "t1"."order"

select *
from rule_reference
where rule like '%E2%'

select json_array_elements((data - > 'schemas')::json -> 0 -> 'orders') #>> '{}' as rule,
       id                                                                           "
            "
from mold
where id in (2, 32) E4-Independent Assurance
on scope 1 and scope 2 GHG emissions
    E4-Independent Assurance on scope 1 and scope 2 GHG emission
    E9- Scope 3 emissions data by categories
    E9- Scope 3 emission data by categories


SELECT "t1"."doc_type",
       "t2"."activated",
       "t1"."name",
       "t1"."published",
       "t1"."report_year",
       "t1"."stock_code",
       "t1"."year_end",
       "t4"."fid"
FROM "question" AS "t4"
         INNER JOIN "hkex_file_meta" AS "t1" ON ("t4"."fid" = "t1"."fid")
         LEFT OUTER JOIN "file_esg_xref" AS "t2" ON ("t4"."fid" = "t2"."fid")
WHERE (((("t4"."deleted_utc" = 0) AND ("t1"."deleted_utc" = 0))) AND ("t4"."id" = 306238))
WITH "tmp_ans" AS (SELECT "t1"."id",
                          RANK() OVER (PARTITION BY "t1"."rule", "t1"."fid" ORDER BY "t1"."uid" DESC) AS "rank"
                   FROM "esg_result" AS "t1"
                            INNER JOIN "file_esg_xref" AS "t2" ON ("t2"."fid" = "t1"."fid")
                            INNER JOIN "hkex_file_meta" AS "t3" ON ("t1"."fid" = "t3"."fid")
                   WHERE ((("t2"."activated" AND ("t1"."uid" >= 0)) AND ("t3"."stock_code" = '00280')) AND
                          (("t3"."report_year" <= '2024') AND ("t3"."report_year" >= '2024'))))
SELECT "t4"."rule", "t5"."report_year", "t4"."enum_value", "t5"."fid"
FROM "esg_result" AS "t4"
         INNER JOIN "hkex_file_meta" AS "t5" ON ("t4"."fid" = "t5"."fid")
         INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t4"."id")
WHERE ((("t5"."stock_code" = '00280') AND ("tmp_ans"."rank" = 1)) AND ("t4"."enum_value" IS NOT NULL))


select h.fid, h.stock_code, h.report_year, h.doc_type
from hkex_file_meta h
         join file_esg_xref x on h.fid = x.fid
where h.stock_code in
      ('00002', '00003', '00004', '00006', '00008', '00010', '00012', '00014', '00016', '00018', '00019', '00023',
       '00038', '00040', '00051', '00066', '00069', '00083', '00101', '01090')
  and h.report_year in ('2023', '2024')
  and x.activated \COPY (SELECT m.stock_code, a.event_id, a.report_dates
FROM addition_data a
         JOIN hkex_file_meta m ON (a.stock_code = m.stock_code)
WHERE m.doc_type = 1
  and m.deleted_utc = 0
  AND a.last_report_date >= '2024-09-30'
GROUP BY m.stock_code, a.event_id, a.report_dates) TO '/opt/scriber/output.csv'
WITH (FORMAT CSV, HEADER);

select rulereference_id, count(*)
from rule_group_rule_reference_throughz
group by rulereference_id



SELECT "t1"."id",
       "t1"."main_rule",
       "t1"."gem_rule",
       "t1"."rule",
       "t1"."rule_description",
       "t1"."batch",
       "t1"."order",
       "t1"."gem_description",
       "t1"."activated_at",
       "t1"."main_alias",
       "t1"."gem_alias",
       "t1"."operation",
       "t1"."created_utc",
       "t1"."updated_utc",
       "t1"."deleted_utc"
FROM "rule_reference" AS "t1"
WHERE (("t1"."deleted_utc" = 0) AND ("t1"."id" = 141))
LIMIT 1 OFFSET 0

SELECT "t1"."id",
       "t1"."name",
       "t1"."description",
       "t1"."parent_id",
       "t1"."user_id",
       "t1"."level",
       "t1"."is_enabled",
       "t1"."created_utc",
       "t1"."updated_utc",
       "t1"."deleted_utc",
       "t1"."order"
FROM "rule_group" AS "t1"
         INNER JOIN "rule_group_rule_reference_through" AS "t2" ON ("t1"."id" = "t2"."rulegroup_id")
         INNER JOIN "rule_reference" AS "t3" ON ("t3"."id" = "t2"."rulereference_id")
         INNER JOIN "rule_group" AS "dad" ON ("dad"."id" = "t1"."parent_id")
         INNER JOIN "rule_group" AS "grandpa" ON ("dad"."parent_id" = "grandpa"."id")
WHERE ((((("t1"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("dad"."deleted_utc" = 0)) AND
        ("grandpa"."deleted_utc" = 0)) AND
       (("t1"."level" = 2), ("grandpa"."name" = 'Annual Report'), ("dad"."name" = 'All'), ("dad"."level" = 1),
        ("grandpa"."level" = 0), ("t2"."rulereference_id" = 447)))
ORDER BY "t1"."id";



SELECT "t1"."id"
FROM "question" AS "t1"
         INNER JOIN "file" AS "t2" ON ("t2"."id" = "t1"."fid")
         INNER JOIN "hkex_file_meta" AS "t3" ON ("t2"."id" = "t3"."fid")
WHERE (((("t1"."deleted_utc" = 0) AND ("t2"."deleted_utc" = 0)) AND ("t3"."deleted_utc" = 0)) AND
       (("t2"."deleted_utc" = 0) AND ("t3"."doc_type" = 31)))
ORDER BY "t2"."id" desc

-- \COPY
-- (select id from file where id in (104190,104170,104167,104201,71298,71299,71321,71312,71292,71294,71297,71319,71340,71350,71361,71368,71372,71376,71377,71379,70983,71381,71383,71384,71385,71339,71349,71352,71353,71355,71358,71360,71362,71370,71373,71378,71369,71374,71375,71380,71382,70973,70539,71386,71388,71390,71395,71391,71393,71296,71410,71414,71394,71396,71401,71403,71407,71413,71415,71416,71397,71400,71402,71405,71406,71419,71443,71398,71399,71404,71408,71412,71444,71447,71451,70527,71513,71301,71417,71420,71423,71427,71430,71434,71435,71437,71441,71445,71448,71516,71519,71524,71421,71424,71426,71429,71432,71433,71438,71439,71442,71324,71525,71527,71425,71428,71431,71559,70898,71323,71436,71440,71446,71449,71452,71453,71456,71454,71457,71460,71463,71466,71455,71458,71313,71459,71461,71462,71467,71470,71477,71480,71482,71492,71468,71471,71469,71472,71475,71478,71481,71483,71484,71485,71486,71494,71489,71490,71493,71500,71502,70709,71506,71508,71510,71511,71473,71474,71476,70543,71487,71488,71496,71495,71499,71501,71504,71507,71514,71517,70597,71557,71560,71572,71577,71553,71554,71555,71556,71562,71565,71571,71575,71581,71561,70564,70544,71566,71570,71573,71574,71308,71580,71582,71578,70525,71310,71316,71326,71311))
-- TO '~/output.csv'
-- WITH (FORMAT CSV, HEADER);

SELECT "t1"."file_id", "t1"."index", "t1"."position", "t1"."text", "t1"."metadata"
FROM "embedding" AS "t1"
         INNER JOIN (SELECT "t1"."index"
                     FROM "embedding" AS "t1"
                     WHERE ((("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71587))) AND
                            ("t1"."text" ~* '\ydecreasing\y'))
                     GROUP BY "t1"."index") AS "subquery"
                    ON (("t1"."index" = "subquery"."index") AND ("t1"."text" ~* '\ydecreasing\y'))
WHERE (("t1"."deleted_utc" = 0) AND ("t1"."file_id" IN (71587)))
ORDER BY "t1"."id" DESC
LIMIT 20


select concat('https://jura.paodingai.com/#/hkex/result-announcement/report-review/', id, '?fileId=', fid,
              '&schemaId=32&rule=&ratio=ratio1') url
from question
where id in
      (298202, 298199, 298208, 298198, 298210, 298212, 298219, 298204, 298228, 298216, 298232, 298230, 298236, 298234,
       298224, 298226);


SELECT hk.stock_code,
       hk.report_year,
       qa.fid,
       concat('https://jura6-lir.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?treeId=',
              file.tree_id, '&fileId=', qa.fid,
              '&schemaId=1&rule=E1-Reference%20to%20ISSB%20Standards') local_url

from question qa
         join file on file.id = qa.fid
         join hkex_file_meta hk on qa.fid = hk.fid
where qa.fid in
      (103409, 103418, 103421, 103420, 103407, 103417, 71290, 71289, 71288, 103406, 103416, 103411, 103419, 71544,
       103405, 71587, 103408, 103415, 103410, 71374, 104190, 104170, 104167, 104201, 71298, 71299, 71321, 71312, 71292,
       71294, 71297, 71319, 71340, 71350, 71361, 71368, 71372, 71376, 71377, 71379, 70983, 71381, 71383, 71384, 71385,
       71339, 71349, 71352, 71353, 71355, 71358, 71360, 71362, 71370, 71373, 71378, 71369, 71374, 71375, 71380, 71382,
       70973, 70539, 71386, 71388, 71390, 71395, 71391, 71393, 71296, 71410, 71414, 71394, 71396, 71401, 71403, 71407,
       71413, 71415, 71416, 71397, 71400, 71402, 71405, 71406, 71419, 71443, 71398, 71399, 71404, 71408, 71412, 71444,
       71447, 71451, 70527, 71513, 71301, 71417, 71420, 71423, 71427, 71430, 71434, 71435, 71437, 71441, 71445, 71448,
       71516, 71519, 71524, 71421, 71424, 71426, 71429, 71432, 71433, 71438, 71439, 71442, 71324, 71525, 71527, 71425,
       71428, 71431, 71559, 70898, 71323, 71436, 71440, 71446, 71449, 71452, 71453, 71456, 71454, 71457, 71460, 71463,
       71466, 71455, 71458, 71313, 71459, 71461, 71462, 71467, 71470, 71477, 71480, 71482, 71492, 71468, 71471, 71469,
       71472, 71475, 71478, 71481, 71483, 71484, 71485, 71486, 71494, 71489, 71490, 71493, 71500, 71502, 70709, 71506,
       71508, 71510, 71511, 71473, 71474, 71476, 70543, 71487, 71488, 71496, 71495, 71499, 71501, 71504, 71507, 71514,
       71517, 70597, 71557, 71560, 71572, 71577, 71553, 71554, 71555, 71556, 71562, 71565, 71571, 71575, 71581, 71561,
       70564, 70544, 71566, 71570, 71573, 71574, 71308, 71580, 71582, 71578, 70525, 71310, 71316, 71326, 71311)
  and qa.mold = 1;

SELECT hk.stock_code,
       hk.report_year,
       qa.fid,
       concat('https://jura6-lir.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?treeId=',
              file.tree_id, '&fileId=', qa.fid,
              '&schemaId=2&rule=E1-Reference%20to%20ISSB%20Standards') local_url

from question qa
         join file on file.id = qa.fid
         join hkex_file_meta hk on qa.fid = hk.fid
where qa.fid in
      (103409, 103418, 103421, 103420, 103407, 103417, 71290, 71289, 71288, 103406, 103416, 103411, 103419, 71544,
       103405, 71587, 103408, 103415, 103410, 71374, 104190, 104170, 104167, 104201, 71298, 71299, 71321, 71312, 71292,
       71294, 71297, 71319, 71340, 71350, 71361, 71368, 71372, 71376, 71377, 71379, 70983, 71381, 71383, 71384, 71385,
       71339, 71349, 71352, 71353, 71355, 71358, 71360, 71362, 71370, 71373, 71378, 71369, 71374, 71375, 71380, 71382,
       70973, 70539, 71386, 71388, 71390, 71395, 71391, 71393, 71296, 71410, 71414, 71394, 71396, 71401, 71403, 71407,
       71413, 71415, 71416, 71397, 71400, 71402, 71405, 71406, 71419, 71443, 71398, 71399, 71404, 71408, 71412, 71444,
       71447, 71451, 70527, 71513, 71301, 71417, 71420, 71423, 71427, 71430, 71434, 71435, 71437, 71441, 71445, 71448,
       71516, 71519, 71524, 71421, 71424, 71426, 71429, 71432, 71433, 71438, 71439, 71442, 71324, 71525, 71527, 71425,
       71428, 71431, 71559, 70898, 71323, 71436, 71440, 71446, 71449, 71452, 71453, 71456, 71454, 71457, 71460, 71463,
       71466, 71455, 71458, 71313, 71459, 71461, 71462, 71467, 71470, 71477, 71480, 71482, 71492, 71468, 71471, 71469,
       71472, 71475, 71478, 71481, 71483, 71484, 71485, 71486, 71494, 71489, 71490, 71493, 71500, 71502, 70709, 71506,
       71508, 71510, 71511, 71473, 71474, 71476, 70543, 71487, 71488, 71496, 71495, 71499, 71501, 71504, 71507, 71514,
       71517, 70597, 71557, 71560, 71572, 71577, 71553, 71554, 71555, 71556, 71562, 71565, 71571, 71575, 71581, 71561,
       70564, 70544, 71566, 71570, 71573, 71574, 71308, 71580, 71582, 71578, 70525, 71310, 71316, 71326, 71311)
  and qa.mold = 2;

-- https://jura6-lir.paodingai.com/#/hkex/esg-report-checking/report-review/307264?fileId=71586&schemaId=1&rule=E1-Reference%20to%20ISSB%20Standards&delist=0

-- http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/266467?fileId=70507&schemaId=18&rule=C1&delist=0
-- http://100.64.0.105:55647/#/project/remark/266467?treeId=16807&fileId=70507&schemaId=18&projectId=16807&schemaKey=C2

\COPY
(
SELECT hk.stock_code,
       hk.report_year,
       qa.fid,
       concat('http://100.64.0.105:55647/#/hkex/annual-report-checking/report-review/', qa.id, '?treeId=',
              file.tree_id, '&fileId=', qa.fid,
              '&schemaId=18&rule=C1')              compliance_url,
       concat('http://100.64.0.105:55647/#/project/remark/', qa.id, '?treeId=',
              file.tree_id, '&fileId=', qa.fid,
              '&schemaId=18&projectId=', file.pid) preset_url

from question qa
         join file on file.id = qa.fid
         join hkex_file_meta hk on qa.fid = hk.fid
where qa.fid in
      (70640, 70623, 70619, 70604, 70580, 70486, 70461, 70389, 70385, 70473, 70535, 70499, 70443, 70392, 70391, 70384,
       70294, 70288, 70851, 70631, 70540, 70518, 70507, 70496, 70497, 70467, 70453, 70435, 70356, 70950, 70405, 70991,
       70877, 70407, 70905, 70397, 70382, 70860, 70848, 70778, 70762, 70834, 70738, 70699, 70688, 71028, 71014, 71003,
       70980, 70413, 70907, 70741, 70730, 70339, 70767, 70658, 70708, 70524, 70433, 70556, 70325, 70530, 70772, 70788,
       70967, 70923, 70910, 70314, 70315, 70912, 70896, 70889, 70849, 70657, 70694, 70266, 70792, 70575, 70560, 70565,
       70689, 70241, 70441, 70670, 70805, 70876, 70867, 70884, 70725, 70709, 70706, 70597, 70344, 70334, 70322, 70784,
       70260, 70246, 70667, 70232, 70423, 70417, 70412, 70421, 70561, 70550, 70261, 70429, 70426)
  and qa.mold = 18 ) TO '~/output.csv'
WITH (FORMAT CSV, HEADER);


-- 查询Policy-ESG的链接
-- \COPY
-- (
SELECT qa.fid                                                                 as file_id,
       REGEXP_REPLACE(
               REGEXP_REPLACE(
                       REGEXP_REPLACE(qa.field, ':.*$', ''),
                       '^E(\d+)', 'T\1'
               ), '-', '')                                                    as rule,
       qa.value                                                               as predict_value,
       fm.stock_code                                                          as stock_code,
       fm.report_year                                                         as report_year,
       concat('https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid,
              '&schemaId=1', '&rule=',
              replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20')) as url
FROM (SELECT id,
             fid,
             mold,
             (item ->> 'key')::jsonb ->> 1 as field,
             (item ->> 'key')::jsonb ->> 2 as words,
             item ->> 'value'              AS value
      FROM (SELECT id, fid, mold, item
            FROM question,
                 json_array_elements(preset_answer -> 'userAnswer' -> 'items') item
            WHERE mold in (31, 32)) q -- AND (item ->> 'key') LIKE '%Beginning amount%'
     ) qa
         JOIN file ON file.id = qa.fid
         JOIN file_esg_xref ON file.id = file_esg_xref.fid
         JOIN hkex_file_meta fm ON fm.fid = qa.fid
where file_esg_xref.activated
  and fm.report_year in ('2023', '2024')
  and fm.stock_code in
      ('00016', '00017', '00040', '00066', '00083', '00147', '00202', '00247', '00280', '00341', '00411', '00559',
       '00659', '00723', '00825', '00900', '01676', '01752', '01903', '02421', '03626', '03998', '06808', '08320',
       '08487')
ORDER BY fm.fid
-- ) TO '~/policy-esg.csv' WITH (FORMAT CSV, HEADER);

SELECT qa.fid                                                                 as file_id,
       REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\d+)', 'T\1'), '-',
                      '')                                                     as rule,
       LOWER(CASE
                 WHEN qa.value LIKE '["_%"]' THEN REPLACE(REPLACE(qa.value, '["', ''), '"]', '')
                 ELSE qa.value END)                                           as predict_value,
       fm.stock_code                                                          as stock_code,
       fm.report_year                                                         as report_year,
       concat('https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid,
              '&schemaId=1', '&rule=',
              replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20')) as url
FROM (SELECT id,
             fid,
             mold,
             (item ->> 'key')::jsonb ->> 1 as field,
             (item ->> 'key')::jsonb ->> 2 as words,
             item ->> 'value'              AS value
      FROM (SELECT id, fid, mold, item
            FROM question,
                 json_array_elements(preset_answer -> 'userAnswer' -> 'items') item
            WHERE mold in (31, 32)
              AND deleted_utc = 0
              AND (item ->> 'key') LIKE '%E1%') q) qa
         JOIN file ON file.id = qa.fid
         JOIN file_esg_xref ON file.id = file_esg_xref.fid
         JOIN hkex_file_meta fm ON fm.fid = qa.fid
where file_esg_xref.activated
  and fm.report_year in ('2023', '2024')
ORDER BY fm.fid \COPY (SELECT qa.fid as file_id, REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\d+)', 'T\1'), '-', '') as rule, LOWER(CASE WHEN qa.value LIKE '[\"_%\"]' THEN REPLACE(REPLACE(qa.value, '[\"', ''), '\"]', '') ELSE qa.value END) as predict_value, fm.stock_code as stock_code, fm.report_year as report_year, concat('https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid, '&schemaId=1', '&rule=', replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20')) as url FROM (SELECT id, fid, mold, (item ->> 'key')::jsonb ->> 1 as field, (item ->> 'key')::jsonb ->> 2 as words, item ->> 'value' AS value FROM (SELECT id, fid, mold, item FROM question, json_array_elements(preset_answer -> 'userAnswer' -> 'items') item WHERE mold in (31, 32) AND deleted_utc = 0) q) qa JOIN file ON file.id = qa.fid JOIN file_esg_xref ON file.id = file_esg_xref.fid JOIN hkex_file_meta fm ON fm.fid = qa.fid where file_esg_xref.activated and fm.report_year in ('2023', '2024') ORDER BY fm.fid) TO '~/policy-esg.csv'
WITH (FORMAT CSV, HEADER)
\COPY (select * from addition_data) TO '~/addition_data.csv'
WITH (FORMAT CSV, HEADER);

\COPY
(SELECT qa.fid as file_id, REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\d+)', 'T\1'), '-', '') as rule, qa.value as predict_value, fm.stock_code as stock_code, fm.report_year as report_year, concat('http://100.64.0.105:55647/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid, '&schemaId=', REGEXP_REPLACE(qa.mold::text, '3', ''),'&rule=', replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20')) as url FROM (SELECT id, fid, mold, (item ->> 'key')::jsonb ->> 1 as field, (item ->> 'key')::jsonb ->> 2 as words, item ->> 'value' AS value FROM (SELECT id, fid, mold, item FROM question, json_array_elements(preset_answer -> 'userAnswer' -> 'items') item WHERE mold in (31, 32)) q /*AND (item ->> 'key') LIKE '%Beginning amount%'*/) qa JOIN file ON file.id = qa.fid JOIN file_esg_xref ON file.id = file_esg_xref.fid JOIN hkex_file_meta fm ON fm.fid = qa.fid where file_esg_xref.activated and fm.report_year in ('2023', '2024') and fm.stock_code in ('00016', '00017', '00040', '00066', '00083', '00147', '00202', '00247', '00280', '00341', '00411', '00559', '00659', '00723', '00825', '00900', '01676', '01752', '01903', '02421', '03626', '03998', '06808', '08320', '08487') ORDER BY fm.fid) TO '~/policy-esg.csv' WITH (FORMAT CSV, HEADER);


SELECT id,
       fid,
       mold,
       (item ->> 'key')::jsonb ->> 1 as field,
       (item ->> 'key')::jsonb ->> 2 as words,
       item ->> 'value'              AS value
FROM (SELECT id, fid, mold, item
      FROM question,
           json_array_elements(preset_answer -> 'userAnswer' -> 'items') item
      WHERE mold in (31, 32)
        and question.fid = 71587)


select file_id, index, text
from embedding
where text like '%not specified%'


SELECT "t1"."id",
       "t1"."fid",
       "t1"."qid",
       "t1"."stock_code",
       "t1"."report_year",
       "t1"."doc_type",
       "t1"."name",
       "t1"."published",
       "t1"."year_end",
       "t1"."stat_res",
       "t1"."deleted_utc"
FROM "hkex_file_meta" AS "t1"
WHERE (("t1"."deleted_utc" = 0) AND (("t1"."stock_code" = '02858') AND ("t1"."report_year" = '77887')))

SELECT "t1"."id"
FROM "question" AS "t1"
         INNER JOIN "file" AS "t2" ON ("t2"."id" = "t1"."fid")
         INNER JOIN "hkex_file_meta" AS "t3" ON ("t2"."id" = "t3"."fid")
WHERE (((((("t1"."deleted_utc" = 0) AND ("t2"."deleted_utc" = 0)) AND ("t3"."deleted_utc" = 0)) AND
         ("t2"."deleted_utc" = 0)) AND ("t1"."mold" IN (31, 32))) AND ("t1"."fid" = 71298))
ORDER BY "t2"."id" DESC WITH "tmp_union" AS (SELECT "t1"."id", "t1"."user_name", "t1"."action_time", "t1"."meta"
                     FROM "history" AS "t1"
                     WHERE ((("t1"."qid" = 307282) AND ("t1"."action" = 330009)) AND meta ? 'adjustment'))
SELECT DISTINCT "tmp_union"."id", "tmp_union"."user_name", "tmp_union"."action_time", "tmp_union"."meta"
FROM "tmp_union"
ORDER BY "tmp_union"."id" DESC
\COPY (SELECT qa.fid as file_id, REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\d+)', 'T\1'), '-', '') as rule, qa.value as predict_value, fm.stock_code as stock_code, fm.report_year as report_year, concat('http://100.64.0.105:55647/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid, '&schemaId=', REGEXP_REPLACE(qa.mold::text, '3', ''),'&rule=', replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20')) as url FROM (SELECT id, fid, mold, (item ->> 'key')::jsonb ->> 1 as field, (item ->> 'key')::jsonb ->> 2 as words, item ->> 'value' AS value FROM (SELECT id, fid, mold, item FROM question, json_array_elements(preset_answer -> 'userAnswer' -> 'items') item WHERE mold in (31, 32)) q /*AND (item ->> 'key') LIKE '%Beginning amount%'*/) qa JOIN file ON file.id = qa.fid JOIN file_esg_xref ON file.id = file_esg_xref.fid JOIN hkex_file_meta fm ON fm.fid = qa.fid where file_esg_xref.activated and fm.report_year in ('2023', '2024') and fm.fid in (66219, 66401, 68210, 68360, 68581, 69338, 69360, 70525, 70723, 70820, 71011, 71288, 71289, 71290, 71312, 71324, 71333, 71373, 71375, 71393, 71397, 71398, 71405, 71435, 71525, 71546, 71586, 71587, 71588, 103406, 103407, 103409, 103410, 103412, 103414, 103415, 103418, 103421, 111769, 111911, 112069, 112239, 112679) ORDER BY fm.fid) TO '~/policy-esg.csv'
WITH (FORMAT CSV, HEADER);


SELECT qa.fid                                                                                          as file_id,
       REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\d+)', 'T\1'), '-', '') as rule,
       qa.value                                                                                        as predict_value,
       fm.stock_code                                                                                   as stock_code,
       fm.report_year                                                                                  as report_year,
       concat('http://100.64.0.105:55647/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid,
              '&schemaId=', REGEXP_REPLACE(qa.mold::text, '3', ''), '&rule=',
              replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20'))                          as url
FROM (SELECT id,
             fid,
             mold,
             (item ->> 'key')::jsonb ->> 1 as field,
             (item ->> 'key')::jsonb ->> 2 as words,
             item ->> 'value'              AS value
      FROM (SELECT id, fid, mold, item
            FROM question,
                 json_array_elements(preset_answer -> 'userAnswer' -> 'items') item
            WHERE mold in (31, 32)) q /*AND (item ->> 'key') LIKE '%Beginning amount%'*/) qa
         JOIN file ON file.id = qa.fid
         JOIN file_esg_xref ON file.id = file_esg_xref.fid
         JOIN hkex_file_meta fm ON fm.fid = qa.fid
where file_esg_xref.activated
  and fm.report_year in ('2023', '2024')
  and fm.fid in
      (66219, 66401, 68210, 68360, 68581, 69338, 69360, 70525, 70723, 70820, 71011, 71288, 71289, 71290, 71312, 71324,
       71333, 71373, 71375, 71393, 71397, 71398, 71405, 71435, 71525, 71546, 71586, 71587, 71588, 103406, 103407,
       103409, 103410, 103412, 103414, 103415, 103418, 103421, 111769, 111911, 112069, 112239, 112679)
ORDER BY fm.fid \COPY ( select q.fid from question q join hkex_file_meta fm on q.fid = fm.fid where q.mold in (31, 32) and q.preset_answer is null and fm.report_year in ('2023', '2024')) TO '~/new_fids.csv'
WITH (FORMAT CSV, HEADER);

\COPY
( select data from mold where id=18) TO '~/lir_18_mold.csv' WITH (FORMAT CSV, HEADER);
\COPY
( select * from question_prev_fund_raising ) TO '~/question_prev_fund_raising.csv' WITH (FORMAT CSV, HEADER);

select *
from question_prev_fund_raising


SELECT qa.fid                                                                               as file_id,
       qa.preset_field                                                                      as rule,
       qa.preset_value                                                                      as predict_value,
       qa.label_value                                                                       as label_value,
       fm.stock_code                                                                        as stock_code,
       fm.report_year                                                                       as report_year,
       concat('https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid,
              '&schemaId=1', '&rule=',
              replace(substr(qa.preset_field, 0, length(qa.preset_field) - 1), ' ', '%20')) as url
FROM (SELECT id,
             fid,
             mold,
             (item ->> 'key')::jsonb ->> 1 as preset_field,
             (item ->> 'key')::jsonb ->> 2 as preset_words,
             item ->> 'value'              AS preset_value,
             label_item ->> 'value'        AS label_value
      FROM (SELECT id, fid, mold, item, label_item
            FROM question,
                 json_array_elements(preset_answer -> 'userAnswer' -> 'items') item,
                 json_array_elements(answer -> 'userAnswer' -> 'items') label_item
            WHERE mold in (31, 32)) q) qa
         JOIN file ON file.id = qa.fid
         JOIN file_esg_xref ON file.id = file_esg_xref.fid
         JOIN hkex_file_meta fm ON fm.fid = qa.fid
where file_esg_xref.activated
  and fm.report_year in ('2023', '2024')
  and qa.preset_field like '%E1-Reference to ISSB Standards%'
ORDER BY fm.fid

SELECT distinct qa.fid                                                                               as file_id,
                qa.preset_field                                                                      as rule,
                LOWER(CASE
                          WHEN qa.preset_value LIKE '["_%"]' THEN REPLACE(REPLACE(qa.preset_value, '["', ''), '"]', '')
                          ELSE qa.preset_value
                    END)                                                                             as predict_value,
                LOWER(CASE
                          WHEN qa.label_value LIKE '["_%"]' THEN REPLACE(REPLACE(qa.label_value, '["', ''), '"]', '')
                          ELSE qa.label_value
                    END)                                                                             as label_value,
                fm.stock_code                                                                        as stock_code,
                fm.report_year                                                                       as report_year,
                concat('http://localhost:9003/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid,
                       '&schemaId=2', '&rule=',
                       replace(substr(qa.preset_field, 0, length(qa.preset_field) - 1), ' ', '%20')) as url
FROM (SELECT id,
             fid,
             mold,
             (item ->> 'key')::jsonb ->> 1       as preset_field,
             (item ->> 'key')::jsonb ->> 2       as preset_words,
             item ->> 'value'                    AS preset_value,
             (label_item ->> 'key')::jsonb ->> 1 as label_field,
             label_item ->> 'value'              AS label_value
      FROM (SELECT id, fid, mold, item, label_item
            FROM question,
                 json_array_elements(preset_answer -> 'userAnswer' -> 'items') item,
                 json_array_elements(answer -> 'userAnswer' -> 'items') label_item
            WHERE mold in (31, 32)) q) qa
         JOIN file ON file.id = qa.fid
         JOIN file_esg_xref ON file.id = file_esg_xref.fid
         JOIN hkex_file_meta fm ON fm.fid = qa.fid
where file_esg_xref.activated
  and fm.report_year in ('2023', '2024')
-- and qa.preset_field like '%E1-Reference to ISSB Standards%' and label_field like  '%E1-Reference to ISSB Standards%'
  and qa.preset_field like '%E7%'
  and label_field like '%E7%'
  AND LOWER(CASE
                WHEN qa.preset_value LIKE '["_%"]' THEN REPLACE(REPLACE(qa.preset_value, '["', ''), '"]', '')
                ELSE qa.preset_value
    END)
    != LOWER(CASE
                 WHEN qa.label_value LIKE '["_%"]' THEN REPLACE(REPLACE(qa.label_value, '["', ''), '"]', '')
                 ELSE qa.label_value
        END)
ORDER BY file_id



CREATE INDEX ON embedding USING hnsw (embedding vector_cosine_ops);


select *
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.report_year = '2024'
  and h.published > '2025-02-01';


select h.fid
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.fid in
      (70748, 68816, 71588, 104124, 68830, 68214, 104116, 104032, 69689, 69360, 69338, 68581, 71288, 66219, 70820,
       71017, 104122, 70615, 71002, 70948, 70901, 68360, 70723, 70935, 71011, 68744, 104105, 68770, 104206, 66250,
       71525, 103421, 71290, 71587, 103415, 71333, 103410, 112239, 112069, 71373, 71324, 71546, 71375, 71405, 71398,
       71393, 103409, 103407, 111769, 103406, 103412)


select *
from director
where stock_code = '00186'
  and english_name like '%BINNEY%';

SELECT qa.fid                                                                                           as file_id,
       REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\\d+)', 'T\1'), '-', '') as rule,
       LOWER(CASE
                 WHEN qa.value LIKE '[\"_%\"]' THEN REPLACE(REPLACE(qa.value, '[\"', ''), '\"]', '')
                 ELSE qa.value END)                                                                     as predict_value,
       fm.stock_code                                                                                    as stock_code,
       fm.report_year                                                                                   as report_year,
       concat('https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid,
              '&schemaId=1', '&rule=', replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20'))  as url
FROM (SELECT id,
             fid,
             mold,
             (item ->> 'key')::jsonb ->> 1 as field,
             (item ->> 'key')::jsonb ->> 2 as words,
             item ->> 'value'              AS value
      FROM (SELECT id, fid, mold, item
            FROM question,
                 json_array_elements(preset_answer -> 'userAnswer' -> 'items') item
            WHERE mold in (31, 32)
              AND deleted_utc = 0) q) qa
         JOIN file ON file.id = qa.fid
         JOIN file_esg_xref ON file.id = file_esg_xref.fid
         JOIN hkex_file_meta fm ON fm.fid = qa.fid
where file_esg_xref.activated
  and fm.report_year in ('2023', '2024')
  and fm.fid in
      (112763, 112764, 112765, 112766, 112767, 112768, 112769, 112770, 112771, 112772, 112773, 112774, 112775, 112776,
       112777, 112778, 112779, 112780, 112781, 112782, 112783, 112784, 112785, 112786, 112787, 112788, 112789, 112790,
       112791, 112792, 112794, 112793, 112795, 112796, 112798, 112797, 112799, 112800, 112801, 112802, 112803, 112804,
       112805, 112806, 112807, 112808, 112809, 112810, 112811)
ORDER BY fm.fid \COPY (SELECT qa.fid as file_id, REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\\d+)', 'T\1'), '-', '') as rule, LOWER(CASE WHEN qa.value LIKE '[\"_%\"]' THEN REPLACE(REPLACE(qa.value, '[\"', ''), '\"]', '') ELSE qa.value END) as predict_value, fm.stock_code as stock_code, fm.report_year as report_year, concat('https://jura6-esg.paodingai.com/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', qa.fid, '&schemaId=1', '&rule=', replace(substr(qa.field, 0, length(qa.field) - 1), ' ', '%20')) as url FROM (SELECT id, fid, mold, (item ->> 'key')::jsonb ->> 1 as field, (item ->> 'key')::jsonb ->> 2 as words, item ->> 'value' AS value FROM (SELECT id, fid, mold, item FROM question, json_array_elements(preset_answer -> 'userAnswer' -> 'items') item WHERE mold in (31, 32) AND deleted_utc = 0) q) qa JOIN file ON file.id = qa.fid JOIN file_esg_xref ON file.id = file_esg_xref.fid JOIN hkex_file_meta fm ON fm.fid = qa.fid where file_esg_xref.activated and fm.report_year in ('2023', '2024') and fm.fid in (112763,112764,112765,112766,112767,112768,112769,112770,112771,112772,112773,112774,112775,112776,112777,112778,112779,112780,112781,112782,112783,112784,112785,112786,112787,112788,112789,112790,112791,112792,112794,112793,112795,112796,112798,112797,112799,112800,112801,112802,112803,112804,112805,112806,112807,112808,112809,112810,112811) ORDER BY fm.fid) TO '~/policy-esg.csv'
WITH (FORMAT CSV, HEADER)
\COPY (select * from hkex_file_meta where doc_type=30) TO '~/agm_file_meta.csv'
WITH (FORMAT CSV, HEADER)

select *
from hkex_file_meta
where fid = 156952;


WITH stock_years AS (SELECT CASE
                                WHEN LENGTH(stock_code) = 1 THEN '0000' || stock_code
                                WHEN LENGTH(stock_code) = 2 THEN '000' || stock_code
                                WHEN LENGTH(stock_code) = 3 THEN '00' || stock_code
                                WHEN LENGTH(stock_code) = 4 THEN '0' || stock_code
                                ELSE stock_code
                                END              AS stock_code,
                            report_year::integer AS current_year
                     FROM (VALUES ('2001', '2024'),
                                  ('2309', '2024'),
                                  ('2689', '2024'),
                                  ('8133', '2023'),
                                  ('8293', '2024'),
                                  ('9908', '2023'),
                                  ('138', '2023'),
                                  ('1730', '2024'),
                                  ('591', '2024'),
                                  ('1906', '2023'),
                                  ('1712', '2023'),
                                  ('1059', '2024'),
                                  ('115', '2023'),
                                  ('2399', '2023'),
                                  ('542', '2023'),
                                  ('659', '2024'),
                                  ('844', '2023'),
                                  ('193', '2024'),
                                  ('6959', '2023'),
                                  ('2616', '2023'),
                                  ('6669', '2023'),
                                  ('6683', '2023'),
                                  ('519', '2024'),
                                  ('1901', '2023'),
                                  ('3708', '2024'),
                                  ('92', '2024'),
                                  ('1611', '2023'),
                                  ('8475', '2024'),
                                  ('197', '2024'),
                                  ('8365', '2024')) AS t(stock_code, report_year))

SELECT hfm.fid,
       hfm.stock_code,
       hfm.report_year
FROM hkex_file_meta hfm
         JOIN
     stock_years sy ON hfm.stock_code = sy.stock_code AND hfm.report_year = (sy.current_year - 1)::text
WHERE hfm.deleted_utc = 0
ORDER BY hfm.stock_code;


select fid
from hkex_file_meta
where fid in
      (68567, 67078, 67151, 68505, 68535, 68536, 67061, 68579, 68594, 67383, 68563, 68684, 67982, 68703, 66859, 67388,
       68662, 68558, 66662, 67414, 68577, 68543, 67491, 68695, 68534, 70657, 103992, 67511);

select h.fid, h.report_year, h.published
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.report_year = '2024'
  and h.published > '2025-03-25';

select h.fid as fid
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.report_year = '2024'
  and h.published > '2025-03-25';
select h.fid as fid
from hkex_file_meta h
         inner join file_esg_xref x on h.fid = x.fid
where x.activated
  and h.doc_type in (1, 2)
  and h.report_year = '2024'
  and h.published > '2025-03-25'
  and h.published < '2025-03-26';

\COPY
(select * from hkex_file_meta where fid in (156968,155354,155563,155628,155643,155646,155668,155673,155695,155706,155738,155751,155752,155795,155801,155815,155818,155854,156975,156996,155181,155354,155369,155374,155398,155401,155412,155414,155419,155421,155431,155436,155441,155449,155450,155500,155519,155539,155551,156860,156880,156070,156199,156336,156934,156939,155738,155928,155944,155949,155963,155967,155971,155979,156003,156005,156013,156014,156025,156035,156039,156041,156070,156074,156091,156097,156147,156162,156164,156179,156199,156209,156238,156331,156336,156351,156888,156889,156907,157057,156832,156835,156839,156850,156855,157064,157071,157075,157076,157088,157091,157092,157093,157133,157136,157142,157149,156548,156570,156580,156586,156592,156599,156600,156607,156611,156612,156617,156628,156633,156636,156642,156648,156654,156666,156669,156686,156687,156715,156762,156773,156807,155149,155150,155152,155181,155194,155209,155212,155218,155263,157064,156461,156474,156490,156503,155149,155150,155152,155181,155194,155209,155212,155218,155263,155354,155369,155374,155398,155401,155412,155414,155419,155421,155431,155436,155441,155449,155450,155500,155519,155539,155551,155563,155628,155643,155646,155668,155673,155695,155706,155738,155751,155752,155795,155801,155815,155818,155854,155928,155944,155949,155963,155967,155971,155979,156003,156005,156013,156014,156025,156035,156039,156041,156070,156074,156091,156097,156147,156162,156164,156179,156199,156209,156238,156331,156336,156351,156461,156474,156490,156503,156548,156570,156580,156586,156592,156599,156600,156607,156611,156612,156617,156628,156633,156636,156642,156648,156654,156666,156669,156686,156687,156715,156762,156773,156807,156832,156835,156839,156850,156855,156860,156880,156888,156889,156907,156934,156939,156968,156975,156996,157057,157064,157071,157075,157076,157088,157091,157092,157093,157133,157136,157142,157149)) TO '/tmp/fix_error.csv' WITH (FORMAT CSV, HEADER)
\COPY (select * from hkex_file_meta where doc_type=30) TO '~/agm_file_meta.csv' WITH (FORMAT CSV, HEADER)
\COPY (select q.fid, q.mold, hm.published, hm.stock_code, hm.report_year, hm.doc_type from question q  join public.hkex_file_meta hm on q.fid = hm.fid where preset_answer is null and hm.published > '2025-03-20') TO '/tmp/preset_answer_null.csv' WITH (FORMAT CSV, HEADER)

select q.fid, q.mold
from question q
         join public.hkex_file_meta hm on q.fid = hm.fid
where preset_answer is null
  and hm.published > '2025-03-20'


SELECT "t1"."id",
       "t1"."name",
       "t1"."parent_id",
       "t1"."is_enabled"
FROM "rule_group" AS "t1"
         INNER JOIN "rule_group" AS "dad" ON ("dad"."id" = "t1"."parent_id")
         INNER JOIN "rule_group" AS "grandpa" ON ("dad"."parent_id" = "grandpa"."id")
WHERE dad.level = 1
  and "grandpa"."name" = 'Annual Report'
  and "dad"."name" = 'All'
  and t1.name = 'Treasury shares'
-- WHERE ((((("t1"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("dad"."deleted_utc" = 0)) AND
--         ("grandpa"."deleted_utc" = 0)) AND
--        (("t1"."level" = 2), ("grandpa"."name" = 'Annual Report'), ("dad"."name" = 'All'), ("dad"."level" = 1),
--         ("grandpa"."level" = 0), ("t2"."rulereference_id" = 447)))
-- ORDER BY "t1"."id";


explain
WITH "tmp_table" AS (SELECT "t1"."fid",
                            RANK()
                            OVER (PARTITION BY "t1"."stock_code", "t1"."report_year" ORDER BY "t1"."published" DESC, "t1"."fid" DESC) AS "rank"
                     FROM "hkex_file_meta" AS "t1"
                              INNER JOIN "file_esg_xref" AS "t2" ON ("t1"."fid" = "t2"."fid")
                     WHERE (((("t1"."deleted_utc" = 0) AND "t2"."activated") AND ("t1"."doc_type" IN (2, 1))) AND
                            (CAST("t1"."report_year" AS int) BETWEEN 2019 AND 2024))),
     "tmp_ans" AS (SELECT "t3"."id",
                          RANK() OVER (PARTITION BY "t3"."rule", "t3"."fid" ORDER BY "t3"."uid" DESC) AS "rank"
                   FROM "esg_result" AS "t3"
                            INNER JOIN "tmp_table" ON ("t3"."fid" = "tmp_table"."fid")
                   WHERE ((("t3"."uid" >= 0) AND ("t3"."rule" = 'MDR 13 i - board oversight')) AND
                          ("tmp_table"."rank" = 1))) ((SELECT 0,
                                                              "t4"."report_year",
                                                              "t5"."enum_value",
                                                              COUNT("t5"."enum_value")
                                                       FROM "esg_result" AS "t5"
                                                                INNER JOIN "hkex_file_meta" AS "t4" ON ("t5"."fid" = "t4"."fid")
                                                                INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t5"."id")
                                                       WHERE (("t4"."deleted_utc" = 0) AND ("tmp_ans"."rank" = 1))
                                                       GROUP BY "t4"."report_year", "t5"."enum_value")
                                                      UNION ALL
                                                      (SELECT 1,
                                                              "t1"."report_year",
                                                              "t3"."enum_value",
                                                              COUNT("t3"."enum_value")
                                                       FROM "esg_result" AS "t3"
                                                                INNER JOIN "hkex_file_meta" AS "t1" ON ("t3"."fid" = "t1"."fid")
                                                                INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t3"."id")
                                                       WHERE (("t1"."deleted_utc" = 0) AND
                                                              (("tmp_ans"."rank" = 1) AND NOT ("t1"."stock_code" ILIKE '08%')))
                                                       GROUP BY "t1"."report_year", "t3"."enum_value"))
UNION ALL
(SELECT 2, "t1"."report_year", "t3"."enum_value", COUNT("t3"."enum_value")
 FROM "esg_result" AS "t3"
          INNER JOIN "hkex_file_meta" AS "t1"
                     ON ("t3"."fid" = "t1"."fid")
          INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t3"."id")
 WHERE (("t1"."deleted_utc" = 0) AND (("tmp_ans"."rank" = 1) AND ("t1"."stock_code" ILIKE '08%')))
 GROUP BY "t1"."report_year", "t3"."enum_value") \COPY (
select distinct convening_date
from agm_meta) TO '/tmp/all_convening_date.csv'
WITH (FORMAT CSV, HEADER)
select distinct convening_date
from agm_meta \ COPY (select q.fid, q.mold, hm.published, hm.stock_code, hm.report_year, hm.doc_type from question q  join public.hkex_file_meta hm on q.fid = hm.fid where preset_answer is null and hm.published > '2025-03-20') TO '/tmp/preset_answer_null.csv'
WITH (FORMAT CSV, HEADER)

select q.fid, q.mold, hm.published, hm.stock_code, hm.report_year, hm.doc_type
from question q
         join public.hkex_file_meta hm on q.fid = hm.fid
where preset_answer is null
  and hm.fid in
      (93658, 91985, 91669, 91510, 91417, 91330, 91291, 91262, 91258, 91252, 91179, 91156, 91098, 91071, 91029, 91023,
       90819, 90792, 90679, 90595, 90556, 90553, 90549, 90525, 90521, 90470, 90457, 90418, 90373, 90354, 90329, 90319,
       90276, 90274, 90177, 90065, 90061, 90039, 90025, 89950, 89947, 89933, 89924, 89868, 89851, 89747, 89738, 89729,
       89713, 89709, 89666, 89643, 89622, 89604, 89603, 89582, 89471, 89452, 89442, 89406, 89376, 89365, 89363, 89271,
       89248, 89201, 89185, 89168, 89119, 89084, 89013, 89009, 88961, 88939, 88908, 88816, 88804, 88761, 88751, 88709,
       88706, 88681, 88658, 88449, 88327, 88119, 87480, 87456, 87455, 87453, 86837, 86081, 85671, 85533)

-----
WITH "tmp_table" AS (SELECT "t1"."fid",
                            RANK()
                            OVER (PARTITION BY "t1"."stock_code", "t1"."report_year" ORDER BY "t1"."published" DESC, "t1"."fid" DESC) AS "rank"
                     FROM "hkex_file_meta" AS "t1"
                              INNER JOIN "file_esg_xref" AS "t2" ON ("t1"."fid" = "t2"."fid")
                     WHERE (((("t1"."deleted_utc" = 0) AND "t2"."activated") AND ("t1"."doc_type" IN (2, 1))) AND
                            (CAST("t1"."report_year" AS int) BETWEEN 2020 AND 2024))),
     "tmp_ans" AS (SELECT "t3"."id",
                          RANK() OVER (PARTITION BY "t3"."rule", "t3"."fid" ORDER BY "t3"."uid" DESC) AS "rank"
                   FROM "esg_result" AS "t3"
                            INNER JOIN "tmp_table" ON ("t3"."fid" = "tmp_table"."fid")
                   WHERE ((("t3"."uid" >= 0) AND ("t3"."rule" = 'MDR 13 i - board oversight')) AND
                          ("tmp_table"."rank" = 1)))
SELECT CASE
           WHEN NOT ("t4"."stock_code" ILIKE '08%') THEN 1
           WHEN ("t4"."stock_code" ILIKE '08%') THEN 2
           ELSE 0 END AS "idx",
       "t4"."report_year",
       "t5"."enum_value",
       COUNT("t5"."enum_value")
FROM "esg_result" AS "t5"
         INNER JOIN "hkex_file_meta" AS "t4" ON ("t5"."fid" = "t4"."fid")
         INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t5"."id")
WHERE (("t4"."deleted_utc" = 0) AND ("tmp_ans"."rank" = 1))
GROUP BY "t4"."report_year", "t5"."enum_value", "t4"."stock_code" \COPY (select * from hkex_file where type='NDDR' and release_time <1745510400 and release_time>=1745424000) TO '/tmp/all_convening_date.csv'
WITH (FORMAT CSV, HEADER) \COPY (select * from hkex_file where type in ('AGM', 'POLL')) TO '/tmp/prod_exist_agm_poll_20250425.csv'
WITH (FORMAT CSV, HEADER)

SELECT "t1"."id", date(to_timestamp("t1"."release_time"))
FROM "hkex_file" AS "t1"
WHERE (("t1"."deleted_utc" = 0)
    AND ((("t1"."stock_code" = '02135')
        AND ("t1"."type" = 'AGM'))))
\COPY (select * from admin_user) TO '/tmp/admin_user.csv'
WITH (FORMAT CSV, HEADER)
\COPY (select * from hkex_file where type='AGM') TO '/tmp/AGM.csv'
WITH (FORMAT CSV, HEADER)
\COPY (select * from hkex_file_meta where doc_type=31) TO '/tmp/poll.csv'
WITH (FORMAT CSV, HEADER)
EXPLAIN ANALYSE
SELECT "t1"."fid",
       "t2"."id"              AS "qid",
       "t2"."mold"            AS "mold_id",
       "t1"."stock_code",
       "t1"."name"            AS "company_name",
       "t3"."name"            AS "title",
       "t3"."headline",
       "t3"."release_time",
       "t4"."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t1"
         INNER JOIN "hkex_file" AS "t3" ON (("t1"."fid" = "t3"."fid") AND ("t3"."type" = 'AGM'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t1"."fid")
         INNER JOIN "question" AS "t2" ON (("t5"."id" = "t2"."fid") AND ("t2"."mold" = 33))
         LEFT OUTER JOIN (SELECT "t6"."file_id",
                                 json_object_agg("t6"."compliance_answer_key",
                                                 "t6"."compliance_value") AS "compliance_data"
                          FROM "agm_result" AS "t6"
                          WHERE ("t6"."mold_id" = 33)
                          GROUP BY "t6"."file_id") AS "t4" ON ("t1"."fid" = "t4"."file_id")
WHERE ((((("t1"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t2"."deleted_utc" = 0)) AND (("t1"."doc_type" = 30) AND ("t3"."release_time" >= 1737938901)))
ORDER BY "t1"."published" DESC;


EXPLAIN ANALYSE
SELECT "t1"."fid",
       "t2"."id"              AS "qid",
       "t2"."mold"            AS "mold_id",
       "t1"."stock_code",
       "t1"."name"            AS "company_name",
       "t3"."name"            AS "title",
       "t3"."headline",
       "t3"."release_time",
       "t4"."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t1"
         INNER JOIN "hkex_file" AS "t3" ON (("t1"."fid" = "t3"."fid") AND ("t3"."type" = 'AGM'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t1"."fid")
         INNER JOIN "question" AS "t2" ON (("t5"."id" = "t2"."fid") AND ("t2"."mold" = 33))
         LEFT OUTER JOIN (SELECT "t6"."file_id",
                                 json_object_agg("t6"."compliance_answer_key",
                                                 "t6"."compliance_value") AS "compliance_data"
                          FROM "agm_result" AS "t6"
                          GROUP BY "t6"."file_id") AS "t4" ON ("t1"."fid" = "t4"."file_id")
WHERE ((((("t1"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t2"."deleted_utc" = 0)) AND (("t1"."doc_type" = 30)))
ORDER BY "t1"."published" DESC;



EXPLAIN ANALYSE
SELECT "t1"."fid",
       "t1"."stock_code",
       "t1"."name"            AS "company_name",
       "t3"."name"            AS "title",
       "t3"."headline",
       "t3"."release_time",
       "t4"."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t1"
         INNER JOIN "hkex_file" AS "t3" ON (("t1"."fid" = "t3"."fid") AND ("t3"."type" = 'AGM'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t1"."fid")
         LEFT OUTER JOIN (SELECT "t6"."file_id",
                                 json_object_agg("t6"."compliance_answer_key",
                                                 "t6"."compliance_value") AS "compliance_data"
                          FROM "agm_result" AS "t6"
                          WHERE ("t6"."mold_id" = 33)
                          GROUP BY "t6"."file_id") AS "t4" ON ("t1"."fid" = "t4"."file_id")
WHERE ((((("t1"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0))) AND
       (("t1"."doc_type" = 30) AND ("t3"."release_time" >= 1737938901)))
ORDER BY "t3"."release_time" DESC;



WITH filtered_agm AS (SELECT file_id,
                             json_object_agg(compliance_answer_key, compliance_value) AS compliance_data
                      FROM agm_result
                      WHERE mold_id = 33
                      GROUP BY file_id)
SELECT t1.fid,
       t2.id              AS qid,
       t2.mold            AS mold_id,
       t1.stock_code,
       t1.name            AS company_name,
       t3.name            AS title,
       t3.headline,
       t3.release_time,
       t4.compliance_data AS stat_res
FROM hkex_file_meta t1
         INNER JOIN hkex_file t3 ON t1.fid = t3.fid AND t3.type = 'AGM'
         INNER JOIN file t5 ON t5.id = t1.fid
         INNER JOIN question t2 ON t5.id = t2.fid AND t2.mold = 33
         LEFT OUTER JOIN filtered_agm t4 ON t1.fid = t4.file_id
WHERE t1.deleted_utc = 0
  AND t3.deleted_utc = 0
  AND t5.deleted_utc = 0
  AND t2.deleted_utc = 0
  AND t1.doc_type = 30
  AND t3.release_time >= 1737938901
ORDER BY t1.published DESC;


SELECT "t1"."fid",
       "t1"."stock_code",
       "t1"."name"            AS "company_name",
       "t2"."name"            AS "title",
       "t2"."headline",
       "t2"."release_time",
       "t3"."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t1"
         INNER JOIN "hkex_file" AS "t2" ON (("t1"."fid" = "t2"."fid") AND ("t2"."type" = 'AGM'))
         INNER JOIN "file" AS "t4" ON ("t4"."id" = "t1"."fid")
         LEFT OUTER JOIN (SELECT "t5"."file_id",
                                 json_object_agg("t5"."compliance_answer_key",
                                                 "t5"."compliance_value") AS "compliance_data"
                          FROM "agm_result" AS "t5"
                          WHERE ("t5"."mold_id" = 33)
                          GROUP BY "t5"."file_id") AS "t3" ON ("t1"."fid" = "t3"."file_id")
WHERE (((("t1"."deleted_utc" = 0) AND ("t2"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND
       (("t1"."doc_type" = 30) AND ("t2"."release_time" >= 1737945486)))
ORDER BY "t1"."published" DESC
LIMIT 10 OFFSET 0;


EXPLAIN ANALYZE
WITH aggregated_agm_results AS MATERIALIZED (SELECT t6.file_id,
                                                    json_object_agg(t6.compliance_answer_key, t6.compliance_value) AS "compliance_data"
                                             FROM "agm_result" AS "t6"
                                             WHERE ("t6"."mold_id" = 33)
                                             GROUP BY "t6"."file_id")
SELECT "t1"."fid",
       "t2"."id"            AS "qid",
       "t2"."mold"          AS "mold_id",
       "t1"."stock_code",
       "t1"."name"          AS "company_name",
       "t3"."name"          AS "title",
       "t3"."headline",
       "t3"."release_time",
       t4."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t1"
         INNER JOIN "hkex_file" AS "t3" ON (("t1"."fid" = "t3"."fid") AND ("t3"."type" = 'AGM'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t1"."fid")
         INNER JOIN "question" AS "t2" ON (("t5"."id" = "t2"."fid") AND ("t2"."mold" = 33))
         LEFT OUTER JOIN aggregated_agm_results AS t4 ON ("t1"."fid" = t4."file_id") -- 加入 CTE
WHERE ((((("t1"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t2"."deleted_utc" = 0)) AND (("t1"."doc_type" = 30) AND ("t3"."release_time" >= 1737941921))) -- 保留条件
ORDER BY "t1"."published" DESC;

EXPLAIN ANALYZE
WITH "file_meta_with_rank" AS (SELECT "t1"."fid",
                                      ROW_NUMBER()
                                      OVER (PARTITION BY "t1"."stock_code", "t1"."report_year" ORDER BY "t1"."published" DESC) AS "rank"
                               FROM "hkex_file_meta" AS "t1"
                               WHERE (("t1"."deleted_utc" = 0) AND
                                      ((("t1"."doc_type" = 1) AND (CAST("t1"."report_year" AS int) >= 2020)) AND
                                       ("t1"."report_year" IN ('2024')))))
SELECT "t2"."admin_user_id"                                       AS "uid",
       "t3"."main_alias"                                          AS "rule_number",
       "t3"."rule_description",
       "t4"."name"                                                AS "company_name",
       "t4"."stock_code",
       "t4"."report_year"                                         AS "financial_year",
       "t4"."year_end"                                            AS "financial_year_end",
       to_char(CAST("t4"."published" AS timestamp), 'YYYY/MM/DD') AS "date_of_annual_report",
       'Listing'                                                  AS "listing_status",
       CASE
           WHEN ("t2"."enum_list" @ > ARRAY [1]) THEN 'Comply'
           WHEN ("t2"."enum_list" @ > ARRAY [2]) THEN 'No Disclosure'
           WHEN ("t2"."enum_list" @ > ARRAY [3]) THEN 'Not Applicable'
           ELSE '' END                                            AS "enum_value",
       "t2"."data"                                                AS "boxes"
FROM "cg_result" AS "t2"
         INNER JOIN "question" AS "t5" ON ("t2"."question_id" = "t5"."id")
         INNER JOIN "hkex_file_meta" AS "t4" ON ("t4"."fid" = "t5"."fid")
         INNER JOIN "rule_reference" AS "t3" ON ("t3"."rule" = "t2"."rule")
         INNER JOIN "file_meta_with_rank"
                    ON (("t4"."fid" = "file_meta_with_rank"."fid") AND ("file_meta_with_rank"."rank" = 1))
WHERE (((("t5"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t3"."deleted_utc" = 0)) AND
       (((("t4"."doc_type" = 1) AND (CAST("t4"."report_year" AS int) >= 2020)) AND ("t4"."report_year" IN ('2024'))) AND
        ("t2"."rule" = 'A(a)-Explanation of application of code principles')))
ORDER BY CAST("t4"."stock_code" AS int), CAST("t4"."report_year" AS int) DESC, "t3"."order", "t2"."admin_user_id" DESC


SELECT "t1"."doc_type",
       "t2"."activated",
       "t1"."name",
       "t1"."published",
       "t1"."report_year",
       "t1"."stock_code",
       "t1"."year_end",
       "t3"."name" AS "title",
       "t4"."fid"
FROM "question" AS "t4"
         INNER JOIN "hkex_file_meta" AS "t1" ON ("t4"."fid" = "t1"."fid")
         LEFT OUTER JOIN "hkex_file" AS "t3" ON ("t1"."fid" = "t3"."fid")
         LEFT OUTER JOIN "file_esg_xref" AS "t2" ON ("t4"."fid" = "t2"."fid")
         INNER JOIN "hkex_companies_info" AS "t5" ON ("t1"."stock_code" = "t5"."stock_code")
WHERE ((("t4"."deleted_utc" = 0) AND ("t1"."deleted_utc" = 0)) AND
       ((("t4"."id" = 395072) AND ("t5"."delisted_date" IS NULL)) AND ("t1"."deleted_utc" = 0)))


select count(*)
from agm_result am
         join hkex_file_meta hm on am.file_id = hm.fid
where hm.report_year = '2025'
  and am.compliance_answer_key = 'M2-total number and description of the shares which the issuer proposes to purchase'
  and am.compliance_value = 'potential non-compliance';

select count(*)
from agm_result am
         join hkex_file_meta hm on am.file_id = hm.fid
where hm.report_year = '2025'
  and am.compliance_answer_key = 'M2-total number and description of the shares which the issuer proposes to purchase'
  and am.compliance_value = 'compliance';

select file_id, compliance_value
from poll_result
where compliance_value = '';


select distinct file_id
from poll_result am
         join hkex_file_


select count(*)
from hkex_file
where fid not in (select fid from hkex_file_meta where doc_type = 31)
  and type = 'POLL';
select hk.id, hk.stock_code
from hkex_file hk
where hk.stock_code not in (select stock_code from hkex_file_meta where doc_type = 34);

select stock_code
from hkex_file_meta
where doc_type = 31
\COPY (select * from hkex_file where type='POLL') TO '/tmp/poll_file.csv'
WITH (FORMAT CSV, HEADER) \COPY (select * from hkex_file_meta where doc_type=31) TO '/tmp/poll_file_meta.csv'
WITH (FORMAT CSV, HEADER)


SELECT to_timestamp(updated_utc) + interval '8 hours'
FROM question
WHERE id = 260538;
SELECT to_timestamp(created_utc) + interval '8 hours'
FROM file
WHERE id = 92926;


select h.report_year::int                                      as financial_year,
       extract(epoch from h.published::timestamp)::int - 28800 as published_time,
       q.ar_status                                             as ar_status,
       f.id                                                    as file_id,
       f.tree_id                                               as tree_id,
       f.pid                                                   as project_id,
       q.id                                                    as question_id,
       q.mold                                                  as mold_id,
       h.doc_type                                              as doc_type
from hkex_file_meta h
         inner join file f on f.id = h.fid
         inner join question q on q.fid = f.id

where h.report_year >= '2020' -- 只需查询指定年份以后的数据
  and q.mold in (5)
  and h.deleted_utc = 0

  and h.doc_type = 1
  and h.id in (select max(h.id)
               from hkex_file_meta h
                        inner join file f on f.id = h.fid
               where f.deleted_utc = 0
                 and h.doc_type = 1
                 and f.qid is not null
                 and h.qid is not null
                 and h.report_year >= '2020' -- 只需要近五年的年报记录
               group by h.stock_code, h.report_year)
  and h.stock_code = '08308'
order by h.published desc, h.doc_type;


UPDATE hkex_file_meta
SET deleted_utc=0,
    stock_code='09893'
WHERE fid in (92924, 79208, 60274, 37186, 33366);

INSERT INTO hkex_file (name, company, stock_code, type, headline, url, result, release_time, created_utc, finished_utc,
                       hash, size, tu_hash, pdf_hash, meta, status, updated_utc, deleted_utc, fid)
SELECT name,
       company,
       '09893',
       type,
       headline,
       url,
       result,
       release_time,
       extract(epoch from now())::int,
       finished_utc,
       hash,
       size,
       md5(type || url)::text,
       pdf_hash,
       meta,
       status,
       extract(epoch from now()):: int,
       0,
       fid
FROM hkex_file
WHERE id = 724455
RETURNING id;

insert into delisted_file_meta
select *
from hkex_file_meta
where fid = 92924;

select *
from hkex_file
where id = 3516428;


INSERT INTO public.file_esg_xref (id, fid, activated)
VALUES (DEFAULT, 72408, true);


select count(*)
from hkex_file_meta
where report_year = '2024'
  and doc_type = 1
  and published > '2025-03-01T00:00:00';
select fid
from hkex_file_meta
where report_year = '2024'
  and doc_type = 1
  and published > '2025-03-01T00:00:00'

select fid
from hkex_file_meta
where report_year = '2024'
  and doc_type = 1
  and published > '2025-04-10T00:00:00';


update delisted_file_meta
set deleted_utc=1
where stock_code = '08053';


\COPY
(SELECT f.id from file f JOIN file_esg_xref ON f.id = file_esg_xref.fid JOIN hkex_file_meta fm ON fm.fid = f.id where file_esg_xref.activated and fm.report_year = '2024' and fm.fid not in (66219, 68360, 68581, 69360, 70723, 70820, 71011, 71288, 71290, 71324, 71333, 71373, 71375, 71393, 71398, 71405, 71525, 71546, 71587, 71588, 103406, 103407, 103409, 103410, 103412, 103415, 103421, 111769, 112069, 112239,112992,112886,112875,112834,112886,112990,112976,112880,112981,112991,112821,112982,112963,112869,112910,112913,112984,112957,112947,112950) ORDER BY fm.fid desc) TO '/tmp/policy_2024.csv' WITH (FORMAT CSV, HEADER)

\COPY (SELECT f.id from file f JOIN file_esg_xref ON f.id = file_esg_xref.fid JOIN hkex_file_meta fm ON fm.fid = f.id where file_esg_xref.activated and fm.report_year = '2023' and fm.fid not in (66219, 68360, 68581, 69360, 70723, 70820, 71011, 71288, 71290, 71324, 71333, 71373, 71375, 71393, 71398, 71405, 71525, 71546, 71587, 71588, 103406, 103407, 103409, 103410, 103412, 103415, 103421, 111769, 112069, 112239, 112992,112886,112875,112834,112886,112990,112976,112880,112981,112991,112821,112982,112963,112869,112910,112913,112984,112957,112947,112950) ORDER BY fm.fid desc) TO '/tmp/policy_2023.csv' WITH (FORMAT CSV, HEADER)

\COPY (select stock_code, report_year,hm.fid, CASE WHEN doc_type=1 THEN 'AR' WHEN doc_type=2 THEN 'ESG' END AS "doc_type" , published, concat('http://100.64.0.105:55647/#/project/remark/', qa.id, '?treeId=', file.tree_id, '&fileId=', hm.fid, '&schemaId=', qa.mold, '&projectId=', file.pid) label_url, concat('http://100.64.0.105:55647/#/hkex/esg-report-checking/report-review/', qa.id, '?fileId=', hm.fid, '&schemaId=', qa.mold, '&rule=E1-Reference%20to%20ISSB%20Standards&delist=0') review_url from hkex_file_meta hm join file on file.id = hm.fid join question qa on qa.fid = file.id where hm.fid in (112812,112814,112816,112821,112823,112829,112831,112832,112836,112839,112840,112841,112849,112850,112855,112856,112858,112860,112863,112865,112871,112872,112876,112880,112886,112888,112891,112899,112900,112903,112907,112909,112914,112915,112916,112919,112923,112924,112925,112928,112932,112939,112940,112943,112953,112958,112959,112969,112974,112976,112980,112979,112981,112983,112984,112990,112991,112992,112993,112994,112995,112996,71288,70772,71479,104174,70613) and qa.mold in (31,32)) TO '/tmp/policy_label_url.csv' WITH (FORMAT CSV, HEADER)

\COPY (select * from hkex_file where hash in ('7a632b6894279fa26a9323ee5eff0f84', '4ecd24a480df158262195bfd2785e7b2', 'b12a6fcdacd8bb9974e4964e0c4e8787', 'd3c04f92a54d54a1253ecd9a67fad05f', '3bd729f7b5aea78f0c8d0fd37532a0a5', 'bf6186b9217be06cd912d0986e2ea869', 'fa15df8f36850647a7a3da25a884423b', '8e85bc19659a2f0d0e2bc495a65c5ea0', 'd4ee54ae59129f1417148207602d205c', '0a4113757a4a51b8d5268b8c6a760b4b', '7725c7da593455933c98ab834fde9f3f', 'b1a36964b5e1b8fc7aa2f0ffc79068ac', 'c95a8b482e4bc9e5a9541a820f1dcdb4', 'ee66d7124e847cf094fa734131b5dff8', 'bbb10ec080a56c4ba4ba1237a4120c28', 'b085e56d090e2345554b4a545e365aba', 'e51a48d7c44ce8ae38dd204563ad11c5', 'b39d55430e25840d7780ba839330228c', '5e03a077b2e7391f66300885658ac202', '5bb0fb417868981b05fdb3b91f71cc65', '2390d502f83f816d890565f9f92ebb59', '119352adf3ab7ca566b0be6fff371a0c', '93d8c0886c87fd8cf34dff75bfcad1db', '1c2fbfd12f97e3aff4515f448c6f232c', 'b77200723638a92352b1fa03b25f4c04', '895bcffe59e53ea57bcd7e5b1e288859', '21614d7f08735c4f06c4826a3a1cd5b9', 'f0a834c088790956d0f60271def68f53', '2b4334266c2f2ef43681384183e70a17', '73eeb9308d1b1145f9d6d4e5817704ae', 'fcd123e374f7054b0b374fe6b7536018', 'f383bf45377ec1a884ef9090124e678a', 'f241e386b35ce5a27f09c9377ea01a4b', 'ea1f26ab8ec08e5ac04a98ddc584e9ad', '38ce2588d9605e595f4963dd8f198b99', 'hash', '7d8337d7fe1195a11d3db119e669e5eb', '31018ad4a59d33113108f151a5c6fe5f', 'e2cd97032700241deb5aec98de5f5f1f', '9de5da75df0c0ba213089eff0704e4a2', '99838e19c0af8783acd34b5f3fe66481', '7cce3056eac6be1d8b55b6a36d3aa359', 'cf063e640e27cf0ec364cce81af0cc2e', '15d064d5ffb4d402b202f2b3537f8624')) TO '/tmp/different_aboutsupplemental.csv' WITH (FORMAT CSV, HEADER)

select count(*)
from hkex_companies_info;

select *
from file
where qid is null
  and 36 = any (mold_list)

UPDATE hkex_file
SET stock_code = LPAD(stock_code, 5, '0')
where LENGTH(stock_code) < 5
  and type = 'POLL';

SELECT "t1"."id"
FROM "hkex_companies_info" AS "t1"
WHERE (("t1"."stock_code" = '01439') AND ("t1"."delisted_date" IS NULL))

select fid
from hkex_file
where type = 'AGM'
  and created_utc > '1748164126'
  and fid is not null
select fid, type
from hkex_file
where created_utc > '1748164126'
  and fid is not null;


SELECT "t1"."id" AS "qid", "t2"."tree_id"
FROM "question" AS "t1"
         INNER JOIN "file" AS "t2" ON ("t2"."id" = "t1"."fid")
WHERE (((("t1"."deleted_utc" = 0) AND ("t2"."deleted_utc" = 0)) AND ("t1"."fid" IS NULL)) AND ("t1"."mold" = 37))
\copy (SELECT "t1"."fid" FROM "hkex_file_meta" AS "t1" INNER JOIN "file_esg_xref" AS "t2" ON ("t1"."fid" = "t2"."fid") WHERE ((((("t1"."deleted_utc" = 0) AND "t2"."activated") AND ("t1"."report_year" = '2024')) AND ("t1"."doc_type" IN (2, 1))) AND ("t1"."deleted_utc" = 0))) TO '/tmp/esg_embedding_fids_20250605.csv'
WITH (FORMAT CSV, HEADER)

select fid
from hkex_file_meta
where report_year = '2024'
  and stock_code = '00002'
  and doc_type = 1


select crude_answer
from question
where fid = 121015
  and mold = 32;

SELECT "t1"."fid"
FROM "hkex_file_meta" AS "t1"
         LEFT OUTER JOIN "poll_result" AS "t2" ON ("t2"."file_id" = "t1"."fid")
WHERE ((((("t1"."deleted_utc" = 0) AND ("t2"."rule_reference_id" = 566)) AND
         (CAST("t1"."report_year" AS int) >= 2024)) AND (CAST("t1"."report_year" AS int) <= 2024)) AND
       ("t1"."doc_type" = 31))
ORDER BY "t1"."report_year" DESC WITH "aggregated_results" AS MATERIALIZED (SELECT "t1"."file_id",
                                                  json_object_agg("t1"."compliance_answer_key",
                                                                  "t1"."compliance_value") AS "compliance_data"
                                           FROM "agm_result" AS "t1"
                                           WHERE ("t1"."mold_id" = 33)
                                           GROUP BY "t1"."file_id")
SELECT "t2"."fid",
       "t3"."id"                              AS "qid",
       "t3"."mold"                            AS "mold_id",
       "t2"."stock_code",
       "t2"."name"                            AS "company_name",
       "t4"."name"                            AS "title",
       "t4"."headline",
       "t4"."release_time",
       "aggregated_results"."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t4" ON (("t2"."fid" = "t4"."fid") AND ("t4"."type" = 'AGM'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t2"."fid")
         INNER JOIN "question" AS "t3" ON (("t5"."id" = "t3"."fid") AND ("t3"."mold" = 33))
         LEFT OUTER JOIN "aggregated_results" ON ("t2"."fid" = "aggregated_results"."file_id")
WHERE ((((("t2"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t3"."deleted_utc" = 0)) AND (("t2"."doc_type" = 30)))
ORDER BY "t2"."published" DESC;

INSERT INTO hkex_file (name, company, stock_code, type, headline, url, result,
                       release_time, created_utc, finished_utc, hash, size, tu_hash, pdf_hash,
                       meta, status, updated_utc, deleted_utc, fid)
VALUES ('Poll Results of the Annual General Meeting held on 8th August, 2024.pdf',
        'DICKSON CONCEPTS (INTERNATIONAL) LIMITED',
        '00113',
        'POLL',
        'Announcements and Notices - [Results of AGM]',
        'https://www1.hkexnews.hk/listedco/listconews/sehk/2024/0808/2024080800345.pdf',
        '{
          "items": [],
          "preset": false
        }',
        1723106100,
        1745579535,
        1745581304,
        '083cd0daab03fb38a5e238e252dccc2a',
        195436,
        '8c111338563dee11ea86965257ce8dfc',
        '', -- pdf_hash is empty
        '{
          "stocks": [
            "00113"
          ]
        }',
        0,
        1745579535,
        0,
        71945);

EXPLAIN ANALYZE
WITH "tmp_ans" AS (SELECT "t1"."id",
                          ROW_NUMBER()
                          OVER (PARTITION BY "t1"."rule", "t1"."question_id" ORDER BY "t1"."admin_user_id" DESC) AS "rank"
                   FROM "cg_result" AS "t1"
                            INNER JOIN "question" AS "t2" ON ("t2"."id" = "t1"."question_id")
                            INNER JOIN "hkex_file_meta" AS "t3" ON ("t2"."fid" = "t3"."fid")
                            INNER JOIN (SELECT "t3"."fid",
                                               ROW_NUMBER()
                                               OVER (PARTITION BY "t3"."stock_code", "t3"."report_year" ORDER BY "t3"."published" DESC) AS "rank"
                                        FROM "hkex_file_meta" AS "t3"
                                        WHERE (((("t3"."deleted_utc" = 0) AND ("t3"."doc_type" = 1)) AND
                                                (CAST("t3"."report_year" AS int) >= 2020)) AND
                                               (CAST("t3"."report_year" AS int) <= 2024))) AS "tmp_unique"
                                       ON ("tmp_unique"."fid" = "t3"."fid")
                   WHERE ((((((((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND
                                ("t1"."admin_user_id" >= 0)) AND ("t1"."admin_user_id" != 1)) AND
                              ("t1"."rule" = 'A(a)-Explanation of application of code principles')) AND
                             (CAST("t3"."report_year" AS int) >= 2020)) AND
                            (CAST("t3"."report_year" AS int) <= 2024)) AND ("t3"."doc_type" = 1)) AND
                          ("tmp_unique"."rank" = 1))) ((SELECT 0,
                                                               "t4"."report_year",
                                                               "t5"."enum_list",
                                                               COUNT("t5"."enum_list")
                                                        FROM "cg_result" AS "t5"
                                                                 INNER JOIN "question" AS "t6" ON ("t6"."id" = "t5"."question_id")
                                                                 INNER JOIN "hkex_file_meta" AS "t4" ON ("t6"."fid" = "t4"."fid")
                                                                 INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t5"."id")
                                                        WHERE ((("t6"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND
                                                               ("tmp_ans"."rank" = 1))
                                                        GROUP BY "t4"."report_year", "t5"."enum_list")
                                                       UNION ALL
                                                       (SELECT 1,
                                                               "t3"."report_year",
                                                               "t1"."enum_list",
                                                               COUNT("t1"."enum_list")
                                                        FROM "cg_result" AS "t1"
                                                                 INNER JOIN "question" AS "t2" ON ("t2"."id" = "t1"."question_id")
                                                                 INNER JOIN "hkex_file_meta" AS "t3" ON ("t2"."fid" = "t3"."fid")
                                                                 INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t1"."id")
                                                        WHERE ((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND
                                                               (("tmp_ans"."rank" = 1) AND NOT ("t3"."stock_code" ILIKE '08%')))
                                                        GROUP BY "t3"."report_year", "t1"."enum_list"))
UNION ALL
(SELECT 2, "t3"."report_year", "t1"."enum_list", COUNT("t1"."enum_list")
 FROM "cg_result" AS "t1"
          INNER JOIN "question" AS "t2" ON ("t2"."id" = "t1"."question_id")
          INNER JOIN "hkex_file_meta" AS "t3" ON ("t2"."fid" = "t3"."fid")
          INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t1"."id")
 WHERE ((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND
        (("tmp_ans"."rank" = 1) AND ("t3"."stock_code" ILIKE '08%')))
 GROUP BY "t3"."report_year", "t1"."enum_list");

SELECT count(f.id)
FROM file f
         JOIN hkex_file_meta h ON f.id = h.fid
WHERE 1 = ANY (f.mold_list)
  AND NOT (31 = ANY (f.mold_list))
  AND h.report_year = '2024';


SELECT count(f.id)
FROM file f
         JOIN hkex_file_meta h ON f.id = h.fid
WHERE 2 = ANY (f.mold_list)
  AND NOT (32 = ANY (f.mold_list))
  AND h.report_year = '2024';


\COPY
(SELECT f.id FROM file f JOIN hkex_file_meta h ON f.id = h.fid WHERE 2 = ANY (f.mold_list) AND NOT (32 = ANY (f.mold_list)) AND h.report_year = '2024') TO '/tmp/2024_2_fids';
\COPY
(SELECT f.id FROM file f JOIN hkex_file_meta h ON f.id = h.fid WHERE 1 = ANY (f.mold_list) AND NOT (31 = ANY (f.mold_list)) AND h.report_year = '2024') TO '/tmp/2024_1_fids';


explain analyse
WITH "aggregated_results" AS not MATERIALIZED (SELECT "t1"."file_id",
                                                      json_object_agg("t1"."compliance_answer_key",
                                                                      "t1"."compliance_value") AS "compliance_data"
                                               FROM "agm_result" AS "t1"
                                               WHERE ("t1"."mold_id" = 33)
                                               GROUP BY "t1"."file_id")
SELECT "t2"."fid",
       "t3"."id"                              AS "qid",
       "t3"."mold"                            AS "mold_id",
       "t2"."stock_code",
       "t2"."name"                            AS "company_name",
       "t4"."name"                            AS "title",
       "t4"."headline",
       "t4"."release_time",
       "aggregated_results"."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t4" ON (("t2"."fid" = "t4"."fid") AND ("t4"."type" = 'AGM'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t2"."fid")
         INNER JOIN "question" AS "t3" ON (("t5"."id" = "t3"."fid") AND ("t3"."mold" = 33))
         LEFT OUTER JOIN "aggregated_results" ON ("t2"."fid" = "aggregated_results"."file_id")
WHERE ((((("t2"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t3"."deleted_utc" = 0)) AND (("t2"."doc_type" = 30) AND ("t2"."report_year" IN ('2025'))))
ORDER BY "t2"."published" DESC


--prod  by-rule
SELECT count("t1"."fid")
FROM "hkex_file_meta" AS "t1"
         LEFT OUTER JOIN "poll_result" AS "t2" ON ("t2"."file_id" = "t1"."fid")
WHERE ((((("t1"."deleted_utc" = 0) AND ("t2"."rule_reference_id" = 2955)) AND
         (CAST("t1"."report_year" AS int) >= 2025)) AND (CAST("t1"."report_year" AS int) <= 2025)) AND
       ("t1"."doc_type" = 31));

--prod  dashboard

WITH "aggregated_results"
         AS NOT MATERIALIZED (SELECT "t1"."file_id",
                                     json_object_agg("t1"."compliance_answer_key",
                                                     "t1"."compliance_value") AS "compliance_data"
                              FROM "agm_result" AS "t1"
                              WHERE ("t1"."mold_id" = 34)
                              GROUP BY "t1"."file_id")
SELECT count("t2"."fid")
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t4" ON (("t2"."fid" = "t4"."fid") AND ("t4"."type" = 'POLL'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t2"."fid")
         INNER JOIN "question" AS "t3" ON (("t5"."id" = "t3"."fid") AND ("t3"."mold" = 34))
         LEFT OUTER JOIN "aggregated_results" ON ("t2"."fid" = "aggregated_results"."file_id")
WHERE ((((("t2"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t3"."deleted_utc" = 0)) AND (("t2"."doc_type" = 31)) and
       ((CAST("t2"."report_year" AS int) >= 2025) AND (CAST("t2"."report_year" AS int) <= 2025)));



(SELECT "t1"."fid"
 FROM "hkex_file_meta" AS "t1"
          LEFT OUTER JOIN "poll_result" AS "t2" ON ("t2"."file_id" = "t1"."fid")
 WHERE ((((("t1"."deleted_utc" = 0) AND ("t2"."rule_reference_id" = 2955)) AND
          (CAST("t1"."report_year" AS int) >= 2025)) AND (CAST("t1"."report_year" AS int) <= 2025)) AND
        ("t1"."doc_type" = 31)))
except
(WITH "aggregated_results"
          AS NOT MATERIALIZED (SELECT "t1"."file_id",
                                      json_object_agg("t1"."compliance_answer_key",
                                                      "t1"."compliance_value") AS "compliance_data"
                               FROM "agm_result" AS "t1"
                               WHERE ("t1"."mold_id" = 34)
                               GROUP BY "t1"."file_id")
 SELECT "t2"."fid"
 FROM "hkex_file_meta" AS "t2"
          INNER JOIN "hkex_file" AS "t4"
                     ON (("t2"."fid" = "t4"."fid") AND ("t4"."type" = 'POLL'))
          INNER JOIN "file" AS "t5" ON ("t5"."id" = "t2"."fid")
          INNER JOIN "question" AS "t3" ON (("t5"."id" = "t3"."fid") AND ("t3"."mold" = 34))
          LEFT OUTER JOIN "aggregated_results" ON ("t2"."fid" = "aggregated_results"."file_id")
 WHERE ((((("t2"."deleted_utc" = 0)
     AND ("t4"."deleted_utc" = 0))
     AND ("t5"."deleted_utc" = 0))
     AND
         ("t3"."deleted_utc" = 0))
     AND (("t2"."doc_type" = 31))
     and
        ((CAST("t2"."report_year" AS int) >= 2025)
            AND (CAST("t2"."report_year" AS int) <= 2025))))


WITH "aggregated_results" AS NOT MATERIALIZED (SELECT "t1"."file_id",
                                                      json_object_agg("t1"."compliance_answer_key",
                                                                      "t1"."compliance_value") AS "compliance_data"
                                               FROM "agm_result" AS "t1"
                                               WHERE ("t1"."mold_id" = 34)
                                               GROUP BY "t1"."file_id")
SELECT "t2"."fid"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t4" ON (("t2"."fid" = "t4"."fid") AND ("t4"."type" = 'POLL'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t2"."fid")
         INNER JOIN "question" AS "t3" ON (("t5"."id" = "t3"."fid") AND ("t3"."mold" = 34))
         LEFT OUTER JOIN "aggregated_results" ON ("t2"."fid" = "aggregated_results"."file_id")
WHERE ((((("t2"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t3"."deleted_utc" = 0)) AND (("t2"."doc_type" = 31)) and
       ((CAST("t2"."report_year" AS int) >= 2025) AND (CAST("t2"."report_year" AS int) <= 2025)))
WITH "aggregated_results" AS NOT MATERIALIZED (SELECT "t1"."file_id",
                                                      json_object_agg("t1"."compliance_answer_key",
                                                                      "t1"."compliance_value") AS "compliance_data"
                                               FROM "agm_result" AS "t1"
                                               WHERE ("t1"."mold_id" = 34)
                                               GROUP BY "t1"."file_id")
SELECT "t2"."fid", COUNT(*) as occurrence_count
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t4" ON (("t2"."fid" = "t4"."fid") AND ("t4"."type" = 'POLL'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t2"."fid")
         INNER JOIN "question" AS "t3" ON (("t5"."id" = "t3"."fid") AND ("t3"."mold" = 34))
         LEFT OUTER JOIN "aggregated_results" ON ("t2"."fid" = "aggregated_results"."file_id")
WHERE ((((("t2"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t3"."deleted_utc" = 0)) AND (("t2"."doc_type" = 31)) AND
       ((CAST("t2"."report_year" AS int) >= 2025) AND (CAST("t2"."report_year" AS int) <= 2025)))
GROUP BY "t2"."fid"
HAVING COUNT(*) > 1
ORDER BY occurrence_count DESC WITH "aggregated_results" AS MATERIALIZED (SELECT "t1"."file_id",
                                                  json_object_agg("t1"."compliance_answer_key",
                                                                  "t1"."compliance_value") AS "compliance_data"
                                           FROM "poll_result" AS "t1"
                                           WHERE ("t1"."mold_id" = 34)
                                           GROUP BY "t1"."file_id")
SELECT "t2"."fid",
       "t4"."url",
       "t4"."release_time",
       "t2"."published",
       "aggregated_results"."compliance_data" AS "stat_res"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t4" ON (("t2"."fid" = "t4"."fid") AND ("t4"."type" = 'POLL'))
         INNER JOIN "file" AS "t5" ON ("t5"."id" = "t2"."fid")
         INNER JOIN "question" AS "t3" ON (("t5"."id" = "t3"."fid") AND ("t3"."mold" = 34))
         LEFT OUTER JOIN "aggregated_results" ON ("t2"."fid" = "aggregated_results"."file_id")
WHERE ((((("t2"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t5"."deleted_utc" = 0)) AND
        ("t3"."deleted_utc" = 0)) AND
       ((("t2"."doc_type" = 31) AND ("t2"."stock_code" = '01962')) AND ("t4"."release_time" >= 1741871612)))
ORDER BY "t2"."published" DESC, "t4"."id" DESC


select count(distinct q.id)
from question q
         join file_esg_xref fex on q.fid = fex.fid
         join hkex_file_meta hm on q.fid = hm.fid
where hm.report_year = '2024'
  and q.mold in (31, 32)
  and fex.activated
  and q.updated_utc < 1749815167;


select stock_code, report_year
from hkex_file_meta
where doc_type = 1
  and stock_code in
      ('08078', '00273', '02118', '00250', '00530', '00906', '06090', '08607', '00359', '02148', '00715', '08228',
       '00680', '08045', '03636', '00342', '01266', '01381', '01469', '01902', '07801', '08353')
  and report_year > '2019'
  and deleted_utc > 0;

select report_year || ' ' || stock_code as result
from hkex_file_meta
where doc_type = 1
  and stock_code in
      ('08078', '00273', '02118', '00250', '00530', '00906', '06090', '08607', '00359', '02148', '00715', '08228',
       '00680', '08045', '03636', '00342', '01266', '01381', '01469', '01902', '07801', '08353')
  and report_year = '2024'
  and deleted_utc > 0
order by stock_code;


select fid, stock_code, release_time, headline, name, url
from hkex_file
where name ilike '%supplemental%'
  and type = 'AGM'

select q.fid
from question q
         join file_esg_xref fex on q.fid = fex.fid
         join hkex_file_meta hm on q.fid = hm.fid
where hm.report_year = '2024'
  and q.mold in (31, 32)
  and fex.activated
  and hm.deleted_utc = 0
  and q.updated_utc < 1749815167


select hkex_file_meta.fid, stock_code
from hkex_file_meta
         join file_esg_xref fex on hkex_file_meta.fid = fex.fid
where fex.activated
  and hkex_file_meta.deleted_utc != 0
  and hkex_file_meta.doc_type in (1, 2)
  and hkex_file_meta.report_year = '2024'
  and stock_code in
      ('08078', '00273', '02118', '00250', '00530', '00906', '06090', '08607', '00359', '02148', '00715', '08228',
       '00680', '08045', '03636', '00342', '01266', '01381', '01469', '01902', '07801', '08353')

select q.fid, stock_code, to_timestamp(q.updated_utc)
from hkex_file_meta
         join question q on hkex_file_meta.fid = q.fid
where doc_type in (1, 2)
  and hkex_file_meta.report_year = '2020'
order by q.updated_utc desc
limit 1

SELECT q.fid                                                                                            as file_id,
       REGEXP_REPLACE(REGEXP_REPLACE(REGEXP_REPLACE(qa.field, ':.*$', ''), '^E(\\d+)', 'T\1'), '-', '') as rule,
       LOWER(CASE
                 WHEN qa.value LIKE '[\"_%\"]' THEN REPLACE(REPLACE(qa.value, '[\"', ''), '\"]', '')
                 ELSE qa.value END)                                                                     as label_value,
       fm.stock_code                                                                                    as stock_code,
       fm.report_year                                                                                   as report_year,
       concat('http://100.64.0.105:55647/#/project/remark/', q.id, '?treeId=', file.tree_id, '&fileId=', q.fid,
              '&schemaId=18&projectId=', file.pid, '&schemaKey=C2.4')                                   as url,
       ad.name                                                                                          as user_name
FROM (SELECT a.qid,
             a.uid,
             (item ->> 'key')::jsonb ->> 1 as field,
             (item ->> 'key')::jsonb ->> 2 as words,
             item ->> 'value'              AS value
      FROM (SELECT qid, uid, item
            FROM answer,
                 json_array_elements(data -> 'userAnswer' -> 'items') item
            where deleted_utc = 0) a) qa
         JOIN question q ON q.id = qa.qid
         JOIN file ON file.id = q.fid
         JOIN hkex_file_meta fm ON fm.fid = q.fid
         JOIN admin_user ad
              ON qa.uid = ad.id and fm.report_year in ('2024') and q.mold = 18 and qa.field like '%C2.4%' and
                 qa.value ilike '%no disclosure%'

select id, uid, qid, key_value, updated_utc
from (select id, uid, qid, json_array_elements(data - > 'userAnswer' - > 'items') ->> 'key' AS key_value, updated_utc
      from answer
      where qid in (404853, 411399))
where key_value like '%B83.1%'
   or key_value like '%B73.1%'
order by qid desc, updated_utc desc;

select fid
from file_esg_xref
where fid in
      (155590, 99159, 98949, 158227, 156697, 157478, 166147, 157862, 157327, 98744, 157349, 165932, 158313, 155173,
       167611, 167535, 157437, 165973, 99301, 156953, 158265, 99250, 166772, 166192, 165927, 99391, 99386, 158659,
       158118, 156957, 157420, 157406, 156870, 99539, 99241, 158647, 157265, 99555, 157218, 157107, 166215, 98777,
       155168, 167377, 167274, 99553, 165846, 167562, 166545, 98724, 98613, 166940, 157177, 98534, 158069, 165840,
       167365, 98633, 99240, 158540, 158503, 99462, 167548, 158448, 157061, 99019, 98836, 155682, 157321, 98748, 167550,
       166656, 166142, 157115, 98924, 158219, 158249, 165836, 166520, 166782, 158554, 99158, 158235, 156684, 99478,
       166171, 157338, 98725, 99586, 165956, 158305, 99453, 99598, 99299, 158315, 165953, 99389, 99383, 158680, 99274,
       157412, 156872, 99538, 157267, 98780, 99492, 167385, 167276, 165845, 167233, 157175, 165853, 98634, 99242,
       158501, 99459, 167589, 157063, 99024, 98837, 155681, 167531, 166704, 99270, 98938, 158213, 166521, 166809)
  and activated

update hkex_file_meta
set year_end='31 Mar 2025'
where fid in (168713, 169263, 168609, 172254, 171917);


delete
from special_answer
where qid = 406097;
delete
from disclosure_result
where fid = 169916;
update hkex_file_meta
set deleted_utc=1
where fid = 169916;


WITH "cte" AS (SELECT "t1"."fid",
                      ROW_NUMBER()
                      OVER (PARTITION BY "t1"."report_year", "t1"."doc_type" ORDER BY "t1"."published" DESC) AS "rank"
               FROM "hkex_file_meta" AS "t1"
                        INNER JOIN "file_esg_xref" AS "t2" ON (("t2"."fid" = "t1"."fid") AND "t2"."activated")
               WHERE (("t1"."deleted_utc" = 0) AND (((("t1"."stock_code" = '00002') AND ("t1"."doc_type" IN (1, 2))) AND
                                                     ("t1"."report_year" >= '2020')) AND
                                                    ("t1"."report_year" = '2024'))))
SELECT "t3"."fid",
       "t3"."stock_code",
       "t4"."name"                                                           AS "title",
       "t4"."headline",
       "t4"."url"                                                            AS "hkex_url",
       "t3"."report_year",
       "t5"."company_name_en"                                                AS "company_name",
       "t5"."team_id"                                                        AS "team",
       TO_CHAR(CAST("t3"."published" AS timestamp), 'YYYY/MM/DD HH24:MI:SS') AS "release_time"
FROM "hkex_file_meta" AS "t3"
         INNER JOIN "hkex_file" AS "t4" ON ("t3"."fid" = "t4"."fid")
         INNER JOIN "hkex_companies_info" AS "t5" ON ("t3"."stock_code" = "t5"."stock_code")
         INNER JOIN "cte" ON ("cte"."fid" = "t3"."fid")
WHERE ((("t3"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("cte"."rank" = 1))
ORDER BY CAST("t3"."report_year" AS integer) DESC

SELECT "t1"."id" AS "qid",
       "t1"."fid",
       "t2"."tree_id",
       "t3"."stock_code",
       "t3"."report_year"
FROM "question" AS "t1"
         INNER JOIN "file" AS "t2" ON ("t2"."id" = "t1"."fid")
         INNER JOIN "hkex_file_meta" AS "t3" ON ("t2"."id" = "t3"."fid")
WHERE ((((("t1"."deleted_utc" = 0) AND ("t2"."deleted_utc" = 0)) AND ("t3"."deleted_utc" = 0)) AND
        ("t3"."report_year" IN ('2023', '2024'))) AND ("t1"."mold" IN (5, 15)))
  and t3.stock_code = '00831';


select id
from file
where 38 = any (mold_list)
  and 33 = any (mold_list);

--  updata file mold_list {33,38} to {34, 38}
update file
set mold_list = array_replace(mold_list, 33, 34)
where id in (select id from file where 38 = any (mold_list) and 33 = any (mold_list));

update question
set mold = 34
where fid in
      (157033, 157032, 157034, 157031, 157029, 157025, 157026, 157027, 157028, 157030, 170909, 170915, 170956, 170972,
       170958, 170969)
  and mold = 33;

select fid, mold
from question
where fid in
      (157033, 157032, 157034, 157031, 157029, 157025, 157026, 157027, 157028, 157030, 170909, 170915, 170956, 170972,
       170958, 170969);

\copy
(select h.fid as fid from hkex_file_meta h inner join file_esg_xref x on h.fid = x.fid where x.activated and h.doc_type = 2 and h.report_year = '2024')  to /tmp/2024_indent_esg_fids
\copy (select h.fid as fid from hkex_file_meta where doc_type in (11,12,13,14) and h.report_year = '2024')  to /tmp/2024_ar_fids


select fid, stock_code, report_year
from hkex_file_meta
where doc_type in (1, 2)
  and report_year = '2024'
  and stock_code in
      ('09988', '00700', '03690', '09992', '01810', '09636', '01024', '01177', '03896', '01788', '00981', '09618',
       '01211', '00020', '09888', '02587', '02020', '00175', '02318', '00939', '09961', '09926', '02517', '06181',
       '00241', '02172', '01299', '00268', '03988', '00388', '01801', '09626', '02269', '01398', '00992', '00005',
       '02186', '09999', '09660', '01093', '09995', '03888', '06855', '02015', '03750', '01347', '06682', '01530',
       '01357', '06160', '02382', '00856', '09868', '02556', '00016', '06618', '00914', '02628', '09878', '06969',
       '06862', '01428', '00288', '00941', '09698', '00669', '00027', '09969', '03968', '06680', '00762', '06683',
       '00780', '01288', '06099', '06060', '01988', '03800', '01675', '09880', '02367', '00434', '01088', '00968',
       '00883', '01918', '00308', '02550', '00386', '06955', '06869', '09688', '02228', '03347', '00857', '00425',
       '01513', '01919', '01276', '02157', '09901', '02331', '01109', '03933', '01341', '01375', '00285', '00467',
       '01336', '03908', '00688', '01359', '02319', '01548', '01709', '01208', '00267', '01060', '00788', '00291',
       '09863', '00165', '01272', '01456', '02388', '03759', '01952', '02616', '09633', '02359', '01519', '02899',
       '06690', '02328', '00763', '02268', '01364', '01378', '02453', '01928', '06693', '00012', '01658', '03606',
       '00836', '00998', '01833', '03990', '00001', '09866', '00354', '00772', '00728', '02313', '01772', '02611',
       '01877', '00551', '06881', '06127', '00013', '00512', '01304', '06030', '01113', '03328', '02522', '01339',
       '09899', '02423', '03996', '01818', '03692', '03993', '02050', '00960', '00011', '00003', '09959', '09858',
       '01171', '03380', '02598', '00909', '00136', '00853', '02552', '02158', '00006', '02202', '01898', '02013',
       '02057', '01816', '00297', '02888', '02162', '09896', '01209', '02097', '00314', '02018', '02469', '02799',
       '00489', '00345', '01860', '09922', '00917', '02600', '09985', '09987', '00376', '00002', '06806', '02688',
       '02357', '01797', '00966', '02068', '06088', '09923', '02498', '06990', '00390', '01686', '06613', '00460',
       '02252', '06186', '01951', '00863', '01766', '00568', '01263', '02255', '01328', '02533', '02400', '03678',
       '00881', '06936', '02460', '00316', '00697', '01896', '01033', '02105', '06086', '00300', '02577', '02432',
       '06196', '01053', '00880', '02601', '00200', '01112', '01164', '06178', '00590', '00189', '02510', '06069',
       '00179', '09885', '03681', '01099', '03288', '01055', '01558', '06066', '01888', '00596', '02333', '09696',
       '03738', '00604', '00868', '02507', '00168', '01478', '00867', '06168', '00991', '00066', '06886', '01128',
       '01274', '02282', '02386', '03958', '00636', '01681', '01405', '00325', '02155', '01789', '01776', '00719',
       '01515', '01302', '00322', '02727', '01787', '01038', '00358', '00819', '02419', '02096', '00522', '02431',
       '01929', '01318', '00916', '01193', '03900', '01415', '01316', '01126', '02618', '06078', '02007', '01138',
       '06823', '06603', '01258', '02696', '02722', '06818', '06055', '09890', '02273', '03311', '01651', '02443',
       '02465', '03320', '02196', '02338', '01157', '00817', '03998', '02038', '06185', '02171', '06098', '01477',
       '00753', '01385', '02218', '01876', '02506', '01057', '02669', '00902', '01606', '01880', '02276', '01997',
       '01698', '01114', '03880', '01044', '02588', '02016', '09860', '01999', '00207', '00323', '01196', '00558',
       '02590', '00535', '09678', '01729', '00412', '00317', '09676', '01735', '06049', '02866', '03323', '01611',
       '01972', '00839', '00347', '00123', '01308', '00553', '02508', '01179', '01969', '00256', '01030', '00667',
       '00581', '02128', '06100', '01071', '00017', '01579', '00990', '00101', '06808', '02666', '06699', '01610',
       '00588', '01167', '00570', '02245', '01368', '06865', '01066', '01361', '00303', '03360', '00694', '00019',
       '00586', '06821', '02858', '09606', '02099', '02777', '00135', '01186', '02302', '01349', '01828', '02238',
       '00887', '01981', '03898', '02285', '02192', '01635', '03396', '02233', '00151', '03339', '03899', '01910',
       '01913', '03877', '01108', '03393', '09993', '03918', '02343', '02380', '02208', '01277', '00777', '06963',
       '00921', '00270', '02142', '09966', '03939', '03618', '02555', '09699', '00187', '00826', '02607', '06626',
       '01811', '01133', '00090', '00113', '09939', '01672', '01855', '09997', '09898', '01907', '00754', '09996',
       '00327', '02477', '02429', '01773', '09911', '06616', '03808', '02005', '00631', '03868', '02291', '00895',
       '02592', '06979', '01585', '00815', '00384', '08540', '02602', '02145', '01541', '01958', '00564', '00579',
       '00511', '01836', '02585', '01939', '02643', '81211', '09955', '00696', '00371', '01330', '01499', '02459',
       '89888', '03319', '00743', '00340', '02314', '01691', '00081', '00370', '89988', '02440', '01908', '01920',
       '00799', '06110', '02883', '02150', '08219', '02648', '02256', '00517', '00107', '00392', '00293', '00565',
       '01508', '02378', '02869', '01448', '02865', '01520', '08087', '00119', '00874', '02473', '00710', '01911',
       '02009', '00010', '01666', '01963', '01800', '00218', '02603', '01921', '08406', '00666', '00220', '06826',
       '01310', '00831', '02629', '02076', '02660', '00144', '00956', '01516', '02488', '00670', '01117', '09989',
       '00087', '02511', '06831', '01995', '03931', '00656', '03316', '02306', '06198', '00357', '08299', '00975',
       '03329', '00813', '00639', '03668', '00552', '02160', '02209', '00440', '02562', '00806', '00148', '01199',
       '80992', '00400', '00842', '00217', '00527', '00257', '00337', '06660', '01083', '03638', '00999', '01618',
       '08017', '01070', '03306', '02342', '02569', '00855', '08483', '01640', '06058', '00133', '02565', '01432',
       '01858', '00014', '02126', '00884', '01799', '00548', '00363', '80700', '03330', '00683', '02410', '01401',
       '00751', '01762', '00177', '00659', '08083', '01122', '00142', '02689', '06608', '01812', '01733', '09936',
       '02185', '02121', '02877', '03613', '03677', '02373', '01866', '01072', '02563', '00152', '08198', '01890',
       '03848', '00861', '03669', '03969', '01228', '01061', '06978', '00004', '01783', '02360', '03698', '02589',
       '01003', '01949', '00338', '02570', '01168', '00008', '09658', '01883', '01440', '00580', '09977', '01119',
       '80175', '09639', '01247', '02232', '02356', '01313', '06655', '03709', '00336', '00023', '00752', '01882',
       '01712', '03369', '01142', '00576', '03650', '06698', '03337', '01802', '02880', '00033', '06628', '00215',
       '02619', '03377', '09690', '01571', '00698', '82318', '00525', '02188', '02638', '01373', '80388', '01223',
       '83690', '01333', '01244', '01539', '01643', '02500', '02190', '02149', '02411', '00290', '00173', '01798',
       '06820', '01458', '03686', '06996', '01051', '03383', '00716', '01901', '02495', '00069', '06669', '01647',
       '02309', '01086', '02166', '06676', '06883', '09979', '00227', '02337', '03788', '02100', '02279', '09879',
       '00866', '01747', '02651', '06622', '02503', '01769', '01140', '01726', '03866', '01903', '00621', '01412',
       '00995', '02001', '02416', '00818', '02597', '02250', '02617', '01065', '03600', '01050', '02609', '08239',
       '02086', '02582', '00381', '00811', '03660', '02312', '00085', '02039', '01527', '01538', '01528', '09669',
       '02683', '00598', '01252', '00076', '01431', '06666', '01522', '02566', '02137', '01286', '03680', '03626',
       '00673', '80941', '01985', '00536', '01282', '00398', '06993', '00038', '08603', '03978', '01468', '03883',
       '02489', '00272', '00264', '00242', '00302', '01355', '01683', '02516', '01283', '08292', '02490', '01141',
       '81024', '09668', '02325', '02402', '01931', '00951', '00493', '01723', '01203', '00486', '00905', '00717',
       '00582', '09968', '00451', '02399', '01023', '01745', '00520', '00031', '02159', '01098', '01184', '02560',
       '09956', '81299', '81810', '01848', '02169', '02433', '01713', '01815', '00746', '09881', '01162', '01234',
       '01205', '02738', '09986', '00476', '06609', '00178', '02107', '06288', '00655', '00591', '01765', '01948',
       '06998', '08245', '00616', '02439', '01238', '00234', '02225', '08267', '01870', '02390', '00632', '00546',
       '03813', '01872', '02573', '02487', '03983', '02779', '00071', '00341', '02197', '02392', '00931', '00139',
       '02567', '01521', '00279', '00326', '01795', '02605', '01905', '00554', '08446', '00640', '08037', '02263',
       '01372', '02161', '01737', '03303', '08427', '00222', '09900', '02613', '01301', '00833', '01945', '08220',
       '09877', '02216', '00232', '06128', '00147', '00474', '02139', '02520', '00329', '02339', '00111', '03700',
       '00197', '03633', '02217', '02265', '00497', '00108', '01216', '82388', '02391', '00804', '01455', '01865',
       '03768', '00934', '01425', '02559', '00619', '03737', '03395', '00372', '01116', '01763', '00243', '00083',
       '01280', '02621', '00410', '02425', '00775', '01662', '01555', '01147', '03839', '08391', '00732', '00816',
       '00196', '01697', '00900', '00709', '08217', '09930', '02283', '01830', '02502', '01636', '00418', '00932',
       '02455', '00471', '02596', '06882', '00116', '80011', '02728', '03718', '06138', '00583', '01627', '01692',
       '00210', '08189', '00926', '01786', '00613', '00618', '02499', '01979', '01176', '01716', '01970', '08179',
       '03368', '02505', '00838', '02633', '08082', '00163', '00898', '06601', '00693', '08645', '08136', '00301',
       '01180', '00103', '08137', '02561', '02531', '01862', '08158', '02219', '08206', '01273', '00993', '00603',
       '01397', '06681', '00875', '00563', '01878', '01576', '09869', '00052', '08473', '09666', '00305', '00034',
       '01200', '01429', '00830', '01145', '08139', '00078', '00022', '01556', '01760', '09928', '06118', '00721',
       '01950', '00332', '02415', '02678', '01856', '00745', '09933', '03301', '01676', '00040', '00690', '01719',
       '08043', '02999', '00771', '09889', '01753', '02405', '01351', '02529', '01111', '02376', '02530', '01638',
       '00807', '01215', '01213', '03836', '01588', '00712', '01498', '00126', '02317', '08223', '00464', '02901',
       '00858', '06158', '08629', '00356', '00575', '03833', '00276', '01580', '01717', '06108', '02025', '09919',
       '00547', '08370', '00556', '08066', '08117', '01725', '00907', '00193', '00095', '01241', '01930', '01165',
       '00353', '09958', '01809', '00681', '01052', '02593', '06696', '01837', '03390', '02368', '02377', '02610',
       '01696', '02033', '02486', '80020', '00373', '00637', '00587', '08271', '08041', '00265', '02281', '01591',
       '03773', '01947', '00465', '02211', '00888', '01873', '02680', '03830', '00980', '00330', '01927', '01195',
       '03336', '00184', '06828', '02518', '01756', '03639', '00623', '00182', '03798', '00747', '01600', '01526',
       '06036', '00433', '01268', '01623', '01449', '00515', '01000', '00893', '01938', '02450', '01371', '02383',
       '08283', '02298', '01115', '03309', '02235', '00873', '00432', '08295', '01271', '00211', '03838', '08040',
       '02156', '08305', '02347', '00809', '01433', '01497', '02175', '06093', '01966', '01450', '03623', '08311',
       '01718', '06639', '01912', '08057', '00055', '08187', '01685', '06929', '08418', '02165', '00075', '01581',
       '08131', '00366', '00419', '01380', '00952', '00810', '01148', '01617', '00521', '00397', '00953', '03997',
       '00658', '00730', '06988', '01327', '08279', '06638', '02903', '08262', '00077', '01986', '00480', '09916',
       '01343', '00455', '06911', '03913', '00829', '00064', '00538', '00832', '06830', '02900', '02012', '01229',
       '03886', '01259', '00987', '08320', '00206', '00422', '01778', '00500', '01250', '01183', '01091', '06133',
       '00825', '01831', '01132', '00360', '02358', '00312', '01312', '00120', '02122', '00166', '06968', '00896',
       '00007', '00009', '00018', '00021', '00025', '00026', '00028', '00029', '00030', '00032', '00035', '00036',
       '00037', '00039', '00041', '00042', '00045', '00046', '00048', '00050', '00051', '00053', '00057', '00058',
       '00059', '00060', '00061', '00062', '00063', '00065', '00070', '00072', '00073', '00079', '00080', '00082',
       '00084', '00086', '00088', '00089', '00092', '00093', '00094', '00096', '00097', '00098', '00099', '00102',
       '00104', '00105', '00106', '00110', '00114', '00115', '00117', '00118', '00122', '00124', '00125', '00127',
       '00128', '00129', '00130', '00131', '00132', '00137', '00138', '00145', '00146', '00149', '00150', '00154',
       '00156', '00157', '00158', '00159', '00160', '00162', '00164', '00167', '00169', '00171', '00174', '00176',
       '00180', '00181', '00183', '00185', '00186', '00188', '00191', '00194', '00195', '00199', '00201', '00202',
       '00204', '00205', '00209', '00212', '00213', '00214', '00216', '00219', '00223', '00224', '00225', '00226',
       '00228', '00229', '00230', '00235', '00236', '00237', '00239', '00240', '00244', '00245', '00247', '00248',
       '00251', '00252', '00253', '00254', '00255', '00258', '00259', '00261', '00262', '00266', '00269', '00271',
       '00274', '00277', '00280', '00286', '00287', '00289', '00294', '00295', '00296', '00298', '00299', '00306',
       '00309', '00310', '00311', '00313', '00315', '00318', '00320', '00321', '00328', '00331', '00333', '00334',
       '00335', '00339', '00343', '00346', '00348', '00351', '00352', '00355', '00361', '00362', '00365', '00367',
       '00368', '00369', '00374', '00375', '00377', '00379', '00380', '00382', '00383', '00385', '00387', '00389',
       '00391', '00393', '00396', '00399', '00401', '00403', '00406', '00408', '00411', '00413', '00417', '00420',
       '00423', '00426', '00428', '00430', '00431', '00436', '00438', '00439', '00442', '00444', '00450', '00456',
       '00458', '00459', '00468', '00472', '00475', '00482', '00483', '00484', '00485', '00487', '00488', '00491',
       '00495', '00496', '00498', '00499', '00505', '00506', '00508', '00509', '00510', '00513', '00518', '00519',
       '00524', '00526', '00528', '00529', '00532', '00533', '00540', '00542', '00543', '00544', '00550', '00557',
       '00559', '00560', '00567', '00571', '00572', '00573', '00574', '00585', '00589', '00593', '00595', '00599',
       '00601', '00602', '00605', '00606', '00607', '00608', '00609', '00610', '00611', '00612', '00617', '00620',
       '00622', '00626', '00627', '00628', '00629', '00630', '00635', '00641', '00643', '00645', '00646', '00648',
       '00650', '00653', '00657', '00660', '00661', '00662', '00663', '00672', '00674', '00675', '00676', '00677',
       '00679', '00682', '00684', '00685', '00686', '00687', '00689', '00691', '00695', '00701', '00702', '00703',
       '00704', '00707', '00708', '00711', '00713', '00718', '00720', '00722', '00723', '00724', '00725', '00726',
       '00727', '00731', '00733', '00736', '00737', '00738', '00750', '00755', '00756', '00757', '00759', '00760',
       '00764', '00765', '00767', '00768', '00769', '00770', '00776', '00784', '00789', '00794', '00797', '00798',
       '00802', '00805', '00812', '00814', '00821', '00822', '00827', '00828', '00834', '00837', '00840', '00841',
       '00844', '00845', '00846', '00848', '00851', '00852', '00854', '00859', '00860', '00862', '00864', '00865',
       '00869', '00871', '00876', '00878', '00882', '00889', '00894', '00897', '00899', '00910', '00911', '00912',
       '00913', '00915', '00918', '00919', '00922', '00923', '00924', '00925', '00927', '00928', '00929', '00933',
       '00936', '00938', '00943', '00945', '00947', '00948', '00950', '00954', '00959', '00969', '00970', '00974',
       '00976', '00978', '00979', '00983', '00984', '00986', '00989', '00994', '00997', '01001', '01002', '01004',
       '01005', '01007', '01008', '01009', '01010', '01011', '01013', '01020', '01022', '01025', '01026', '01027',
       '01028', '01029', '01034', '01036', '01037', '01039', '01045', '01046', '01047', '01049', '01058', '01059',
       '01062', '01063', '01064', '01068', '01069', '01073', '01075', '01079', '01080', '01082', '01084', '01085',
       '01087', '01090', '01094', '01097', '01100', '01101', '01102', '01104', '01105', '01107', '01110', '01118',
       '01120', '01121', '01123', '01124', '01125', '01127', '01129', '01130', '01134', '01137', '01143', '01146',
       '01150', '01152', '01153', '01156', '01159', '01160', '01161', '01163', '01166', '01170', '01172', '01173',
       '01181', '01182', '01185', '01188', '01189', '01198', '01201', '01202', '01204', '01206', '01217', '01218',
       '01220', '01221', '01222', '01224', '01225', '01226', '01231', '01232', '01233', '01235', '01237', '01239',
       '01240', '01243', '01245', '01246', '01251', '01253', '01255', '01257', '01260', '01262', '01265', '01269',
       '01270', '01278', '01281', '01284', '01285', '01289', '01290', '01292', '01293', '01298', '01300', '01303',
       '01305', '01314', '01315', '01317', '01319', '01321', '01323', '01326', '01332', '01334', '01335', '01338',
       '01340', '01345', '01346', '01348', '01354', '01358', '01360', '01362', '01370', '01376', '01379', '01382',
       '01383', '01388', '01389', '01393', '01395', '01396', '01399', '01400', '01402', '01406', '01407', '01408',
       '01410', '01413', '01416', '01417', '01418', '01419', '01420', '01421', '01427', '01442', '01443', '01446',
       '01447', '01451', '01452', '01459', '01460', '01461', '01463', '01466', '01470', '01471', '01472', '01473',
       '01475', '01476', '01480', '01481', '01483', '01486', '01488', '01489', '01490', '01495', '01496', '01500',
       '01501', '01502', '01518', '01523', '01525', '01529', '01532', '01536', '01540', '01542', '01543', '01545',
       '01546', '01547', '01549', '01551', '01552', '01553', '01557', '01559', '01560', '01561', '01563', '01565',
       '01566', '01568', '01569', '01570', '01572', '01575', '01577', '01578', '01582', '01583', '01586', '01587',
       '01592', '01593', '01596', '01597', '01598', '01599', '01601', '01608', '01612', '01613', '01615', '01616',
       '01620', '01621', '01622', '01626', '01628', '01629', '01630', '01631', '01632', '01633', '01637', '01645',
       '01650', '01652', '01653', '01655', '01656', '01657', '01660', '01661', '01663', '01667', '01668', '01669',
       '01671', '01673', '01679', '01680', '01682', '01689', '01690', '01693', '01695', '01699', '01701', '01702',
       '01703', '01705', '01707', '01708', '01710', '01711', '01715', '01720', '01721', '01722', '01727', '01728',
       '01730', '01731', '01732', '01736', '01738', '01739', '01740', '01741', '01742', '01746', '01748', '01749',
       '01750', '01751', '01752', '01755', '01757', '01758', '01759', '01767', '01771', '01775', '01777', '01780',
       '01782', '01785', '01790', '01792', '01793', '01796', '01803', '01808', '01813', '01817', '01820', '01822',
       '01823', '01825', '01826', '01827', '01832', '01835', '01841', '01842', '01843', '01845', '01846', '01847',
       '01849', '01850', '01851', '01853', '01854', '01857', '01861', '01863', '01867', '01868', '01869', '01871',
       '01875', '01884', '01889', '01891', '01894', '01895', '01897', '01899', '01900', '01906', '01909', '01915',
       '01916', '01917', '01922', '01925', '01932', '01933', '01935', '01936', '01937', '01940', '01941', '01942',
       '01943', '01953', '01955', '01957', '01959', '01960', '01961', '01962', '01965', '01967', '01968', '01971',
       '01973', '01975', '01977', '01978', '01980', '01982', '01983', '01987', '01991', '01993', '01996', '02000',
       '02002', '02003', '02008', '02011', '02017', '02019', '02022', '02023', '02028', '02030', '02031', '02048',
       '02051', '02060', '02066', '02078', '02080', '02088', '02098', '02101', '02102', '02108', '02110', '02111',
       '02112', '02113', '02116', '02119', '02120', '02125', '02127', '02129', '02130', '02131', '02132', '02135',
       '02136', '02138', '02146', '02147', '02152', '02153', '02163', '02167', '02168', '02170', '02176', '02177',
       '02178', '02179', '02180', '02181', '02182', '02187', '02189', '02193', '02195', '02198', '02199', '02203',
       '02205', '02210', '02212', '02215', '02221', '02222', '02223', '02226', '02227', '02230', '02231', '02236',
       '02237', '02239', '02246', '02251', '02257', '02258', '02260', '02262', '02266', '02270', '02271', '02280',
       '02286', '02288', '02289', '02292', '02293', '02295', '02297', '02299', '02307', '02310', '02315', '02320',
       '02321', '02322', '02323', '02324', '02326', '02327', '02329', '02330', '02336', '02340', '02348', '02349',
       '02350', '02352', '02355', '02361', '02362', '02363', '02369', '02370', '02371', '02372', '02381', '02385',
       '02389', '02393', '02407', '02409', '02418', '02420', '02421', '02422', '02427', '02436', '02438', '02442',
       '02448', '02451', '02457', '02458', '02461', '02478', '02479', '02480', '02481', '02482', '02483', '02496',
       '02497', '02501', '02509', '02512', '02515', '02519', '02521', '02528', '02535', '02536', '02540', '02545',
       '02549', '02551', '02558', '02571', '02572', '02576', '02586', '02608', '02612', '02623', '02625', '02663',
       '02668', '02682', '02699', '02700', '02708', '02772', '02789', '02798', '02863', '02878', '02881', '02882',
       '02885', '02886', '02892', '02898', '02904', '02905', '02906', '02992', '02993', '02995', '02997', '02998',
       '03300', '03302', '03313', '03315', '03318', '03321', '03322', '03326', '03332', '03333', '03348', '03363',
       '03366', '03382', '03389', '03398', '03399', '03601', '03603', '03616', '03628', '03658', '03662', '03666',
       '03683', '03688', '03689', '03699', '03708', '03728', '03778', '03789', '03816', '03818', '03822', '03828',
       '03860', '03869', '03878', '03882', '03889', '03893', '03903', '03919', '03928', '03938', '03963', '03989',
       '03991', '03999', '04332', '04333', '04335', '04336', '04337', '04338', '04620', '04621', '04855', '06033',
       '06038', '06063', '06068', '06080', '06083', '06113', '06117', '06119', '06122', '06123', '06136', '06162',
       '06163', '06169', '06182', '06188', '06189', '06190', '06193', '06199', '06606', '06610', '06611', '06623',
       '06633', '06657', '06661', '06663', '06667', '06668', '06677', '06686', '06689', '06805', '06811', '06812',
       '06816', '06822', '06829', '06833', '06838', '06839', '06858', '06860', '06866', '06868', '06877', '06878',
       '06885', '06888', '06889', '06890', '06893', '06896', '06898', '06899', '06900', '06908', '06909', '06913',
       '06918', '06919', '06922', '06928', '06933', '06939', '06958', '06959', '06966', '06989', '06999', '07855',
       '08001', '08003', '08005', '08006', '08007', '08013', '08018', '08019', '08020', '08021', '08023', '08026',
       '08027', '08028', '08029', '08030', '08031', '08033', '08035', '08036', '08039', '08042', '08047', '08048',
       '08049', '08050', '08051', '08052', '08056', '08059', '08060', '08062', '08063', '08065', '08067', '08069',
       '08070', '08071', '08072', '08073', '08076', '08079', '08080', '08081', '08091', '08092', '08093', '08095',
       '08096', '08098', '08100', '08103', '08106', '08107', '08111', '08112', '08113', '08115', '08118', '08120',
       '08121', '08123', '08125', '08126', '08128', '08130', '08132', '08133', '08140', '08143', '08146', '08147',
       '08148', '08149', '08152', '08153', '08156', '08159', '08160', '08161', '08162', '08163', '08167', '08168',
       '08169', '08172', '08173', '08176', '08178', '08181', '08186', '08188', '08191', '08193', '08195', '08196',
       '08200', '08201', '08203', '08205', '08208', '08210', '08211', '08213', '08215', '08218', '08221', '08222',
       '08225', '08226', '08227', '08229', '08232', '08237', '08238', '08241', '08246', '08247', '08249', '08250',
       '08257', '08268', '08269', '08270', '08275', '08277', '08280', '08281', '08282', '08285', '08286', '08290',
       '08291', '08293', '08296', '08297', '08300', '08307', '08308', '08309', '08310', '08313', '08315', '08316',
       '08317', '08319', '08321', '08326', '08328', '08329', '08331', '08333', '08337', '08340', '08341', '08347',
       '08348', '08349', '08350', '08356', '08357', '08360', '08362', '08363', '08365', '08366', '08367', '08368',
       '08371', '08372', '08373', '08375', '08377', '08379', '08385', '08392', '08395', '08400', '08401', '08402',
       '08403', '08411', '08412', '08413', '08416', '08417', '08419', '08420', '08422', '08423', '08425', '08426',
       '08428', '08429', '08430', '08431', '08432', '08436', '08437', '08439', '08445', '08447', '08448', '08450',
       '08451', '08452', '08455', '08456', '08460', '08462', '08471', '08472', '08475', '08476', '08480', '08481',
       '08482', '08487', '08489', '08490', '08491', '08493', '08495', '08496', '08500', '08501', '08502', '08507',
       '08509', '08510', '08511', '08512', '08513', '08516', '08519', '08521', '08523', '08525', '08526', '08527',
       '08529', '08532', '08535', '08536', '08537', '08545', '08547', '08561', '08590', '08601', '08606', '08611',
       '08612', '08613', '08616', '08619', '08620', '08621', '08622', '08623', '08627', '08631', '08635', '08637',
       '08646', '08657', '08659', '08668', '09600', '09608', '09616', '09638', '09663', '09677', '09680', '09686',
       '09689', '09857', '09882', '09886', '09893', '09906', '09908', '09909', '09913', '09918', '09929', '09938',
       '09960', '09963', '09978', '09982', '09983', '09990', '09991', '09998', '80016', '80291', '80737', '80883',
       '82020', '82331', '82333', '86618', '89618')
select count(*)
from hkex_file_meta
         join file_esg_xref fex on hkex_file_meta.fid = fex.fid
where doc_type in (1)
  and report_year = '2024'
  and stock_code in
      ('09988', '00700', '03690', '09992', '01810', '09636', '01024', '01177', '03896', '01788', '00981', '09618',
       '01211', '00020', '09888', '02587', '02020', '00175', '02318', '00939', '09961', '09926', '02517', '06181',
       '00241', '02172', '01299', '00268', '03988', '00388', '01801', '09626', '02269', '01398', '00992', '00005',
       '02186', '09999', '09660', '01093', '09995', '03888', '06855', '02015', '03750', '01347', '06682', '01530',
       '01357', '06160', '02382', '00856', '09868', '02556', '00016', '06618', '00914', '02628', '09878', '06969',
       '06862', '01428', '00288', '00941', '09698', '00669', '00027', '09969', '03968', '06680', '00762', '06683',
       '00780', '01288', '06099', '06060', '01988', '03800', '01675', '09880', '02367', '00434', '01088', '00968',
       '00883', '01918', '00308', '02550', '00386', '06955', '06869', '09688', '02228', '03347', '00857', '00425',
       '01513', '01919', '01276', '02157', '09901', '02331', '01109', '03933', '01341', '01375', '00285', '00467',
       '01336', '03908', '00688', '01359', '02319', '01548', '01709', '01208', '00267', '01060', '00788', '00291',
       '09863', '00165', '01272', '01456', '02388', '03759', '01952', '02616', '09633', '02359', '01519', '02899',
       '06690', '02328', '00763', '02268', '01364', '01378', '02453', '01928', '06693', '00012', '01658', '03606',
       '00836', '00998', '01833', '03990', '00001', '09866', '00354', '00772', '00728', '02313', '01772', '02611',
       '01877', '00551', '06881', '06127', '00013', '00512', '01304', '06030', '01113', '03328', '02522', '01339',
       '09899', '02423', '03996', '01818', '03692', '03993', '02050', '00960', '00011', '00003', '09959', '09858',
       '01171', '03380', '02598', '00909', '00136', '00853', '02552', '02158', '00006', '02202', '01898', '02013',
       '02057', '01816', '00297', '02888', '02162', '09896', '01209', '02097', '00314', '02018', '02469', '02799',
       '00489', '00345', '01860', '09922', '00917', '02600', '09985', '09987', '00376', '00002', '06806', '02688',
       '02357', '01797', '00966', '02068', '06088', '09923', '02498', '06990', '00390', '01686', '06613', '00460',
       '02252', '06186', '01951', '00863', '01766', '00568', '01263', '02255', '01328', '02533', '02400', '03678',
       '00881', '06936', '02460', '00316', '00697', '01896', '01033', '02105', '06086', '00300', '02577', '02432',
       '06196', '01053', '00880', '02601', '00200', '01112', '01164', '06178', '00590', '00189', '02510', '06069',
       '00179', '09885', '03681', '01099', '03288', '01055', '01558', '06066', '01888', '00596', '02333', '09696',
       '03738', '00604', '00868', '02507', '00168', '01478', '00867', '06168', '00991', '00066', '06886', '01128',
       '01274', '02282', '02386', '03958', '00636', '01681', '01405', '00325', '02155', '01789', '01776', '00719',
       '01515', '01302', '00322', '02727', '01787', '01038', '00358', '00819', '02419', '02096', '00522', '02431',
       '01929', '01318', '00916', '01193', '03900', '01415', '01316', '01126', '02618', '06078', '02007', '01138',
       '06823', '06603', '01258', '02696', '02722', '06818', '06055', '09890', '02273', '03311', '01651', '02443',
       '02465', '03320', '02196', '02338', '01157', '00817', '03998', '02038', '06185', '02171', '06098', '01477',
       '00753', '01385', '02218', '01876', '02506', '01057', '02669', '00902', '01606', '01880', '02276', '01997',
       '01698', '01114', '03880', '01044', '02588', '02016', '09860', '01999', '00207', '00323', '01196', '00558',
       '02590', '00535', '09678', '01729', '00412', '00317', '09676', '01735', '06049', '02866', '03323', '01611',
       '01972', '00839', '00347', '00123', '01308', '00553', '02508', '01179', '01969', '00256', '01030', '00667',
       '00581', '02128', '06100', '01071', '00017', '01579', '00990', '00101', '06808', '02666', '06699', '01610',
       '00588', '01167', '00570', '02245', '01368', '06865', '01066', '01361', '00303', '03360', '00694', '00019',
       '00586', '06821', '02858', '09606', '02099', '02777', '00135', '01186', '02302', '01349', '01828', '02238',
       '00887', '01981', '03898', '02285', '02192', '01635', '03396', '02233', '00151', '03339', '03899', '01910',
       '01913', '03877', '01108', '03393', '09993', '03918', '02343', '02380', '02208', '01277', '00777', '06963',
       '00921', '00270', '02142', '09966', '03939', '03618', '02555', '09699', '00187', '00826', '02607', '06626',
       '01811', '01133', '00090', '00113', '09939', '01672', '01855', '09997', '09898', '01907', '00754', '09996',
       '00327', '02477', '02429', '01773', '09911', '06616', '03808', '02005', '00631', '03868', '02291', '00895',
       '02592', '06979', '01585', '00815', '00384', '08540', '02602', '02145', '01541', '01958', '00564', '00579',
       '00511', '01836', '02585', '01939', '02643', '81211', '09955', '00696', '00371', '01330', '01499', '02459',
       '89888', '03319', '00743', '00340', '02314', '01691', '00081', '00370', '89988', '02440', '01908', '01920',
       '00799', '06110', '02883', '02150', '08219', '02648', '02256', '00517', '00107', '00392', '00293', '00565',
       '01508', '02378', '02869', '01448', '02865', '01520', '08087', '00119', '00874', '02473', '00710', '01911',
       '02009', '00010', '01666', '01963', '01800', '00218', '02603', '01921', '08406', '00666', '00220', '06826',
       '01310', '00831', '02629', '02076', '02660', '00144', '00956', '01516', '02488', '00670', '01117', '09989',
       '00087', '02511', '06831', '01995', '03931', '00656', '03316', '02306', '06198', '00357', '08299', '00975',
       '03329', '00813', '00639', '03668', '00552', '02160', '02209', '00440', '02562', '00806', '00148', '01199',
       '80992', '00400', '00842', '00217', '00527', '00257', '00337', '06660', '01083', '03638', '00999', '01618',
       '08017', '01070', '03306', '02342', '02569', '00855', '08483', '01640', '06058', '00133', '02565', '01432',
       '01858', '00014', '02126', '00884', '01799', '00548', '00363', '80700', '03330', '00683', '02410', '01401',
       '00751', '01762', '00177', '00659', '08083', '01122', '00142', '02689', '06608', '01812', '01733', '09936',
       '02185', '02121', '02877', '03613', '03677', '02373', '01866', '01072', '02563', '00152', '08198', '01890',
       '03848', '00861', '03669', '03969', '01228', '01061', '06978', '00004', '01783', '02360', '03698', '02589',
       '01003', '01949', '00338', '02570', '01168', '00008', '09658', '01883', '01440', '00580', '09977', '01119',
       '80175', '09639', '01247', '02232', '02356', '01313', '06655', '03709', '00336', '00023', '00752', '01882',
       '01712', '03369', '01142', '00576', '03650', '06698', '03337', '01802', '02880', '00033', '06628', '00215',
       '02619', '03377', '09690', '01571', '00698', '82318', '00525', '02188', '02638', '01373', '80388', '01223',
       '83690', '01333', '01244', '01539', '01643', '02500', '02190', '02149', '02411', '00290', '00173', '01798',
       '06820', '01458', '03686', '06996', '01051', '03383', '00716', '01901', '02495', '00069', '06669', '01647',
       '02309', '01086', '02166', '06676', '06883', '09979', '00227', '02337', '03788', '02100', '02279', '09879',
       '00866', '01747', '02651', '06622', '02503', '01769', '01140', '01726', '03866', '01903', '00621', '01412',
       '00995', '02001', '02416', '00818', '02597', '02250', '02617', '01065', '03600', '01050', '02609', '08239',
       '02086', '02582', '00381', '00811', '03660', '02312', '00085', '02039', '01527', '01538', '01528', '09669',
       '02683', '00598', '01252', '00076', '01431', '06666', '01522', '02566', '02137', '01286', '03680', '03626',
       '00673', '80941', '01985', '00536', '01282', '00398', '06993', '00038', '08603', '03978', '01468', '03883',
       '02489', '00272', '00264', '00242', '00302', '01355', '01683', '02516', '01283', '08292', '02490', '01141',
       '81024', '09668', '02325', '02402', '01931', '00951', '00493', '01723', '01203', '00486', '00905', '00717',
       '00582', '09968', '00451', '02399', '01023', '01745', '00520', '00031', '02159', '01098', '01184', '02560',
       '09956', '81299', '81810', '01848', '02169', '02433', '01713', '01815', '00746', '09881', '01162', '01234',
       '01205', '02738', '09986', '00476', '06609', '00178', '02107', '06288', '00655', '00591', '01765', '01948',
       '06998', '08245', '00616', '02439', '01238', '00234', '02225', '08267', '01870', '02390', '00632', '00546',
       '03813', '01872', '02573', '02487', '03983', '02779', '00071', '00341', '02197', '02392', '00931', '00139',
       '02567', '01521', '00279', '00326', '01795', '02605', '01905', '00554', '08446', '00640', '08037', '02263',
       '01372', '02161', '01737', '03303', '08427', '00222', '09900', '02613', '01301', '00833', '01945', '08220',
       '09877', '02216', '00232', '06128', '00147', '00474', '02139', '02520', '00329', '02339', '00111', '03700',
       '00197', '03633', '02217', '02265', '00497', '00108', '01216', '82388', '02391', '00804', '01455', '01865',
       '03768', '00934', '01425', '02559', '00619', '03737', '03395', '00372', '01116', '01763', '00243', '00083',
       '01280', '02621', '00410', '02425', '00775', '01662', '01555', '01147', '03839', '08391', '00732', '00816',
       '00196', '01697', '00900', '00709', '08217', '09930', '02283', '01830', '02502', '01636', '00418', '00932',
       '02455', '00471', '02596', '06882', '00116', '80011', '02728', '03718', '06138', '00583', '01627', '01692',
       '00210', '08189', '00926', '01786', '00613', '00618', '02499', '01979', '01176', '01716', '01970', '08179',
       '03368', '02505', '00838', '02633', '08082', '00163', '00898', '06601', '00693', '08645', '08136', '00301',
       '01180', '00103', '08137', '02561', '02531', '01862', '08158', '02219', '08206', '01273', '00993', '00603',
       '01397', '06681', '00875', '00563', '01878', '01576', '09869', '00052', '08473', '09666', '00305', '00034',
       '01200', '01429', '00830', '01145', '08139', '00078', '00022', '01556', '01760', '09928', '06118', '00721',
       '01950', '00332', '02415', '02678', '01856', '00745', '09933', '03301', '01676', '00040', '00690', '01719',
       '08043', '02999', '00771', '09889', '01753', '02405', '01351', '02529', '01111', '02376', '02530', '01638',
       '00807', '01215', '01213', '03836', '01588', '00712', '01498', '00126', '02317', '08223', '00464', '02901',
       '00858', '06158', '08629', '00356', '00575', '03833', '00276', '01580', '01717', '06108', '02025', '09919',
       '00547', '08370', '00556', '08066', '08117', '01725', '00907', '00193', '00095', '01241', '01930', '01165',
       '00353', '09958', '01809', '00681', '01052', '02593', '06696', '01837', '03390', '02368', '02377', '02610',
       '01696', '02033', '02486', '80020', '00373', '00637', '00587', '08271', '08041', '00265', '02281', '01591',
       '03773', '01947', '00465', '02211', '00888', '01873', '02680', '03830', '00980', '00330', '01927', '01195',
       '03336', '00184', '06828', '02518', '01756', '03639', '00623', '00182', '03798', '00747', '01600', '01526',
       '06036', '00433', '01268', '01623', '01449', '00515', '01000', '00893', '01938', '02450', '01371', '02383',
       '08283', '02298', '01115', '03309', '02235', '00873', '00432', '08295', '01271', '00211', '03838', '08040',
       '02156', '08305', '02347', '00809', '01433', '01497', '02175', '06093', '01966', '01450', '03623', '08311',
       '01718', '06639', '01912', '08057', '00055', '08187', '01685', '06929', '08418', '02165', '00075', '01581',
       '08131', '00366', '00419', '01380', '00952', '00810', '01148', '01617', '00521', '00397', '00953', '03997',
       '00658', '00730', '06988', '01327', '08279', '06638', '02903', '08262', '00077', '01986', '00480', '09916',
       '01343', '00455', '06911', '03913', '00829', '00064', '00538', '00832', '06830', '02900', '02012', '01229',
       '03886', '01259', '00987', '08320', '00206', '00422', '01778', '00500', '01250', '01183', '01091', '06133',
       '00825', '01831', '01132', '00360', '02358', '00312', '01312', '00120', '02122', '00166', '06968', '00896',
       '00007', '00009', '00018', '00021', '00025', '00026', '00028', '00029', '00030', '00032', '00035', '00036',
       '00037', '00039', '00041', '00042', '00045', '00046', '00048', '00050', '00051', '00053', '00057', '00058',
       '00059', '00060', '00061', '00062', '00063', '00065', '00070', '00072', '00073', '00079', '00080', '00082',
       '00084', '00086', '00088', '00089', '00092', '00093', '00094', '00096', '00097', '00098', '00099', '00102',
       '00104', '00105', '00106', '00110', '00114', '00115', '00117', '00118', '00122', '00124', '00125', '00127',
       '00128', '00129', '00130', '00131', '00132', '00137', '00138', '00145', '00146', '00149', '00150', '00154',
       '00156', '00157', '00158', '00159', '00160', '00162', '00164', '00167', '00169', '00171', '00174', '00176',
       '00180', '00181', '00183', '00185', '00186', '00188', '00191', '00194', '00195', '00199', '00201', '00202',
       '00204', '00205', '00209', '00212', '00213', '00214', '00216', '00219', '00223', '00224', '00225', '00226',
       '00228', '00229', '00230', '00235', '00236', '00237', '00239', '00240', '00244', '00245', '00247', '00248',
       '00251', '00252', '00253', '00254', '00255', '00258', '00259', '00261', '00262', '00266', '00269', '00271',
       '00274', '00277', '00280', '00286', '00287', '00289', '00294', '00295', '00296', '00298', '00299', '00306',
       '00309', '00310', '00311', '00313', '00315', '00318', '00320', '00321', '00328', '00331', '00333', '00334',
       '00335', '00339', '00343', '00346', '00348', '00351', '00352', '00355', '00361', '00362', '00365', '00367',
       '00368', '00369', '00374', '00375', '00377', '00379', '00380', '00382', '00383', '00385', '00387', '00389',
       '00391', '00393', '00396', '00399', '00401', '00403', '00406', '00408', '00411', '00413', '00417', '00420',
       '00423', '00426', '00428', '00430', '00431', '00436', '00438', '00439', '00442', '00444', '00450', '00456',
       '00458', '00459', '00468', '00472', '00475', '00482', '00483', '00484', '00485', '00487', '00488', '00491',
       '00495', '00496', '00498', '00499', '00505', '00506', '00508', '00509', '00510', '00513', '00518', '00519',
       '00524', '00526', '00528', '00529', '00532', '00533', '00540', '00542', '00543', '00544', '00550', '00557',
       '00559', '00560', '00567', '00571', '00572', '00573', '00574', '00585', '00589', '00593', '00595', '00599',
       '00601', '00602', '00605', '00606', '00607', '00608', '00609', '00610', '00611', '00612', '00617', '00620',
       '00622', '00626', '00627', '00628', '00629', '00630', '00635', '00641', '00643', '00645', '00646', '00648',
       '00650', '00653', '00657', '00660', '00661', '00662', '00663', '00672', '00674', '00675', '00676', '00677',
       '00679', '00682', '00684', '00685', '00686', '00687', '00689', '00691', '00695', '00701', '00702', '00703',
       '00704', '00707', '00708', '00711', '00713', '00718', '00720', '00722', '00723', '00724', '00725', '00726',
       '00727', '00731', '00733', '00736', '00737', '00738', '00750', '00755', '00756', '00757', '00759', '00760',
       '00764', '00765', '00767', '00768', '00769', '00770', '00776', '00784', '00789', '00794', '00797', '00798',
       '00802', '00805', '00812', '00814', '00821', '00822', '00827', '00828', '00834', '00837', '00840', '00841',
       '00844', '00845', '00846', '00848', '00851', '00852', '00854', '00859', '00860', '00862', '00864', '00865',
       '00869', '00871', '00876', '00878', '00882', '00889', '00894', '00897', '00899', '00910', '00911', '00912',
       '00913', '00915', '00918', '00919', '00922', '00923', '00924', '00925', '00927', '00928', '00929', '00933',
       '00936', '00938', '00943', '00945', '00947', '00948', '00950', '00954', '00959', '00969', '00970', '00974',
       '00976', '00978', '00979', '00983', '00984', '00986', '00989', '00994', '00997', '01001', '01002', '01004',
       '01005', '01007', '01008', '01009', '01010', '01011', '01013', '01020', '01022', '01025', '01026', '01027',
       '01028', '01029', '01034', '01036', '01037', '01039', '01045', '01046', '01047', '01049', '01058', '01059',
       '01062', '01063', '01064', '01068', '01069', '01073', '01075', '01079', '01080', '01082', '01084', '01085',
       '01087', '01090', '01094', '01097', '01100', '01101', '01102', '01104', '01105', '01107', '01110', '01118',
       '01120', '01121', '01123', '01124', '01125', '01127', '01129', '01130', '01134', '01137', '01143', '01146',
       '01150', '01152', '01153', '01156', '01159', '01160', '01161', '01163', '01166', '01170', '01172', '01173',
       '01181', '01182', '01185', '01188', '01189', '01198', '01201', '01202', '01204', '01206', '01217', '01218',
       '01220', '01221', '01222', '01224', '01225', '01226', '01231', '01232', '01233', '01235', '01237', '01239',
       '01240', '01243', '01245', '01246', '01251', '01253', '01255', '01257', '01260', '01262', '01265', '01269',
       '01270', '01278', '01281', '01284', '01285', '01289', '01290', '01292', '01293', '01298', '01300', '01303',
       '01305', '01314', '01315', '01317', '01319', '01321', '01323', '01326', '01332', '01334', '01335', '01338',
       '01340', '01345', '01346', '01348', '01354', '01358', '01360', '01362', '01370', '01376', '01379', '01382',
       '01383', '01388', '01389', '01393', '01395', '01396', '01399', '01400', '01402', '01406', '01407', '01408',
       '01410', '01413', '01416', '01417', '01418', '01419', '01420', '01421', '01427', '01442', '01443', '01446',
       '01447', '01451', '01452', '01459', '01460', '01461', '01463', '01466', '01470', '01471', '01472', '01473',
       '01475', '01476', '01480', '01481', '01483', '01486', '01488', '01489', '01490', '01495', '01496', '01500',
       '01501', '01502', '01518', '01523', '01525', '01529', '01532', '01536', '01540', '01542', '01543', '01545',
       '01546', '01547', '01549', '01551', '01552', '01553', '01557', '01559', '01560', '01561', '01563', '01565',
       '01566', '01568', '01569', '01570', '01572', '01575', '01577', '01578', '01582', '01583', '01586', '01587',
       '01592', '01593', '01596', '01597', '01598', '01599', '01601', '01608', '01612', '01613', '01615', '01616',
       '01620', '01621', '01622', '01626', '01628', '01629', '01630', '01631', '01632', '01633', '01637', '01645',
       '01650', '01652', '01653', '01655', '01656', '01657', '01660', '01661', '01663', '01667', '01668', '01669',
       '01671', '01673', '01679', '01680', '01682', '01689', '01690', '01693', '01695', '01699', '01701', '01702',
       '01703', '01705', '01707', '01708', '01710', '01711', '01715', '01720', '01721', '01722', '01727', '01728',
       '01730', '01731', '01732', '01736', '01738', '01739', '01740', '01741', '01742', '01746', '01748', '01749',
       '01750', '01751', '01752', '01755', '01757', '01758', '01759', '01767', '01771', '01775', '01777', '01780',
       '01782', '01785', '01790', '01792', '01793', '01796', '01803', '01808', '01813', '01817', '01820', '01822',
       '01823', '01825', '01826', '01827', '01832', '01835', '01841', '01842', '01843', '01845', '01846', '01847',
       '01849', '01850', '01851', '01853', '01854', '01857', '01861', '01863', '01867', '01868', '01869', '01871',
       '01875', '01884', '01889', '01891', '01894', '01895', '01897', '01899', '01900', '01906', '01909', '01915',
       '01916', '01917', '01922', '01925', '01932', '01933', '01935', '01936', '01937', '01940', '01941', '01942',
       '01943', '01953', '01955', '01957', '01959', '01960', '01961', '01962', '01965', '01967', '01968', '01971',
       '01973', '01975', '01977', '01978', '01980', '01982', '01983', '01987', '01991', '01993', '01996', '02000',
       '02002', '02003', '02008', '02011', '02017', '02019', '02022', '02023', '02028', '02030', '02031', '02048',
       '02051', '02060', '02066', '02078', '02080', '02088', '02098', '02101', '02102', '02108', '02110', '02111',
       '02112', '02113', '02116', '02119', '02120', '02125', '02127', '02129', '02130', '02131', '02132', '02135',
       '02136', '02138', '02146', '02147', '02152', '02153', '02163', '02167', '02168', '02170', '02176', '02177',
       '02178', '02179', '02180', '02181', '02182', '02187', '02189', '02193', '02195', '02198', '02199', '02203',
       '02205', '02210', '02212', '02215', '02221', '02222', '02223', '02226', '02227', '02230', '02231', '02236',
       '02237', '02239', '02246', '02251', '02257', '02258', '02260', '02262', '02266', '02270', '02271', '02280',
       '02286', '02288', '02289', '02292', '02293', '02295', '02297', '02299', '02307', '02310', '02315', '02320',
       '02321', '02322', '02323', '02324', '02326', '02327', '02329', '02330', '02336', '02340', '02348', '02349',
       '02350', '02352', '02355', '02361', '02362', '02363', '02369', '02370', '02371', '02372', '02381', '02385',
       '02389', '02393', '02407', '02409', '02418', '02420', '02421', '02422', '02427', '02436', '02438', '02442',
       '02448', '02451', '02457', '02458', '02461', '02478', '02479', '02480', '02481', '02482', '02483', '02496',
       '02497', '02501', '02509', '02512', '02515', '02519', '02521', '02528', '02535', '02536', '02540', '02545',
       '02549', '02551', '02558', '02571', '02572', '02576', '02586', '02608', '02612', '02623', '02625', '02663',
       '02668', '02682', '02699', '02700', '02708', '02772', '02789', '02798', '02863', '02878', '02881', '02882',
       '02885', '02886', '02892', '02898', '02904', '02905', '02906', '02992', '02993', '02995', '02997', '02998',
       '03300', '03302', '03313', '03315', '03318', '03321', '03322', '03326', '03332', '03333', '03348', '03363',
       '03366', '03382', '03389', '03398', '03399', '03601', '03603', '03616', '03628', '03658', '03662', '03666',
       '03683', '03688', '03689', '03699', '03708', '03728', '03778', '03789', '03816', '03818', '03822', '03828',
       '03860', '03869', '03878', '03882', '03889', '03893', '03903', '03919', '03928', '03938', '03963', '03989',
       '03991', '03999', '04332', '04333', '04335', '04336', '04337', '04338', '04620', '04621', '04855', '06033',
       '06038', '06063', '06068', '06080', '06083', '06113', '06117', '06119', '06122', '06123', '06136', '06162',
       '06163', '06169', '06182', '06188', '06189', '06190', '06193', '06199', '06606', '06610', '06611', '06623',
       '06633', '06657', '06661', '06663', '06667', '06668', '06677', '06686', '06689', '06805', '06811', '06812',
       '06816', '06822', '06829', '06833', '06838', '06839', '06858', '06860', '06866', '06868', '06877', '06878',
       '06885', '06888', '06889', '06890', '06893', '06896', '06898', '06899', '06900', '06908', '06909', '06913',
       '06918', '06919', '06922', '06928', '06933', '06939', '06958', '06959', '06966', '06989', '06999', '07855',
       '08001', '08003', '08005', '08006', '08007', '08013', '08018', '08019', '08020', '08021', '08023', '08026',
       '08027', '08028', '08029', '08030', '08031', '08033', '08035', '08036', '08039', '08042', '08047', '08048',
       '08049', '08050', '08051', '08052', '08056', '08059', '08060', '08062', '08063', '08065', '08067', '08069',
       '08070', '08071', '08072', '08073', '08076', '08079', '08080', '08081', '08091', '08092', '08093', '08095',
       '08096', '08098', '08100', '08103', '08106', '08107', '08111', '08112', '08113', '08115', '08118', '08120',
       '08121', '08123', '08125', '08126', '08128', '08130', '08132', '08133', '08140', '08143', '08146', '08147',
       '08148', '08149', '08152', '08153', '08156', '08159', '08160', '08161', '08162', '08163', '08167', '08168',
       '08169', '08172', '08173', '08176', '08178', '08181', '08186', '08188', '08191', '08193', '08195', '08196',
       '08200', '08201', '08203', '08205', '08208', '08210', '08211', '08213', '08215', '08218', '08221', '08222',
       '08225', '08226', '08227', '08229', '08232', '08237', '08238', '08241', '08246', '08247', '08249', '08250',
       '08257', '08268', '08269', '08270', '08275', '08277', '08280', '08281', '08282', '08285', '08286', '08290',
       '08291', '08293', '08296', '08297', '08300', '08307', '08308', '08309', '08310', '08313', '08315', '08316',
       '08317', '08319', '08321', '08326', '08328', '08329', '08331', '08333', '08337', '08340', '08341', '08347',
       '08348', '08349', '08350', '08356', '08357', '08360', '08362', '08363', '08365', '08366', '08367', '08368',
       '08371', '08372', '08373', '08375', '08377', '08379', '08385', '08392', '08395', '08400', '08401', '08402',
       '08403', '08411', '08412', '08413', '08416', '08417', '08419', '08420', '08422', '08423', '08425', '08426',
       '08428', '08429', '08430', '08431', '08432', '08436', '08437', '08439', '08445', '08447', '08448', '08450',
       '08451', '08452', '08455', '08456', '08460', '08462', '08471', '08472', '08475', '08476', '08480', '08481',
       '08482', '08487', '08489', '08490', '08491', '08493', '08495', '08496', '08500', '08501', '08502', '08507',
       '08509', '08510', '08511', '08512', '08513', '08516', '08519', '08521', '08523', '08525', '08526', '08527',
       '08529', '08532', '08535', '08536', '08537', '08545', '08547', '08561', '08590', '08601', '08606', '08611',
       '08612', '08613', '08616', '08619', '08620', '08621', '08622', '08623', '08627', '08631', '08635', '08637',
       '08646', '08657', '08659', '08668', '09600', '09608', '09616', '09638', '09663', '09677', '09680', '09686',
       '09689', '09857', '09882', '09886', '09893', '09906', '09908', '09909', '09913', '09918', '09929', '09938',
       '09960', '09963', '09978', '09982', '09983', '09990', '09991', '09998', '80016', '80291', '80737', '80883',
       '82020', '82331', '82333', '86618', '89618')

select fid
from hkex_file_meta
where doc_type = 1
  and published > '2025-07-01T16:41:00';


WITH "cte" AS (SELECT "t1"."fid",
                      ROW_NUMBER() OVER (PARTITION BY "t1"."report_year" ORDER BY "t1"."published" DESC) AS "rank"
               FROM "hkex_file_meta" AS "t1"
               WHERE (("t1"."deleted_utc" = 0) AND
                      ((("t1"."doc_type" = 1) AND ("t1"."stock_code" = '00005')) AND ("t1"."report_year" = '2024'))))
SELECT "t2"."fid",
       "t3"."id"           AS "qid",
       "t4"."mold"         AS "mold_id",
       "t2"."stock_code",
       "t2"."name"         AS "company_name",
       "t5"."name"         AS "title",
       "t5"."headline",
       "t5"."url"          AS "hkexurl",
       "t5"."release_time" AS "release_date",
       "t2"."doc_type",
       "t2"."stat_res",
       "t2"."report_year",
       "t2"."year_end",
       "t6"."team_id"      AS "team",
       "t3"."ar_status"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t5" ON ("t2"."fid" = "t5"."fid")
         INNER JOIN "file" AS "t4" ON ("t4"."id" = "t2"."fid")
         INNER JOIN "hkex_companies_info" AS "t6" ON ("t2"."stock_code" = "t6"."stock_code")
         INNER JOIN "question" AS "t3" ON (("t2"."fid" = "t3"."fid") AND ("t3"."mold" = 5))
         INNER JOIN "cte" ON ("cte"."fid" = "t2"."fid")
WHERE ((((("t2"."deleted_utc" = 0) AND ("t5"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND
        ("t3"."deleted_utc" = 0)) AND ("cte"."rank" = 1))
ORDER BY CAST("t2"."report_year" AS integer) DESC;


SELECT df.url,
       fm.stock_code                                                  as stock_code,
       fm.fid                                                         as fid,
       concat('https://jura6-lir.paodingai.com/#/hkex/agm-poll-results/report-review/', fm.qid, '?fileId=', fm.fid,
              '&schemaId=34&rule=General%20Mandate%20Limit&delist=0') as url,
       pm.agm_fid                                                     as agm_fid,
       pm.mr_fid                                                      as mr_fid
FROM hkex_file_meta fm
         join poll_meta pm on fm.fid = pm.poll_fid
         join hkex_file hf on fm.fid = hf.fid
where release_time >= 1719763200
  and release_time <= 1753977600;


SELECT count(*)
FROM hkex_file_meta fm
         join poll_meta pm on fm.fid = pm.poll_fid
         join hkex_file hf on fm.fid = hf.fid
where release_time >= 1719763200
  and release_time <= 1753977600;


\copy (WITH "temp_query" AS (SELECT * FROM (((WITH "temp_q" AS (SELECT "t1"."id", "t1"."fid", "t1"."mold", "t1"."deleted_utc", "t1"."updated_utc", ROW_NUMBER() OVER w AS "rank" FROM "question" AS "t1" WHERE (("t1"."mold" IN (24, 26)) AND ("t1"."deleted_utc" = 0)) WINDOW w AS (PARTITION BY "t1"."fid" ORDER BY "t1"."updated_utc" DESC)) SELECT "t2"."fid", "t2"."qid", "temp_q"."updated_utc", "temp_q"."mold" AS "mold_id", "t2"."stock_code", "t2"."stat_res", "t3"."headline", "t3"."url" AS "hkexurl", "t3"."release_time" AS "release_date", "t2"."doc_type" FROM "hkex_file_meta" AS "t2" INNER JOIN "hkex_file" AS "t3" ON (("t2"."fid" = "t3"."fid") AND ("t3"."type" IN ('Q1', 'Interim', 'Q3', 'Final'))) INNER JOIN "file" AS "t4" ON ("t4"."id" = "t2"."fid") INNER JOIN "temp_q" ON (("t2"."fid" = "temp_q"."fid") AND ("temp_q"."rank" = 1)) INNER JOIN "hkex_companies_info" AS "t5" ON ("t2"."stock_code" = "t5"."stock_code") WHERE ((((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND ("t2"."doc_type" IN (11, 12, 13, 14))) ORDER BY "temp_q"."updated_utc" DESC) UNION ALL (WITH "temp_q" AS (SELECT "t6"."id", "t6"."fid", "t6"."mold", "t6"."deleted_utc", "t6"."updated_utc" FROM "question" AS "t6" WHERE (("t6"."mold" IN (2, 1)) AND ("t6"."deleted_utc" = 0))) SELECT "t7"."fid", "temp_q"."id" AS "qid", "temp_q"."updated_utc", "temp_q"."mold" AS "mold_id", "t7"."stock_code", "t7"."stat_res", "t8"."headline", "t8"."url" AS "hkexurl", "t8"."release_time" AS "release_date", "t7"."doc_type" FROM "hkex_file_meta" AS "t7" INNER JOIN "hkex_file" AS "t8" ON (("t7"."fid" = "t8"."fid") AND ("t8"."type" IN ('ESG', 'ar'))) INNER JOIN "file" AS "t9" ON ("t9"."id" = "t7"."fid") INNER JOIN "file_esg_xref" AS "t10" ON ("t9"."id" = "t10"."fid") INNER JOIN "temp_q" ON ("t7"."fid" = "temp_q"."fid") INNER JOIN "hkex_companies_info" AS "t11" ON ("t7"."stock_code" = "t11"."stock_code") WHERE (((((("t7"."deleted_utc" = 0) AND ("t8"."deleted_utc" = 0)) AND ("t9"."deleted_utc" = 0)) AND ("t9"."deleted_utc" = 0)) AND "t10"."activated") AND ("t7"."doc_type" IN (1, 2))) ORDER BY "temp_q"."updated_utc" DESC)) UNION ALL (WITH "temp_q" AS (SELECT "t12"."id", "t12"."fid", "t12"."mold", "t12"."deleted_utc", "t12"."updated_utc", ROW_NUMBER() OVER w AS "rank" FROM "question" AS "t12" WHERE (("t12"."mold" IN (5, 15, 18, 22, 33, 34)) AND ("t12"."deleted_utc" = 0)) WINDOW w AS (PARTITION BY "t12"."fid" ORDER BY "t12"."updated_utc" DESC)) SELECT "t7"."fid", "temp_q"."id" AS "qid", "temp_q"."updated_utc", "temp_q"."mold" AS "mold_id", "t7"."stock_code", "t7"."stat_res", "t8"."headline", "t8"."url" AS "hkexurl", "t8"."release_time" AS "release_date", "t7"."doc_type" FROM "hkex_file_meta" AS "t7" INNER JOIN "hkex_file" AS "t8" ON (("t7"."fid" = "t8"."fid") AND ("t8"."type" IN ('ar', 'AGM', 'POLL'))) INNER JOIN "file" AS "t9" ON ("t9"."id" = "t7"."fid") INNER JOIN "temp_q" ON (("t7"."fid" = "temp_q"."fid") AND ("temp_q"."rank" = 1)) INNER JOIN "hkex_companies_info" AS "t11" ON ("t7"."stock_code" = "t11"."stock_code") WHERE ((((("t7"."deleted_utc" = 0) AND ("t8"."deleted_utc" = 0)) AND ("t9"."deleted_utc" = 0)) AND ("t9"."deleted_utc" = 0)) AND ("t7"."doc_type" IN (1, 30, 31))) ORDER BY "temp_q"."updated_utc" DESC)) AS "t13") ((WITH "temp_q" AS (SELECT "t14"."id", "t14"."fid", "t14"."mold", "t14"."deleted_utc", "t14"."updated_utc", ROW_NUMBER() OVER w AS "rank" FROM "question" AS "t14" WHERE (("t14"."mold" IN (24, 26)) AND ("t14"."deleted_utc" = 0)) WINDOW w AS (PARTITION BY "t14"."fid" ORDER BY "t14"."updated_utc" DESC)) SELECT "t15"."fid", "t15"."qid", "temp_q"."updated_utc", "temp_q"."mold" AS "mold_id", "t15"."stock_code", "t15"."stat_res", "t16"."headline", "t16"."url" AS "hkexurl", "t16"."release_time" AS "release_date", "t15"."doc_type" FROM "hkex_file_meta" AS "t15" INNER JOIN "hkex_file" AS "t16" ON (("t15"."fid" = "t16"."fid") AND ("t16"."type" IN ('Q1', 'Interim', 'Q3', 'Final'))) INNER JOIN "file" AS "t17" ON ("t17"."id" = "t15"."fid") INNER JOIN "temp_q" ON (("t15"."fid" = "temp_q"."fid") AND ("temp_q"."rank" = 1)) INNER JOIN "hkex_companies_info" AS "t18" ON ("t15"."stock_code" = "t18"."stock_code") WHERE ((((("t15"."deleted_utc" = 0) AND ("t16"."deleted_utc" = 0)) AND ("t17"."deleted_utc" = 0)) AND ("t17"."deleted_utc" = 0)) AND ("t15"."doc_type" IN (11, 12, 13, 14))) ORDER BY "temp_q"."updated_utc" DESC) UNION ALL (WITH "temp_q" AS (SELECT "t19"."id", "t19"."fid", "t19"."mold", "t19"."deleted_utc", "t19"."updated_utc" FROM "question" AS "t19" WHERE (("t19"."mold" IN (2, 1)) AND ("t19"."deleted_utc" = 0))) SELECT "t2"."fid", "temp_q"."id" AS "qid", "temp_q"."updated_utc", "temp_q"."mold" AS "mold_id", "t2"."stock_code", "t2"."stat_res", "t3"."headline", "t3"."url" AS "hkexurl", "t3"."release_time" AS "release_date", "t2"."doc_type" FROM "hkex_file_meta" AS "t2" INNER JOIN "hkex_file" AS "t3" ON (("t2"."fid" = "t3"."fid") AND ("t3"."type" IN ('ESG', 'ar'))) INNER JOIN "file" AS "t4" ON ("t4"."id" = "t2"."fid") INNER JOIN "file_esg_xref" AS "t20" ON ("t4"."id" = "t20"."fid") INNER JOIN "temp_q" ON ("t2"."fid" = "temp_q"."fid") INNER JOIN "hkex_companies_info" AS "t5" ON ("t2"."stock_code" = "t5"."stock_code") WHERE (((((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND "t20"."activated") AND ("t2"."doc_type" IN (1, 2))) ORDER BY "temp_q"."updated_utc" DESC)) UNION ALL (WITH "temp_q" AS (SELECT "t21"."id", "t21"."fid", "t21"."mold", "t21"."deleted_utc", "t21"."updated_utc", ROW_NUMBER() OVER w AS "rank" FROM "question" AS "t21" WHERE (("t21"."mold" IN (5, 15, 18, 22, 33, 34)) AND ("t21"."deleted_utc" = 0)) WINDOW w AS (PARTITION BY "t21"."fid" ORDER BY "t21"."updated_utc" DESC)) SELECT "t2"."fid", "temp_q"."id" AS "qid", "temp_q"."updated_utc", "temp_q"."mold" AS "mold_id", "t2"."stock_code", "t2"."stat_res", "t3"."headline", "t3"."url" AS "hkexurl", "t3"."release_time" AS "release_date", "t2"."doc_type" FROM "hkex_file_meta" AS "t2" INNER JOIN "hkex_file" AS "t3" ON (("t2"."fid" = "t3"."fid") AND ("t3"."type" IN ('ar', 'AGM', 'POLL'))) INNER JOIN "file" AS "t4" ON ("t4"."id" = "t2"."fid") INNER JOIN "temp_q" ON (("t2"."fid" = "temp_q"."fid") AND ("temp_q"."rank" = 1)) INNER JOIN "hkex_companies_info" AS "t5" ON ("t2"."stock_code" = "t5"."stock_code") WHERE ((((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND ("t2"."doc_type" IN (1, 30, 31))) ORDER BY "temp_q"."updated_utc" DESC) ORDER BY "updated_utc" DESC) to "/tmp/avt_document.csv" with (format csv, header)

WITH "temp_q" AS (SELECT "t1"."id", "t1"."fid", "t1"."mold", "t1"."deleted_utc", "t1"."updated_utc"
                  FROM "question" AS "t1"
                  WHERE (("t1"."mold" IN (2, 1)) AND ("t1"."deleted_utc" = 0)))
SELECT "t2"."fid",
       "temp_q"."id"       AS "qid",
       "temp_q"."updated_utc",
       "temp_q"."mold"     AS "mold_id",
       "t2"."stock_code",
       "t2"."stat_res",
       "t3"."headline",
       "t3"."url"          AS "hkexurl",
       "t3"."release_time" AS "release_date",
       "t2"."doc_type"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t3"
                    ON (("t2"."fid" = "t3"."fid") AND ("t3"."type" IN ('Q1', 'Interim', 'Q3', 'Final', 'ar')))
         INNER JOIN "file" AS "t4" ON ("t4"."id" = "t2"."fid")
         INNER JOIN "file_esg_xref" AS "t5" ON ("t4"."id" = "t5"."fid")
         INNER JOIN "temp_q" ON ("t2"."fid" = "temp_q"."fid")
         INNER JOIN "hkex_companies_info" AS "t6" ON ("t2"."stock_code" = "t6"."stock_code")
WHERE (((((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND
         ("t4"."deleted_utc" = 0)) AND "t5"."activated") AND ("t2"."doc_type" IN (1, 2)))
ORDER BY "temp_q"."updated_utc" DESC


select count(*)
from hkex_File hf
         join file_esg_xref on hf.fid = file_esg_xref.fid
         join hkex_file_meta hm on hf.fid = hm.fid
where hf.type in ('ESG', 'ar')
  and hm.report_year >= '2020'
  and file_esg_xref.activated


select count(*)
from hkex_File hf
         join hkex_file_meta hm on hf.fid = hm.fid
where hf.type in ('ar')
  and hm.report_year >= '2020'


select count(*)
from hkex_File hf
         join hkex_file_meta hm on hf.fid = hm.fid
where hf.type in ('Q1')
  and hm.report_year >= '2020'


update rule_reference
set gem_description= 'To assist general mandate limit checking in LARA regarding fundraising under general mandate.
MB Rule 13.29/ GEM Rule 17.30A: Where the securities are issued for cash under the authority of a general mandate granted to the directors by the shareholders in accordance with MB Rule 13.36(2)(b)/ GEM Rule 17.41(2).'
where id = 607;

WITH "temp_q" AS (SELECT "t1"."id", "t1"."fid", "t1"."mold", "t1"."deleted_utc", "t1"."updated_utc"
                  FROM "question" AS "t1"
                  WHERE (("t1"."mold" IN (2, 1)) AND ("t1"."deleted_utc" = 0)))
SELECT distinct "temp_q"."mold"
FROM "hkex_file_meta" AS "t2"
         INNER JOIN "hkex_file" AS "t3" ON (("t2"."fid" = "t3"."fid") AND ("t3"."type" IN ('ESG', 'ar')))
         INNER JOIN "file" AS "t4" ON ("t4"."id" = "t2"."fid")
         INNER JOIN "file_esg_xref" AS "t5" ON ("t4"."id" = "t5"."fid")
         INNER JOIN "temp_q" ON ("t2"."fid" = "temp_q"."fid")
         INNER JOIN "hkex_companies_info" AS "t6" ON ("t2"."stock_code" = "t6"."stock_code")
WHERE (((((("t2"."deleted_utc" = 0) AND ("t3"."deleted_utc" = 0)) AND ("t4"."deleted_utc" = 0)) AND
         ("t4"."deleted_utc" = 0)) AND "t5"."activated") AND ("t2"."doc_type" IN (1, 2)))
ORDER BY "temp_q"."updated_utc" DESC


select hm.fid
from hkex_file_meta hm
         join file f on hm.fid = f.id
where f.deleted_utc = 0
  and f.updated_utc < '1754560800'
  and hm.doc_type in (11, 12, 13, 14)
  and report_year = '2024'

SELECT distinct fid
FROM disclosure_result
WHERE jsonb_array_length(answer_set) >= 2
  AND answer_set ->> (jsonb_array_length(answer_set) - 2) = ''
  AND fid = 90117;

SELECT hf.url,
       fm.stock_code                                                  as stock_code,
       fm.fid                                                         as fid,
       concat('https://jura6-lir.paodingai.com/#/hkex/agm-poll-results/report-review/', fm.qid, '?fileId=', fm.fid,
              '&schemaId=34&rule=General%20Mandate%20Limit&delist=0') as url,
       pm.agm_fid                                                     as agm_fid,
       pm.mr_fid                                                      as mr_fid,
       hf.release_time
FROM hkex_file_meta fm
         join poll_meta pm on fm.fid = pm.poll_fid
         join hkex_file hf on fm.fid = hf.fid
where release_time >= 1753977600
  and release_time <= 1755187200


WITH "tmp_ans" AS (SELECT "t1"."id",
                          "t2"."fid"                                                                             AS "fid",
                          "t1"."admin_user_id",
                          ROW_NUMBER()
                          OVER (PARTITION BY "t1"."rule", "t1"."question_id" ORDER BY "t1"."admin_user_id" DESC) AS "rank"
                   FROM "cg_result" AS "t1"
                            INNER JOIN "question" AS "t2" ON ("t2"."id" = "t1"."question_id")
                            INNER JOIN "hkex_file_meta" AS "t3" ON (("t2"."fid" = "t3"."fid") AND ("t2"."mold" = 28))
                   WHERE (("t2"."deleted_utc" = 0) AND
                          ((((("t1"."admin_user_id" >= 0) AND ("t1"."admin_user_id" != 1)) AND
                             ("t3"."stock_code" = '00001')) AND ("t3"."doc_type" = 1)) AND
                           ((CAST("t3"."report_year" AS int) >= 2023) AND (CAST("t3"."report_year" AS int) <= 2025)))))
SELECT "t4"."main_alias", "t5"."report_year", "t6"."enum_list","t6"."admin_user_id"
FROM "cg_result" AS "t6"
         INNER JOIN "question" AS "t7" ON ("t7"."id" = "t6"."question_id")
         INNER JOIN "hkex_file_meta" AS "t5" ON ("t7"."fid" = "t5"."fid")
         INNER JOIN "tmp_ans" ON ("tmp_ans"."id" = "t6"."id")
         INNER JOIN "rule_reference" AS "t4" ON ("t4"."rule" = "t6"."rule")
WHERE ((((((("t7"."deleted_utc" = 0) AND ("t4"."deleted_utc" = 0)) AND ("t5"."stock_code" = '00001')) AND
          ("t5"."doc_type" = 1)) AND
         ((CAST("t5"."report_year" AS int) >= 2023) AND (CAST("t5"."report_year" AS int) <= 2025))) AND
        ("tmp_ans"."rank" = 1)) AND ("t6"."enum_list" IS NOT NULL));



UPDATE cg_result SET enum_list = '{1}' where admin_user_id=366 and rule = 'A(b)-Compliance with CPs' and question_id =380879;